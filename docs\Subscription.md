# Subscription Plan (recurring billing monthly/annually)
1. Free
- 10 minutes per day
- Access to all platforms (Windows, MacOS, iOS, Android)
- Maximum device limit: 1

2. Starter ($9.99/mo) ($99/yr)
- 1 hour per day
- Access to all platforms (Windows, MacOS, iOS, Android)
- Maximum device limit: 1

3. Pro ($19.99/mo) ($199/yr)
- 3 hours per day
- Access to all platforms (Windows, MacOS, iOS, Android)
- Maximum device limit: 2

4. Premium ($59.99/mo) ($559/yr)
- 8 hours per day
- Access to all platforms (Windows, MacOS, iOS, Android)
- Maximum device limit: 5

5. Max ($129.99/mo) ($1099/yr)
- Unlimited hours per month
- Access to all platforms (Windows, MacOS, iOS, Android)
- Unlimited device limit

# Remarks for backend team, not for end-customer
- will need a schedule job to reset daily usage limit for all users at 00:00 UTC at their region time every day

# Create a script to generate products and prices in Stripe
# Run the script
node scripts/setup-stripe.js