"""
全面的訂閱場景測試
使用 Firebase Admin SDK 直接驗證數據庫狀態
"""

import unittest
import sys
import os
import json
import time
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend-api'))

# Firebase Admin SDK 初始化
try:
    import firebase_admin
    from firebase_admin import credentials, firestore
    
    # 使用服務帳戶金鑰初始化
    cred_path = os.path.join(os.path.dirname(__file__), '..', 'speakoneai-dev-9f995-firebase-adminsdk-fbsvc-5f34f67ccf.json')
    if os.path.exists(cred_path):
        cred = credentials.Certificate(cred_path)
        if not firebase_admin._apps:
            firebase_admin.initialize_app(cred)
        db = firestore.client()
        print("✅ Firebase Admin SDK 初始化成功")
    else:
        print("❌ 找不到 Firebase 服務帳戶金鑰")
        db = None
except ImportError:
    print("❌ Firebase Admin SDK 未安裝")
    db = None

class SubscriptionTestFramework:
    """訂閱測試框架"""
    
    def __init__(self, db):
        self.db = db
        self.test_users = []
        self.timestamp = str(int(time.time()))
        
    def create_test_user(self, user_id: str, initial_plan: str = "FREE") -> Dict[str, Any]:
        """創建測試用戶"""
        user_data = {
            'email': f'{user_id}@test.com',
            'display_name': f'Test User {user_id}',
            'current_subscription_id': f'sub_{user_id}',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        
        # 創建用戶記錄
        self.db.collection('users').document(user_id).set(user_data)
        
        # 創建訂閱記錄
        subscription_data = self.get_plan_config(initial_plan, user_id)
        self.db.collection('subscriptions').document(f'sub_{user_id}').set(subscription_data)
        
        self.test_users.append(user_id)
        return user_data
    
    def get_plan_config(self, plan: str, user_id: str) -> Dict[str, Any]:
        """獲取計劃配置"""
        plan_configs = {
            "FREE": {
                "daily_limit_seconds": 600,  # 10 minutes
                "max_devices": 1
            },
            "STARTER": {
                "daily_limit_seconds": 3600,  # 1 hour
                "max_devices": 1
            },
            "PRO": {
                "daily_limit_seconds": 10800,  # 3 hours
                "max_devices": 2
            },
            "PREMIUM": {
                "daily_limit_seconds": 28800,  # 8 hours
                "max_devices": 5
            },
            "MAX": {
                "daily_limit_seconds": -1,  # Unlimited
                "max_devices": -1
            }
        }
        
        config = plan_configs.get(plan, plan_configs["FREE"])
        now = datetime.utcnow()
        
        return {
            'user_id': user_id,
            'plan': plan,
            'status': 'active',
            'daily_limit_seconds': config['daily_limit_seconds'],
            'max_devices': config['max_devices'],
            'current_day_used_seconds': 0,
            'last_daily_reset_date': now.strftime('%Y-%m-%d'),
            'last_daily_reset_at': now,
            'register_region_timezone': 'UTC+8',
            'usage_reset_hour': 0,
            'active_device_ids': [],
            'created_at': now,
            'updated_at': now
        }
    
    def simulate_subscription_change(self, user_id: str, new_plan: str, change_type: str = "upgrade"):
        """模擬訂閱變更"""
        subscription_ref = self.db.collection('subscriptions').document(f'sub_{user_id}')
        subscription_doc = subscription_ref.get()
        
        if not subscription_doc.exists:
            raise ValueError(f"用戶 {user_id} 的訂閱不存在")
        
        old_data = subscription_doc.to_dict()
        old_plan = old_data['plan']
        
        # 獲取新計劃配置
        new_config = self.get_plan_config(new_plan, user_id)
        
        # 更新訂閱記錄
        update_data = {
            'plan': new_plan,
            'daily_limit_seconds': new_config['daily_limit_seconds'],
            'max_devices': new_config['max_devices'],
            'updated_at': datetime.utcnow()
        }
        
        # 升級時立即重置配額
        if change_type == "upgrade":
            update_data['current_day_used_seconds'] = 0
            update_data['last_daily_reset_date'] = datetime.utcnow().strftime('%Y-%m-%d')
            update_data['last_daily_reset_at'] = datetime.utcnow()

        # 取消時設置狀態為 canceled
        if change_type == "cancel":
            update_data['status'] = 'canceled'
        
        subscription_ref.update(update_data)
        
        # 更新用戶記錄
        user_ref = self.db.collection('users').document(user_id)
        user_ref.update({
            'updated_at': datetime.utcnow()
        })
        
        return {
            'old_plan': old_plan,
            'new_plan': new_plan,
            'change_type': change_type,
            'old_data': old_data,
            'new_data': update_data
        }
    
    def add_device(self, user_id: str, device_id: str) -> bool:
        """添加設備"""
        subscription_ref = self.db.collection('subscriptions').document(f'sub_{user_id}')
        subscription_doc = subscription_ref.get()
        
        if not subscription_doc.exists:
            return False
        
        subscription_data = subscription_doc.to_dict()
        active_devices = subscription_data.get('active_device_ids', [])
        max_devices = subscription_data.get('max_devices', 1)
        
        # 檢查設備限制
        if max_devices != -1 and len(active_devices) >= max_devices:
            return False
        
        # 添加設備
        if device_id not in active_devices:
            active_devices.append(device_id)
            subscription_ref.update({
                'active_device_ids': active_devices,
                'updated_at': datetime.utcnow()
            })
        
        return True
    
    def get_subscription_data(self, user_id: str) -> Optional[Dict[str, Any]]:
        """獲取訂閱數據"""
        subscription_ref = self.db.collection('subscriptions').document(f'sub_{user_id}')
        subscription_doc = subscription_ref.get()

        if subscription_doc.exists:
            return subscription_doc.to_dict()
        return None

    def get_user_data(self, user_id: str) -> Optional[Dict[str, Any]]:
        """獲取用戶數據"""
        user_ref = self.db.collection('users').document(user_id)
        user_doc = user_ref.get()

        if user_doc.exists:
            return user_doc.to_dict()
        return None

    def print_detailed_data(self, title: str, user_id: str, subscription_data: Dict[str, Any], user_data: Dict[str, Any] = None):
        """打印詳細的數據庫數據"""
        print(f"\n    📊 {title}")
        print(f"    {'='*50}")
        print(f"    用戶 ID: {user_id}")

        # 訂閱數據
        if subscription_data:
            print(f"    📋 訂閱數據 (subscriptions 表):")
            print(f"      - 計劃: {subscription_data.get('plan', 'N/A')}")
            print(f"      - 狀態: {subscription_data.get('status', 'N/A')}")
            print(f"      - 每日配額: {subscription_data.get('daily_limit_seconds', 'N/A')}s")
            print(f"      - 設備限制: {subscription_data.get('max_devices', 'N/A')}")
            print(f"      - 當日已用: {subscription_data.get('current_day_used_seconds', 'N/A')}s")
            print(f"      - 活躍設備: {len(subscription_data.get('active_device_ids', []))} 個")
            print(f"      - 設備列表: {subscription_data.get('active_device_ids', [])}")
            print(f"      - Stripe 客戶 ID: {subscription_data.get('stripe_customer_id', 'N/A')}")
            print(f"      - Stripe 訂閱 ID: {subscription_data.get('stripe_subscription_id', 'N/A')}")
            print(f"      - 計費週期: {subscription_data.get('billing_cycle', 'N/A')}")
            print(f"      - 自動續費: {subscription_data.get('auto_renew', 'N/A')}")
            print(f"      - 最後重置日期: {subscription_data.get('last_daily_reset_date', 'N/A')}")
            print(f"      - 時區: {subscription_data.get('register_region_timezone', 'N/A')}")
            print(f"      - 創建時間: {subscription_data.get('created_at', 'N/A')}")
            print(f"      - 更新時間: {subscription_data.get('updated_at', 'N/A')}")

        # 用戶數據
        if user_data:
            print(f"    👤 用戶數據 (users 表):")
            print(f"      - 郵箱: {user_data.get('email', 'N/A')}")
            print(f"      - 顯示名稱: {user_data.get('display_name', 'N/A')}")
            print(f"      - 當前訂閱 ID: {user_data.get('current_subscription_id', 'N/A')}")
            print(f"      - Stripe 客戶 ID: {user_data.get('stripe_customer_id', 'N/A')}")
            print(f"      - 創建時間: {user_data.get('created_at', 'N/A')}")
            print(f"      - 更新時間: {user_data.get('updated_at', 'N/A')}")

    def print_data_comparison(self, title: str, user_id: str, old_data: Dict[str, Any], new_data: Dict[str, Any]):
        """打印數據變更對比"""
        print(f"\n    🔄 {title}")
        print(f"    {'='*60}")
        print(f"    用戶 ID: {user_id}")

        # 比較關鍵欄位
        key_fields = [
            ('plan', '計劃'),
            ('status', '狀態'),
            ('daily_limit_seconds', '每日配額(秒)'),
            ('max_devices', '設備限制'),
            ('current_day_used_seconds', '當日已用(秒)'),
            ('stripe_subscription_id', 'Stripe 訂閱 ID'),
            ('billing_cycle', '計費週期'),
            ('auto_renew', '自動續費')
        ]

        print(f"    {'欄位':<20} {'變更前':<20} {'變更後':<20} {'狀態'}")
        print(f"    {'-'*70}")

        for field, display_name in key_fields:
            old_value = old_data.get(field, 'N/A')
            new_value = new_data.get(field, 'N/A')

            if old_value != new_value:
                status = "🔄 已變更"
            else:
                status = "✅ 無變更"

            print(f"    {display_name:<20} {str(old_value):<20} {str(new_value):<20} {status}")

        # 設備列表比較
        old_devices = old_data.get('active_device_ids', [])
        new_devices = new_data.get('active_device_ids', [])

        if old_devices != new_devices:
            print(f"\n    📱 設備列表變更:")
            print(f"      變更前: {old_devices} ({len(old_devices)} 個)")
            print(f"      變更後: {new_devices} ({len(new_devices)} 個)")

            added_devices = set(new_devices) - set(old_devices)
            removed_devices = set(old_devices) - set(new_devices)

            if added_devices:
                print(f"      ➕ 新增設備: {list(added_devices)}")
            if removed_devices:
                print(f"      ➖ 移除設備: {list(removed_devices)}")
        else:
            print(f"\n    📱 設備列表: 無變更 ({len(old_devices)} 個設備)")
    
    def cleanup_test_data(self):
        """清理測試數據"""
        for user_id in self.test_users:
            # 刪除用戶記錄
            self.db.collection('users').document(user_id).delete()
            # 刪除訂閱記錄
            self.db.collection('subscriptions').document(f'sub_{user_id}').delete()
        
        self.test_users.clear()

class TestSubscriptionScenarios(unittest.TestCase):
    """訂閱場景測試"""
    
    @classmethod
    def setUpClass(cls):
        """測試類設置"""
        if db is None:
            cls.skipTest(cls, "Firebase Admin SDK 未可用")
        cls.framework = SubscriptionTestFramework(db)
    
    @classmethod
    def tearDownClass(cls):
        """測試類清理"""
        if hasattr(cls, 'framework'):
            cls.framework.cleanup_test_data()
    
    def setUp(self):
        """每個測試的設置"""
        self.timestamp = str(int(time.time()))
    
    def test_01_upgrade_scenarios(self):
        """測試升級場景"""
        print("\n🔼 測試升級場景")
        print("=" * 50)
        
        upgrade_scenarios = [
            ("FREE", "STARTER"),
            ("FREE", "PRO"),
            ("FREE", "PREMIUM"),
            ("FREE", "MAX"),
            ("STARTER", "PRO"),
            ("STARTER", "PREMIUM"),
            ("STARTER", "MAX"),
            ("PRO", "PREMIUM"),
            ("PRO", "MAX"),
            ("PREMIUM", "MAX")
        ]
        
        for old_plan, new_plan in upgrade_scenarios:
            with self.subTest(upgrade=f"{old_plan}_to_{new_plan}"):
                user_id = f"upgrade_test_{old_plan}_{new_plan}_{self.timestamp}"

                print(f"\n  🔼 測試升級: {old_plan} → {new_plan}")
                print(f"  {'='*50}")

                # 創建測試用戶
                self.framework.create_test_user(user_id, old_plan)

                # 獲取升級前數據
                old_subscription = self.framework.get_subscription_data(user_id)
                old_user = self.framework.get_user_data(user_id)

                # 打印升級前數據
                self.framework.print_detailed_data(f"升級前數據 ({old_plan})", user_id, old_subscription, old_user)

                # 執行升級
                self.framework.simulate_subscription_change(user_id, new_plan, "upgrade")

                # 獲取升級後數據
                new_subscription = self.framework.get_subscription_data(user_id)
                new_user = self.framework.get_user_data(user_id)

                # 打印升級後數據
                self.framework.print_detailed_data(f"升級後數據 ({new_plan})", user_id, new_subscription, new_user)

                # 打印數據對比
                self.framework.print_data_comparison(f"升級變更對比: {old_plan} → {new_plan}", user_id, old_subscription, new_subscription)

                # 驗證計劃變更
                self.assertEqual(new_subscription['plan'], new_plan, f"計劃應該從 {old_plan} 變更為 {new_plan}")

                # 驗證配額增加
                old_limit = old_subscription['daily_limit_seconds']
                new_limit = new_subscription['daily_limit_seconds']
                if new_limit != -1 and old_limit != -1:
                    self.assertGreater(new_limit, old_limit, f"配額應該從 {old_limit}s 增加到 {new_limit}s")

                # 驗證設備限制增加
                old_devices = old_subscription['max_devices']
                new_devices = new_subscription['max_devices']
                if new_devices != -1 and old_devices != -1:
                    self.assertGreaterEqual(new_devices, old_devices, f"設備限制應該從 {old_devices} 增加到 {new_devices}")

                # 驗證配額重置
                self.assertEqual(new_subscription['current_day_used_seconds'], 0, "升級時應該重置當日使用量")

                print(f"\n  ✅ 升級驗證通過: {old_plan} → {new_plan}")
                print(f"    - 計劃變更: {old_plan} → {new_plan}")
                print(f"    - 配額變更: {old_limit}s → {new_limit}s")
                print(f"    - 設備限制: {old_devices} → {new_devices}")
                print(f"    - 使用量重置: {old_subscription['current_day_used_seconds']}s → 0s")
    
    def test_02_downgrade_scenarios(self):
        """測試降級場景"""
        print("\n🔽 測試降級場景")
        print("=" * 50)
        
        downgrade_scenarios = [
            ("MAX", "PREMIUM"),
            ("MAX", "PRO"),
            ("MAX", "STARTER"),
            ("PREMIUM", "PRO"),
            ("PREMIUM", "STARTER"),
            ("PRO", "STARTER")
        ]
        
        for old_plan, new_plan in downgrade_scenarios:
            with self.subTest(downgrade=f"{old_plan}_to_{new_plan}"):
                user_id = f"downgrade_test_{old_plan}_{new_plan}_{self.timestamp}"

                print(f"\n  🔽 測試降級: {old_plan} → {new_plan}")
                print(f"  {'='*50}")

                # 創建測試用戶
                self.framework.create_test_user(user_id, old_plan)

                # 添加一些使用量
                subscription_ref = db.collection('subscriptions').document(f'sub_{user_id}')
                subscription_ref.update({'current_day_used_seconds': 1800})  # 30 分鐘

                # 獲取降級前數據
                old_subscription = self.framework.get_subscription_data(user_id)
                old_user = self.framework.get_user_data(user_id)

                # 打印降級前數據
                self.framework.print_detailed_data(f"降級前數據 ({old_plan})", user_id, old_subscription, old_user)

                # 執行降級
                self.framework.simulate_subscription_change(user_id, new_plan, "downgrade")

                # 獲取降級後數據
                new_subscription = self.framework.get_subscription_data(user_id)
                new_user = self.framework.get_user_data(user_id)

                # 打印降級後數據
                self.framework.print_detailed_data(f"降級後數據 ({new_plan})", user_id, new_subscription, new_user)

                # 打印數據對比
                self.framework.print_data_comparison(f"降級變更對比: {old_plan} → {new_plan}", user_id, old_subscription, new_subscription)

                # 驗證計劃變更
                self.assertEqual(new_subscription['plan'], new_plan, f"計劃應該從 {old_plan} 變更為 {new_plan}")

                # 驗證配額減少
                old_limit = old_subscription['daily_limit_seconds']
                new_limit = new_subscription['daily_limit_seconds']
                if old_limit != -1:
                    self.assertLess(new_limit, old_limit, f"配額應該從 {old_limit}s 減少到 {new_limit}s")

                # 驗證設備限制減少
                old_devices = old_subscription['max_devices']
                new_devices = new_subscription['max_devices']
                if old_devices != -1:
                    self.assertLessEqual(new_devices, old_devices, f"設備限制應該從 {old_devices} 減少到 {new_devices}")

                # 驗證使用量保持（降級不立即重置）
                old_used = old_subscription['current_day_used_seconds']
                new_used = new_subscription['current_day_used_seconds']
                self.assertEqual(new_used, old_used, f"降級時使用量應該保持: {old_used}s")

                print(f"\n  ✅ 降級驗證通過: {old_plan} → {new_plan}")
                print(f"    - 計劃變更: {old_plan} → {new_plan}")
                print(f"    - 配額變更: {old_limit}s → {new_limit}s")
                print(f"    - 設備限制: {old_devices} → {new_devices}")
                print(f"    - 使用量保持: {old_used}s → {new_used}s")
    
    def test_03_cancel_to_free_scenarios(self):
        """測試取消到 FREE 場景"""
        print("\n🚫 測試取消到 FREE 場景")
        print("=" * 50)
        
        cancel_scenarios = ["STARTER", "PRO", "PREMIUM", "MAX"]
        
        for old_plan in cancel_scenarios:
            with self.subTest(cancel=f"{old_plan}_to_FREE"):
                user_id = f"cancel_test_{old_plan}_{self.timestamp}"
                
                # 創建測試用戶
                self.framework.create_test_user(user_id, old_plan)
                
                # 添加多個設備（如果計劃允許）
                if old_plan in ["PRO", "PREMIUM", "MAX"]:
                    self.framework.add_device(user_id, f"device_1_{user_id}")
                    self.framework.add_device(user_id, f"device_2_{user_id}")
                
                # 獲取取消前數據
                old_subscription = self.framework.get_subscription_data(user_id)
                old_devices = old_subscription.get('active_device_ids', [])
                
                # 執行取消（降級到 FREE）
                change_result = self.framework.simulate_subscription_change(user_id, "FREE", "cancel")
                
                # 驗證取消後數據
                new_subscription = self.framework.get_subscription_data(user_id)
                
                # 驗證降級到 FREE
                self.assertEqual(new_subscription['plan'], "FREE")
                self.assertEqual(new_subscription['daily_limit_seconds'], 600)  # 10 分鐘
                self.assertEqual(new_subscription['max_devices'], 1)
                
                # 驗證配額重置
                self.assertEqual(new_subscription['current_day_used_seconds'], 0)
                
                print(f"   ✅ {old_plan} → FREE: 設備從 {len(old_devices)} 個需要處理超限")

    def test_04_device_limit_management(self):
        """測試設備限制管理"""
        print("\n📱 測試設備限制管理")
        print("=" * 50)

        # 測試場景 1: 在限制內添加設備
        user_id = f"device_test_1_{self.timestamp}"
        print(f"\n  📱 場景 1: PRO 計劃設備限制測試")
        print(f"  用戶 ID: {user_id}")

        self.framework.create_test_user(user_id, "PRO")  # PRO 允許 2 個設備

        # 打印初始狀態
        initial_subscription = self.framework.get_subscription_data(user_id)
        self.framework.print_detailed_data("初始狀態 (PRO 計劃)", user_id, initial_subscription)

        # 添加第一個設備
        device_1 = f"device_1_{user_id}"
        print(f"\n    🔄 嘗試添加第一個設備: {device_1}")
        result1 = self.framework.add_device(user_id, device_1)
        self.assertTrue(result1, "應該能添加第一個設備")

        subscription_after_1 = self.framework.get_subscription_data(user_id)
        devices_1 = subscription_after_1.get('active_device_ids', [])
        print(f"    ✅ 添加成功，當前設備: {devices_1} ({len(devices_1)}/{subscription_after_1['max_devices']})")

        # 添加第二個設備
        device_2 = f"device_2_{user_id}"
        print(f"\n    🔄 嘗試添加第二個設備: {device_2}")
        result2 = self.framework.add_device(user_id, device_2)
        self.assertTrue(result2, "應該能添加第二個設備")

        subscription_after_2 = self.framework.get_subscription_data(user_id)
        devices_2 = subscription_after_2.get('active_device_ids', [])
        print(f"    ✅ 添加成功，當前設備: {devices_2} ({len(devices_2)}/{subscription_after_2['max_devices']})")

        # 嘗試添加第三個設備（應該失敗）
        device_3 = f"device_3_{user_id}"
        print(f"\n    🔄 嘗試添加第三個設備: {device_3} (應該失敗)")
        result3 = self.framework.add_device(user_id, device_3)
        self.assertFalse(result3, "不應該能添加第三個設備")

        subscription_after_3 = self.framework.get_subscription_data(user_id)
        devices_3 = subscription_after_3.get('active_device_ids', [])
        print(f"    ❌ 添加失敗 (符合預期)，當前設備: {devices_3} ({len(devices_3)}/{subscription_after_3['max_devices']})")

        # 驗證設備列表
        self.assertEqual(len(devices_3), 2, "應該有 2 個設備")

        # 測試場景 2: 升級後添加更多設備
        print(f"\n  📱 場景 2: 升級到 PREMIUM 後設備限制測試")

        # 獲取升級前狀態
        before_upgrade = self.framework.get_subscription_data(user_id)
        print(f"    升級前: {before_upgrade['plan']} 計劃，{len(before_upgrade.get('active_device_ids', []))}/{before_upgrade['max_devices']} 設備")

        self.framework.simulate_subscription_change(user_id, "PREMIUM", "upgrade")  # PREMIUM 允許 5 個設備

        # 獲取升級後狀態
        after_upgrade = self.framework.get_subscription_data(user_id)
        print(f"    升級後: {after_upgrade['plan']} 計劃，{len(after_upgrade.get('active_device_ids', []))}/{after_upgrade['max_devices']} 設備")

        # 現在應該能添加更多設備
        print(f"\n    🔄 嘗試添加第三個設備: {device_3} (升級後應該成功)")
        result4 = self.framework.add_device(user_id, device_3)
        self.assertTrue(result4, "升級後應該能添加第三個設備")

        subscription_after_upgrade = self.framework.get_subscription_data(user_id)
        devices_after_upgrade = subscription_after_upgrade.get('active_device_ids', [])
        self.assertEqual(len(devices_after_upgrade), 3, "升級後應該有 3 個設備")
        print(f"    ✅ 添加成功，當前設備: {devices_after_upgrade} ({len(devices_after_upgrade)}/{subscription_after_upgrade['max_devices']})")

        # 測試場景 3: 降級時設備超限
        print(f"\n  📱 場景 3: 降級時設備超限處理")

        # 先添加更多設備到 PREMIUM 限制
        device_4 = f"device_4_{user_id}"
        device_5 = f"device_5_{user_id}"

        print(f"    🔄 添加更多設備到 PREMIUM 限制")
        self.framework.add_device(user_id, device_4)
        self.framework.add_device(user_id, device_5)

        before_downgrade = self.framework.get_subscription_data(user_id)
        devices_before = before_downgrade.get('active_device_ids', [])
        print(f"    降級前: {before_downgrade['plan']} 計劃，{len(devices_before)}/{before_downgrade['max_devices']} 設備")
        print(f"    設備列表: {devices_before}")

        # 降級到 STARTER（只允許 1 個設備）
        print(f"\n    🔄 降級到 STARTER (只允許 1 個設備)")
        self.framework.simulate_subscription_change(user_id, "STARTER", "downgrade")

        after_downgrade = self.framework.get_subscription_data(user_id)
        devices_after = after_downgrade.get('active_device_ids', [])

        print(f"    降級後: {after_downgrade['plan']} 計劃，{len(devices_after)}/{after_downgrade['max_devices']} 設備")
        print(f"    設備列表: {devices_after}")

        self.assertEqual(after_downgrade['max_devices'], 1, "降級後設備限制應該是 1")

        # 設備列表仍然存在，但超過限制
        excess_devices = len(devices_after) - after_downgrade['max_devices']
        if excess_devices > 0:
            print(f"    ⚠️ 設備超限: {len(devices_after)} 個設備超過限制 {after_downgrade['max_devices']}，超出 {excess_devices} 個")
            print(f"    💡 需要實施設備管理策略（如：停用超出的設備）")
        else:
            print(f"    ✅ 設備數量在限制內")

        print(f"\n  📊 設備限制管理測試總結:")
        print(f"    ✅ PRO 計劃正確限制設備數量 (2 個)")
        print(f"    ✅ 升級後正確增加設備限制 (2 → 5)")
        print(f"    ✅ 降級後正確減少設備限制 (5 → 1)")
        print(f"    ✅ 設備超限情況正確識別和記錄")

    def test_05_billing_interval_changes(self):
        """測試計費週期變更"""
        print("\n💳 測試計費週期變更")
        print("=" * 50)

        user_id = f"billing_test_{self.timestamp}"
        self.framework.create_test_user(user_id, "PRO")

        # 模擬月費到年費的變更
        subscription_ref = db.collection('subscriptions').document(f'sub_{user_id}')
        subscription_ref.update({
            'billing_cycle': 'monthly',
            'price_amount': 1999,  # $19.99/月
            'stripe_subscription_id': f'sub_monthly_{user_id}'
        })

        old_subscription = self.framework.get_subscription_data(user_id)

        # 變更為年費
        subscription_ref.update({
            'billing_cycle': 'yearly',
            'price_amount': 19900,  # $199/年
            'stripe_subscription_id': f'sub_yearly_{user_id}',
            'updated_at': datetime.utcnow()
        })

        new_subscription = self.framework.get_subscription_data(user_id)

        # 驗證計費週期變更
        self.assertEqual(new_subscription['billing_cycle'], 'yearly')
        self.assertEqual(new_subscription['price_amount'], 19900)

        # 計劃配額應該保持不變
        self.assertEqual(new_subscription['plan'], 'PRO')
        self.assertEqual(new_subscription['daily_limit_seconds'], 10800)

        print(f"   ✅ 計費週期變更: monthly → yearly, 價格: $19.99 → $199")

    def test_06_complex_upgrade_downgrade_sequence(self):
        """測試複雜的升級降級序列"""
        print("\n🔄 測試複雜升級降級序列")
        print("=" * 50)

        user_id = f"sequence_test_{self.timestamp}"

        # 序列: STARTER → PRO → PREMIUM → PRO → STARTER
        sequence = [
            ("STARTER", "PRO", "upgrade"),
            ("PRO", "PREMIUM", "upgrade"),
            ("PREMIUM", "PRO", "downgrade"),
            ("PRO", "STARTER", "downgrade")
        ]

        # 從 STARTER 開始
        self.framework.create_test_user(user_id, "STARTER")

        # 添加一個設備
        self.framework.add_device(user_id, f"device_1_{user_id}")

        # 記錄每一步的變化
        changes = []

        for old_plan, new_plan, change_type in sequence:
            # 獲取變更前狀態
            old_subscription = self.framework.get_subscription_data(user_id)

            # 執行變更
            change_result = self.framework.simulate_subscription_change(user_id, new_plan, change_type)

            # 獲取變更後狀態
            new_subscription = self.framework.get_subscription_data(user_id)

            # 記錄變化
            change_record = {
                'from': old_plan,
                'to': new_plan,
                'type': change_type,
                'old_limit': old_subscription['daily_limit_seconds'],
                'new_limit': new_subscription['daily_limit_seconds'],
                'old_devices': old_subscription['max_devices'],
                'new_devices': new_subscription['max_devices'],
                'quota_reset': new_subscription['current_day_used_seconds'] == 0
            }
            changes.append(change_record)

            # 驗證變更
            self.assertEqual(new_subscription['plan'], new_plan)

            # 升級時應該重置配額
            if change_type == "upgrade":
                self.assertEqual(new_subscription['current_day_used_seconds'], 0)

            print(f"   ✅ {old_plan} → {new_plan} ({change_type}): "
                  f"配額 {change_record['old_limit']}s → {change_record['new_limit']}s, "
                  f"設備 {change_record['old_devices']} → {change_record['new_devices']}")

        # 驗證最終狀態
        final_subscription = self.framework.get_subscription_data(user_id)
        self.assertEqual(final_subscription['plan'], 'STARTER')
        self.assertEqual(final_subscription['daily_limit_seconds'], 3600)
        self.assertEqual(final_subscription['max_devices'], 1)

        print(f"   ✅ 序列完成，最終計劃: {final_subscription['plan']}")

    def test_07_usage_preservation_during_changes(self):
        """測試變更期間使用量保持"""
        print("\n📊 測試變更期間使用量保持")
        print("=" * 50)

        user_id = f"usage_test_{self.timestamp}"
        self.framework.create_test_user(user_id, "PRO")

        # 設置一些使用量
        subscription_ref = db.collection('subscriptions').document(f'sub_{user_id}')
        subscription_ref.update({
            'current_day_used_seconds': 5400  # 1.5 小時
        })

        # 測試升級（應該重置使用量）
        old_subscription = self.framework.get_subscription_data(user_id)
        old_usage = old_subscription['current_day_used_seconds']

        self.framework.simulate_subscription_change(user_id, "PREMIUM", "upgrade")

        new_subscription = self.framework.get_subscription_data(user_id)
        self.assertEqual(new_subscription['current_day_used_seconds'], 0, "升級應該重置使用量")

        print(f"   ✅ 升級重置使用量: {old_usage}s → 0s")

        # 重新設置使用量
        subscription_ref.update({
            'current_day_used_seconds': 7200  # 2 小時
        })

        # 測試降級（應該保持使用量）
        old_subscription = self.framework.get_subscription_data(user_id)
        old_usage = old_subscription['current_day_used_seconds']

        self.framework.simulate_subscription_change(user_id, "PRO", "downgrade")

        new_subscription = self.framework.get_subscription_data(user_id)
        self.assertEqual(new_subscription['current_day_used_seconds'], old_usage, "降級應該保持使用量")

        print(f"   ✅ 降級保持使用量: {old_usage}s → {new_subscription['current_day_used_seconds']}s")

    def test_08_unlimited_plan_handling(self):
        """測試無限制計劃處理"""
        print("\n♾️ 測試無限制計劃處理")
        print("=" * 50)

        user_id = f"unlimited_test_{self.timestamp}"
        self.framework.create_test_user(user_id, "MAX")

        subscription = self.framework.get_subscription_data(user_id)

        # 驗證無限制設置
        self.assertEqual(subscription['daily_limit_seconds'], -1, "MAX 計劃應該有無限制配額")
        self.assertEqual(subscription['max_devices'], -1, "MAX 計劃應該有無限制設備")

        # 測試添加多個設備
        for i in range(10):
            result = self.framework.add_device(user_id, f"device_{i}_{user_id}")
            self.assertTrue(result, f"MAX 計劃應該能添加設備 {i}")

        subscription = self.framework.get_subscription_data(user_id)
        devices = subscription.get('active_device_ids', [])
        self.assertEqual(len(devices), 10, "MAX 計劃應該能添加 10 個設備")

        print(f"   ✅ MAX 計劃: 無限制配額和設備，已添加 {len(devices)} 個設備")

        # 測試從無限制降級
        self.framework.simulate_subscription_change(user_id, "PRO", "downgrade")

        subscription = self.framework.get_subscription_data(user_id)
        self.assertEqual(subscription['daily_limit_seconds'], 10800, "降級後應該有限制配額")
        self.assertEqual(subscription['max_devices'], 2, "降級後應該有設備限制")

        # 設備列表仍然存在但超過限制
        devices = subscription.get('active_device_ids', [])
        print(f"   ⚠️ 從 MAX 降級到 PRO: {len(devices)} 設備超過限制 2")

    def test_09_data_consistency_verification(self):
        """測試數據一致性驗證"""
        print("\n🔍 測試數據一致性驗證")
        print("=" * 50)

        user_id = f"consistency_test_{self.timestamp}"

        print(f"\n  📋 初始化測試用戶: {user_id}")
        self.framework.create_test_user(user_id, "STARTER")

        # 獲取初始數據
        initial_user = self.framework.get_user_data(user_id)
        initial_subscription = self.framework.get_subscription_data(user_id)

        # 打印初始數據
        self.framework.print_detailed_data("初始數據 (STARTER)", user_id, initial_subscription, initial_user)

        # 執行多次變更
        changes = [
            ("STARTER", "PRO", "upgrade"),
            ("PRO", "PREMIUM", "upgrade"),
            ("PREMIUM", "MAX", "upgrade"),
            ("MAX", "FREE", "cancel")
        ]

        for i, (old_plan, new_plan, change_type) in enumerate(changes, 1):
            print(f"\n  🔄 第 {i} 次變更: {old_plan} → {new_plan} ({change_type})")
            print(f"  {'='*60}")

            # 獲取變更前數據
            old_user = self.framework.get_user_data(user_id)
            old_subscription = self.framework.get_subscription_data(user_id)

            print(f"\n    📊 變更前數據庫狀態:")
            print(f"    用戶表 (users/{user_id}):")
            print(f"      - current_subscription_id: {old_user.get('current_subscription_id')}")
            print(f"      - stripe_customer_id: {old_user.get('stripe_customer_id')}")
            print(f"      - updated_at: {old_user.get('updated_at')}")

            print(f"    訂閱表 (subscriptions/sub_{user_id}):")
            print(f"      - user_id: {old_subscription.get('user_id')}")
            print(f"      - plan: {old_subscription.get('plan')}")
            print(f"      - status: {old_subscription.get('status')}")
            print(f"      - daily_limit_seconds: {old_subscription.get('daily_limit_seconds')}")
            print(f"      - max_devices: {old_subscription.get('max_devices')}")
            print(f"      - current_day_used_seconds: {old_subscription.get('current_day_used_seconds')}")
            print(f"      - updated_at: {old_subscription.get('updated_at')}")

            # 執行變更
            self.framework.simulate_subscription_change(user_id, new_plan, change_type)

            # 獲取變更後數據
            new_user = self.framework.get_user_data(user_id)
            new_subscription = self.framework.get_subscription_data(user_id)

            print(f"\n    📊 變更後數據庫狀態:")
            print(f"    用戶表 (users/{user_id}):")
            print(f"      - current_subscription_id: {new_user.get('current_subscription_id')}")
            print(f"      - stripe_customer_id: {new_user.get('stripe_customer_id')}")
            print(f"      - updated_at: {new_user.get('updated_at')}")

            print(f"    訂閱表 (subscriptions/sub_{user_id}):")
            print(f"      - user_id: {new_subscription.get('user_id')}")
            print(f"      - plan: {new_subscription.get('plan')}")
            print(f"      - status: {new_subscription.get('status')}")
            print(f"      - daily_limit_seconds: {new_subscription.get('daily_limit_seconds')}")
            print(f"      - max_devices: {new_subscription.get('max_devices')}")
            print(f"      - current_day_used_seconds: {new_subscription.get('current_day_used_seconds')}")
            print(f"      - updated_at: {new_subscription.get('updated_at')}")

            # 驗證關聯一致性
            print(f"\n    🔍 數據一致性檢查:")

            # 檢查用戶-訂閱關聯
            user_sub_id = new_user.get('current_subscription_id')
            expected_sub_id = f'sub_{user_id}'
            print(f"      用戶訂閱 ID: {user_sub_id} (期望: {expected_sub_id})")
            self.assertEqual(user_sub_id, expected_sub_id, f"用戶的 current_subscription_id 應該是 {expected_sub_id}")

            # 檢查訂閱-用戶關聯
            sub_user_id = new_subscription.get('user_id')
            print(f"      訂閱用戶 ID: {sub_user_id} (期望: {user_id})")
            self.assertEqual(sub_user_id, user_id, f"訂閱的 user_id 應該是 {user_id}")

            # 檢查計劃更新
            actual_plan = new_subscription.get('plan')
            print(f"      訂閱計劃: {actual_plan} (期望: {new_plan})")
            self.assertEqual(actual_plan, new_plan, f"訂閱計劃應該是 {new_plan}")

            # 檢查時間戳更新
            old_updated = old_subscription.get('updated_at')
            new_updated = new_subscription.get('updated_at')
            print(f"      訂閱更新時間: {old_updated} → {new_updated}")
            self.assertIsNotNone(new_updated, "訂閱的 updated_at 不應該為空")

            user_updated = new_user.get('updated_at')
            print(f"      用戶更新時間: {user_updated}")
            self.assertIsNotNone(user_updated, "用戶的 updated_at 不應該為空")

            # 檢查狀態邏輯
            if change_type == "cancel":
                expected_status = "canceled"
            else:
                expected_status = "active"
            actual_status = new_subscription.get('status')
            print(f"      訂閱狀態: {actual_status} (期望: {expected_status})")
            self.assertEqual(actual_status, expected_status, f"訂閱狀態應該是 {expected_status}")

            print(f"\n    ✅ 第 {i} 次變更數據一致性驗證通過: {old_plan} → {new_plan}")
            print(f"      - 用戶-訂閱關聯正確")
            print(f"      - 計劃更新正確")
            print(f"      - 時間戳更新正確")
            print(f"      - 狀態邏輯正確")

        print(f"\n  🎉 所有數據一致性檢查通過！")
        print(f"    總共執行了 {len(changes)} 次變更，所有數據庫狀態都正確")

    def test_10_edge_cases(self):
        """測試邊界情況"""
        print("\n⚠️ 測試邊界情況")
        print("=" * 50)

        # 測試 1: 相同計劃"升級"
        user_id = f"edge_test_1_{self.timestamp}"
        self.framework.create_test_user(user_id, "PRO")

        old_subscription = self.framework.get_subscription_data(user_id)
        self.framework.simulate_subscription_change(user_id, "PRO", "upgrade")
        new_subscription = self.framework.get_subscription_data(user_id)

        self.assertEqual(old_subscription['plan'], new_subscription['plan'])
        print("   ✅ 相同計劃變更處理正常")

        # 測試 2: 空設備列表處理
        user_id = f"edge_test_2_{self.timestamp}"
        self.framework.create_test_user(user_id, "FREE")

        subscription = self.framework.get_subscription_data(user_id)
        devices = subscription.get('active_device_ids', [])
        self.assertEqual(len(devices), 0, "新用戶應該沒有設備")

        # 嘗試添加設備到 FREE 計劃
        result = self.framework.add_device(user_id, f"device_1_{user_id}")
        self.assertTrue(result, "FREE 計劃應該能添加 1 個設備")

        result = self.framework.add_device(user_id, f"device_2_{user_id}")
        self.assertFalse(result, "FREE 計劃不應該能添加第 2 個設備")

        print("   ✅ FREE 計劃設備限制正常")

        # 測試 3: 極大使用量處理
        user_id = f"edge_test_3_{self.timestamp}"
        self.framework.create_test_user(user_id, "MAX")

        # 設置極大使用量
        subscription_ref = db.collection('subscriptions').document(f'sub_{user_id}')
        subscription_ref.update({
            'current_day_used_seconds': 86400  # 24 小時
        })

        subscription = self.framework.get_subscription_data(user_id)
        self.assertEqual(subscription['current_day_used_seconds'], 86400)

        # 降級到有限制的計劃
        self.framework.simulate_subscription_change(user_id, "STARTER", "downgrade")

        subscription = self.framework.get_subscription_data(user_id)
        # 使用量應該保持，但超過新限制
        self.assertEqual(subscription['current_day_used_seconds'], 86400)
        self.assertLess(subscription['daily_limit_seconds'], 86400)

        print("   ✅ 極大使用量處理正常")

def run_subscription_scenario_tests():
    """執行訂閱場景測試"""
    print("🧪 開始全面訂閱場景測試...")
    print("=" * 60)

    if db is None:
        print("❌ Firebase Admin SDK 未可用，跳過測試")
        return False

    # 創建測試套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestSubscriptionScenarios)

    # 執行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    print("=" * 60)

    if result.wasSuccessful():
        print("🎉 所有訂閱場景測試通過！")
        print("\n📋 測試場景總結:")
        print("✅ 升級場景 (FREE→STARTER/PRO/PREMIUM/MAX)")
        print("✅ 降級場景 (MAX→PREMIUM→PRO→STARTER)")
        print("✅ 取消到 FREE 場景")
        print("✅ 設備限制管理")
        print("✅ 計費週期變更")
        print("✅ 複雜升級降級序列")
        print("✅ 使用量保持邏輯")
        print("✅ 無限制計劃處理")
        print("✅ 數據一致性驗證")
        print("✅ 邊界情況處理")

        print("\n🔍 數據庫驗證完成:")
        print("  - subscriptions 表狀態正確")
        print("  - users 表關聯正確")
        print("  - 設備限制執行正確")
        print("  - 配額限制更新正確")
        print("  - 時間戳更新正確")

        print("\n🚀 所有訂閱邏輯驗證完成！")
        return True
    else:
        print(f"❌ 測試失敗: {len(result.failures)} 個失敗, {len(result.errors)} 個錯誤")

        if result.failures:
            print("\n失敗的測試:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")

        if result.errors:
            print("\n錯誤的測試:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")

        return False

if __name__ == '__main__':
    success = run_subscription_scenario_tests()
    sys.exit(0 if success else 1)
