# Webhook 測試結構修改說明

## 問題背景

用戶發現在 webhook 測試中，支付動作會觸發用戶表的 INSERT 操作，而不是預期的 UPDATE 操作。經過分析發現，這是因為測試框架在每個測試場景中都會創建新的測試用戶，導致在支付處理時看起來像是在創建新用戶。

## 根本原因分析

1. **測試日誌時間戳問題**: 測試日誌記錄器記錄的用戶創建操作與支付處理操作的時間戳可能很接近，造成混淆
2. **測試架構問題**: 每個測試場景都創建新用戶，而不是使用一個持續存在的用戶進行所有測試
3. **實際 webhook 處理邏輯正確**: `handle_payment_succeeded` 函數只查詢現有用戶，不會創建新用戶

## 解決方案

### 1. 創建增強的測試日誌記錄器

**文件**: `unit_test/test_enhanced_logging.py`

- 提供詳細的測試執行記錄和數據庫變更追蹤
- 包含時間戳格式化，清楚顯示操作發生的時間
- 支持測試場景管理、Webhook 調用記錄、數據庫變更記錄
- 自動生成 Markdown 測試報告

### 2. 修改測試框架結構

**文件**: `unit_test/test_complete_webhook_scenarios.py`

#### 主要修改:

1. **添加共享測試用戶機制**:
   ```python
   def create_shared_test_user(self) -> str:
       """在測試開始時創建一個共享的測試用戶，使用 create_or_update_user 方法"""
   ```

2. **使用正確的用戶創建方法**:
   - 導入並使用 `functions.users.create_or_update_user.create_free_subscription`
   - 遵循正確的用戶創建流程，包含所有必要的字段

3. **修改測試類設置**:
   ```python
   @classmethod
   def setUpClass(cls):
       # 在測試開始時創建共享測試用戶
       cls.shared_user_id = cls.framework.create_shared_test_user()
   ```

4. **更新所有測試方法**:
   - 所有測試方法現在使用 `self.shared_user_id` 而不是創建新用戶
   - 測試圍繞同一個用戶的不同訂閱狀態變化進行

### 3. 測試流程改進

#### 修改前的流程:
```
測試 1: 創建用戶 A → 測試訂閱創建 + 支付
測試 2: 創建用戶 B → 測試訂閱升級 + 支付  
測試 3: 創建用戶 C → 測試訂閱取消
測試 4: 創建用戶 D → 測試多次支付
```

#### 修改後的流程:
```
測試開始: 創建共享用戶 (使用 create_or_update_user)
測試 1: 使用共享用戶 → 測試訂閱創建 + 支付
測試 2: 使用共享用戶 → 測試訂閱升級 + 支付
測試 3: 使用共享用戶 → 測試訂閱取消
測試 4: 使用共享用戶 → 測試多次支付
```

## 技術細節

### 1. 共享用戶創建邏輯

```python
# 使用正確的 create_or_update_user 邏輯創建用戶
user_data = {
    'uid': user_id,
    'email': f'test_{user_id}@example.com',
    'display_name': f'Shared Test User',
    'auth_provider': 'google',
    'email_verified': True,
    'is_active': True,
    'is_banned': False,
    # ... 其他必要字段
}

# 創建用戶記錄
self.db.collection('users').document(user_id).set(user_data)

# 使用 create_free_subscription 創建訂閱
subscription_data = create_free_subscription(user_id, source_app='test')
```

### 2. 回退機制

如果 `create_free_subscription` 導入失敗，系統會自動回退到原有的用戶創建方法，確保測試的穩定性。

### 3. 測試日誌改進

- 清楚標記操作時間戳 (HH:MM:SS 格式)
- 區分測試設置階段和實際測試階段的數據庫操作
- 提供詳細的數據庫變更記錄

## 驗證結果

創建了 `unit_test/test_structure_validation.py` 來驗證修改的正確性:

```
🎉 所有結構驗證測試通過！

📋 驗證項目:
✅ 測試框架導入
✅ 共享用戶創建邏輯  
✅ 回退用戶創建邏輯
✅ 測試類結構
```

## 預期效果

1. **解決用戶表 INSERT 問題**: 用戶創建只在測試開始時發生一次，支付處理時不會再創建用戶
2. **更真實的測試場景**: 模擬真實用戶的訂閱生命週期，而不是每次都創建新用戶
3. **清晰的測試日誌**: 時間戳清楚顯示操作順序，避免混淆
4. **更好的測試性能**: 減少數據庫操作，提高測試執行效率

## 使用方法

1. 運行完整測試:
   ```bash
   cd unit_test
   python test_complete_webhook_scenarios.py
   ```

2. 運行結構驗證:
   ```bash
   cd unit_test  
   python test_structure_validation.py
   ```

3. 查看測試報告:
   - 測試報告會自動保存到 `unit_test/results/` 目錄
   - 文件名格式: `test_results_YYYYMMDD_HHMMSS.md`

## 注意事項

1. **Firebase 認證**: 測試需要正確的 Firebase 認證配置
2. **環境變數**: 確保 `.env.dev` 文件配置正確
3. **依賴導入**: 測試依賴 `functions.users.create_or_update_user` 模塊

## 總結

通過這次修改，我們成功解決了用戶反映的問題，並改進了測試架構，使其更符合實際業務場景。測試現在會在開始時使用正確的 `create_or_update_user` 方法創建一個共享用戶，然後所有後續測試都圍繞這個用戶進行，避免了支付處理時出現用戶創建的混淆情況。
