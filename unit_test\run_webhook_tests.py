"""
執行所有 Stripe Webhook 相關測試
"""

import sys
import os

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend-api'))

def main():
    """主測試函數"""
    print("🚀 SpeakOneAI Stripe Webhook 測試套件")
    print("=" * 60)
    print("測試範圍:")
    print("  - Stripe Webhook 事件處理")
    print("  - 每日配額重置功能")
    print("  - 訂閱生命週期管理")
    print("  - 支付事件處理")
    print("=" * 60)
    
    all_tests_passed = True
    
    # 執行 Stripe Webhook 測試
    print("\n🔔 執行 Stripe Webhook 測試...")
    try:
        from test_stripe_webhook import run_stripe_webhook_tests
        webhook_result = run_stripe_webhook_tests()
        if not webhook_result:
            all_tests_passed = False
    except Exception as e:
        print(f"❌ Stripe Webhook 測試執行失敗: {str(e)}")
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    
    # 執行每日配額重置測試
    print("\n⏰ 執行每日配額重置測試...")
    try:
        from test_daily_quota_reset import run_daily_quota_reset_tests
        quota_result = run_daily_quota_reset_tests()
        if not quota_result:
            all_tests_passed = False
    except Exception as e:
        print(f"❌ 每日配額重置測試執行失敗: {str(e)}")
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    
    # 測試總結
    if all_tests_passed:
        print("🎉 所有測試通過！")
        print("\n✅ 測試總結:")
        print("  ✅ Stripe Webhook 事件處理正常")
        print("  ✅ 每日配額重置邏輯正確")
        print("  ✅ 訂閱生命週期管理完整")
        print("  ✅ 支付事件處理準確")
        
        print("\n🚀 部署準備就緒！")
        print("\n📋 部署檢查清單:")
        print("  [ ] 設置 Stripe Webhook 端點")
        print("  [ ] 配置環境變數 (STRIPE_SECRET_KEY, STRIPE_WEBHOOK_SECRET)")
        print("  [ ] 更新 Stripe Price ID 映射")
        print("  [ ] 部署 Cloud Functions")
        print("  [ ] 測試 Webhook 端點連通性")
        
        print("\n🔗 Webhook 端點:")
        print("  開發: https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/backend-api/stripe_webhook")
        print("  生產: https://asia-east1-speakoneai-prod.cloudfunctions.net/backend-api/stripe_webhook")
        
        print("\n📝 支援的 Webhook 事件:")
        print("  - customer.subscription.created")
        print("  - customer.subscription.updated")
        print("  - customer.subscription.deleted")
        print("  - invoice.payment_succeeded")
        print("  - invoice.payment_failed")
        
        print("\n⏰ 定時任務:")
        print("  - 每小時執行配額重置檢查")
        print("  - 根據用戶時區在當地 00:00 重置")
        print("  - 支援全球時區 (UTC-12 到 UTC+14)")
        
    else:
        print("❌ 部分測試失敗")
        print("\n請檢查上述錯誤信息並修復問題後重新測試")
    
    print("\n" + "=" * 60)
    return all_tests_passed

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
