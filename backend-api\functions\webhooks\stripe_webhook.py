"""
Stripe Webhook 處理函數
處理 Stripe 訂閱生命週期事件和支付事件
"""

import logging
import json
import hmac
import hashlib
import os
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from firebase_functions import https_fn
from firebase_admin import firestore

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端（環境感知）"""
    from ..utils.environment import get_db as get_env_db
    return get_env_db()

def verify_stripe_signature(payload: str, signature: str, webhook_secret: str) -> bool:
    """
    驗證 Stripe webhook 簽名
    
    Args:
        payload: 原始請求體
        signature: Stripe-Signature header
        webhook_secret: Stripe webhook secret
        
    Returns:
        bool: 簽名是否有效
    """
    try:
        # 解析簽名 header
        elements = signature.split(',')
        timestamp = None
        signatures = []
        
        for element in elements:
            key, value = element.split('=', 1)
            if key == 't':
                timestamp = value
            elif key == 'v1':
                signatures.append(value)
        
        if not timestamp or not signatures:
            return False
        
        # 構建簽名字符串
        signed_payload = f"{timestamp}.{payload}"
        
        # 計算期望的簽名
        expected_signature = hmac.new(
            webhook_secret.encode('utf-8'),
            signed_payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        # 驗證簽名
        return any(hmac.compare_digest(expected_signature, sig) for sig in signatures)
        
    except Exception as e:
        logger.error(f"簽名驗證錯誤: {str(e)}")
        return False

def check_event_processed(event_id: str) -> bool:
    """
    檢查事件是否已處理（冪等性檢查）
    
    Args:
        event_id: Stripe 事件 ID
        
    Returns:
        bool: 事件是否已處理
    """
    try:
        db = get_db()
        processed_events_ref = db.collection('processed_events').document(event_id)
        doc = processed_events_ref.get()
        
        if doc.exists:
            logger.info(f"事件 {event_id} 已處理，跳過")
            return True
        
        # 標記事件為處理中
        processed_events_ref.set({
            'event_id': event_id,
            'processed_at': datetime.utcnow(),
            'status': 'processing'
        })
        
        return False
        
    except Exception as e:
        logger.error(f"檢查事件處理狀態錯誤: {str(e)}")
        return True  # 安全起見，假設已處理

def mark_event_completed(event_id: str, success: bool = True, error: str = None):
    """
    標記事件處理完成
    
    Args:
        event_id: Stripe 事件 ID
        success: 是否成功處理
        error: 錯誤信息（如果有）
    """
    try:
        db = get_db()
        processed_events_ref = db.collection('processed_events').document(event_id)
        
        update_data = {
            'status': 'completed' if success else 'failed',
            'completed_at': datetime.utcnow()
        }
        
        if error:
            update_data['error'] = error
        
        processed_events_ref.update(update_data)
        
    except Exception as e:
        logger.error(f"標記事件完成狀態錯誤: {str(e)}")

def stripe_webhook_handler(req: https_fn.Request) -> https_fn.Response:
    """
    Stripe Webhook 主處理函數
    
    Args:
        req: HTTP 請求對象
        
    Returns:
        https_fn.Response: HTTP 響應
    """
    try:
        # 獲取請求數據
        payload = req.get_data(as_text=True)
        signature = req.headers.get('stripe-signature')
        
        if not signature:
            logger.error("缺少 Stripe 簽名")
            return https_fn.Response("缺少 Stripe 簽名", status=400)
        
        # 獲取 webhook secret
        webhook_secret = os.getenv('STRIPE_WEBHOOK_SECRET')
        if not webhook_secret:
            logger.error("未配置 STRIPE_WEBHOOK_SECRET")
            return https_fn.Response("Webhook secret 未配置", status=500)
        
        # 驗證簽名
        if not verify_stripe_signature(payload, signature, webhook_secret):
            logger.error("Stripe 簽名驗證失敗")
            return https_fn.Response("簽名驗證失敗", status=400)
        
        # 解析事件
        try:
            event = json.loads(payload)
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析錯誤: {str(e)}")
            return https_fn.Response("無效的 JSON", status=400)
        
        event_id = event.get('id')
        event_type = event.get('type')
        
        if not event_id or not event_type:
            logger.error("缺少事件 ID 或類型")
            return https_fn.Response("無效的事件格式", status=400)
        
        logger.info(f"收到 Stripe 事件: {event_type} (ID: {event_id})")
        
        # 檢查事件是否已處理
        if check_event_processed(event_id):
            return https_fn.Response("事件已處理", status=200)
        
        # 路由到對應的處理函數
        success = False
        error_message = None
        
        try:
            if event_type == 'customer.subscription.created':
                from .subscription_handlers import handle_subscription_created
                handle_subscription_created(event['data']['object'])
                success = True
                
            elif event_type == 'customer.subscription.updated':
                from .subscription_handlers import handle_subscription_updated
                handle_subscription_updated(event['data']['object'], event['data'].get('previous_attributes', {}))
                success = True
                
            elif event_type == 'customer.subscription.deleted':
                from .subscription_handlers import handle_subscription_deleted
                handle_subscription_deleted(event['data']['object'])
                success = True
                
            elif event_type == 'invoice.payment_succeeded':
                from .payment_handlers import handle_payment_succeeded
                handle_payment_succeeded(event['data']['object'])
                success = True
                
            elif event_type == 'invoice.payment_failed':
                from .payment_handlers import handle_payment_failed
                handle_payment_failed(event['data']['object'])
                success = True
                
            else:
                logger.info(f"未處理的事件類型: {event_type}")
                success = True  # 不是錯誤，只是不處理
        
        except Exception as e:
            error_message = str(e)
            logger.error(f"處理事件 {event_type} 錯誤: {error_message}")
            success = False
        
        # 標記事件處理完成
        mark_event_completed(event_id, success, error_message)
        
        if success:
            logger.info(f"事件 {event_type} 處理成功")
            return https_fn.Response("事件處理成功", status=200)
        else:
            logger.error(f"事件 {event_type} 處理失敗: {error_message}")
            return https_fn.Response("事件處理失敗", status=500)
        
    except Exception as e:
        logger.error(f"Webhook 處理錯誤: {str(e)}")
        return https_fn.Response("內部錯誤", status=500)
