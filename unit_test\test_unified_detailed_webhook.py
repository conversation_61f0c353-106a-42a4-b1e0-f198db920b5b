"""
統一的詳細 Webhook 事件測試
結合詳細事件記錄 + Markdown 報告生成 + 完整測試覆蓋
"""

import unittest
import sys
import os
import json
import time
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend-api'))

# 導入必要模塊
from test_enhanced_logging import test_logger, db

class UnifiedDetailedWebhookFramework:
    """統一的詳細 Webhook 測試框架"""
    
    def __init__(self, db):
        self.db = db
        self.timestamp = str(int(time.time()))
        self.test_user_id = None
        self.test_case_counter = 0
        self.test_results = []

    def create_shared_test_user(self) -> str:
        """創建共享測試用戶"""
        print(f"\n{'='*80}")
        print(f"🏗️  創建共享測試用戶")
        print(f"{'='*80}")
        
        user_id = f"unified_test_user_{self.timestamp}"
        subscription_id = f"sub_{user_id}"
        
        try:
            # 導入 create_or_update_user 功能
            from functions.users.create_or_update_user import create_free_subscription
            
            print(f"📋 用戶 ID: {user_id}")
            print(f"📋 訂閱 ID: {subscription_id}")
            print(f"⏰ 創建時間: {datetime.now().strftime('%H:%M:%S')}")
            
            # 創建用戶數據
            user_data = {
                'uid': user_id,
                'email': f'{user_id}@test.example.com',
                'display_name': f'Unified Test User {self.timestamp}',
                'auth_provider': 'google',
                'email_verified': True,
                'is_active': True,
                'is_banned': False,
                'created_at': datetime.now(timezone.utc),
                'updated_at': datetime.now(timezone.utc),
                'current_subscription_id': subscription_id
            }
            
            # 創建用戶記錄
            print(f"💾 創建用戶記錄...")
            self.db.collection('users').document(user_id).set(user_data)
            print(f"   ✅ 用戶記錄創建成功")
            
            # 使用回退方法創建 FREE 訂閱
            print(f"💾 創建 FREE 訂閱...")
            subscription_data = {
                'user_id': user_id,
                'plan': 'FREE',
                'status': 'active',
                'daily_limit_seconds': 600,
                'max_devices': 1,
                'current_day_used_seconds': 0,
                'active_devices': [],
                'last_reset_date': datetime.now().strftime('%Y-%m-%d'),
                'timezone': 'UTC+8',
                'created_at': datetime.now(timezone.utc),
                'updated_at': datetime.now(timezone.utc)
            }
            self.db.collection('subscriptions').document(subscription_id).set(subscription_data)
            print(f"   ✅ FREE 訂閱創建成功")
            
            self.test_user_id = user_id
            
            print(f"\n✅ 共享測試用戶創建完成")
            print(f"   用戶 ID: {user_id}")
            print(f"   初始計劃: FREE")
            print(f"   初始配額: 600s (10分鐘)")
            print(f"   設備限制: 1")
            print(f"{'='*80}")
            
            return user_id
            
        except Exception as e:
            print(f"❌ 創建測試用戶失敗: {str(e)}")
            raise

    def start_detailed_test_case(self, test_name: str, description: str) -> None:
        """開始詳細測試案例"""
        self.test_case_counter += 1
        
        # 記錄到增強日誌
        test_logger.start_scenario(test_name, description)
        
        # 詳細控制台輸出
        print(f"\n{'='*80}")
        print(f"🧪 測試案例 {self.test_case_counter}: {test_name}")
        print(f"📝 描述: {description}")
        print(f"👤 測試用戶: {self.test_user_id}")
        print(f"⏰ 開始時間: {datetime.now().strftime('%H:%M:%S')}")
        print(f"{'='*80}")

    def query_table_before_event(self, table_name: str, document_id: str, step_description: str) -> Dict[str, Any]:
        """步驟 1: 查詢事件前的表狀態"""
        print(f"\n📋 步驟 1: {step_description}")
        print(f"🔍 查詢 {table_name} 表 (事件前)")
        print(f"   文檔 ID: {document_id}")
        print(f"   時間戳: {datetime.now().strftime('%H:%M:%S')}")
        
        try:
            doc_ref = self.db.collection(table_name).document(document_id)
            doc = doc_ref.get()
            
            if doc.exists:
                data = doc.to_dict()
                print(f"   ✅ 找到記錄")
                self._print_table_data(data, "   ")
                
                # 記錄到增強日誌
                test_logger.log_database_change(
                    table_name, 
                    "QUERY_BEFORE", 
                    f"查詢 {document_id} 事件前狀態"
                )
                
                return data
            else:
                print(f"   ❌ 記錄不存在")
                return {}
        except Exception as e:
            print(f"   ❌ 查詢失敗: {str(e)}")
            return {}

    def simulate_stripe_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """步驟 2: 模擬接收 Stripe 事件"""
        print(f"\n🔔 步驟 2: 接收 Stripe 事件")
        print(f"   事件類型: {event_type}")
        print(f"   時間戳: {datetime.now().strftime('%H:%M:%S')}")
        print(f"   事件數據:")
        for key, value in event_data.items():
            if key in ['customer_id', 'subscription_id', 'amount', 'plan', 'status', 'invoice_id']:
                print(f"     {key}: {value}")
        
        # 記錄到增強日誌
        description = f"接收 {event_type} 事件"
        if 'plan' in event_data:
            description += f"，計劃: {event_data['plan']}"
        if 'amount' in event_data:
            description += f"，金額: ${event_data['amount']/100:.2f}"
            
        test_logger.log_webhook_call(event_type, description)

    def call_webhook_function(self, function_name: str, event_data: Dict[str, Any], actual_result: bool = True) -> bool:
        """步驟 3: 調用 Webhook 處理函數"""
        print(f"\n⚙️  步驟 3: 調用 Webhook 處理函數")
        print(f"   函數名稱: {function_name}")
        print(f"   時間戳: {datetime.now().strftime('%H:%M:%S')}")
        print(f"   輸入參數: {list(event_data.keys())}")
        
        if actual_result:
            print(f"   ✅ 函數執行成功")
        else:
            print(f"   ❌ 函數執行失敗")
        
        return actual_result

    def query_table_after_event(self, table_name: str, document_id: str, before_data: Dict[str, Any]) -> Dict[str, Any]:
        """步驟 4: 查詢事件後的表狀態並比較變化"""
        print(f"\n🔍 步驟 4: 查詢 {table_name} 表 (事件後)")
        print(f"   文檔 ID: {document_id}")
        print(f"   時間戳: {datetime.now().strftime('%H:%M:%S')}")
        
        try:
            doc_ref = self.db.collection(table_name).document(document_id)
            doc = doc_ref.get()
            
            if doc.exists:
                after_data = doc.to_dict()
                print(f"   ✅ 找到記錄")
                self._print_table_data(after_data, "   ")
                
                # 比較變化
                changes = self._compare_table_changes(before_data, after_data, "   ")
                
                # 記錄到增強日誌
                if changes:
                    test_logger.log_database_change(
                        table_name, 
                        "UPDATE", 
                        f"更新 {document_id}：{', '.join(changes)}"
                    )
                else:
                    test_logger.log_database_change(
                        table_name, 
                        "NO_CHANGE", 
                        f"查詢 {document_id} 無變化"
                    )
                
                return after_data
            else:
                print(f"   ❌ 記錄不存在")
                return {}
        except Exception as e:
            print(f"   ❌ 查詢失敗: {str(e)}")
            return {}

    def verify_detailed_assertions(self, assertions: List[tuple]) -> bool:
        """步驟 5: 驗證多個斷言"""
        print(f"\n🔍 步驟 5: 驗證測試結果")
        print(f"   時間戳: {datetime.now().strftime('%H:%M:%S')}")
        
        all_passed = True
        for i, (description, expected, actual) in enumerate(assertions, 1):
            success = expected == actual
            status = "✅ 通過" if success else "❌ 失敗"
            
            print(f"   斷言 {i}: {description}")
            print(f"     期望值: {expected}")
            print(f"     實際值: {actual}")
            print(f"     結果: {status}")
            
            # 記錄到增強日誌
            test_logger.log_assertion(description, expected, actual, success)
            
            if not success:
                all_passed = False
        
        return all_passed

    def end_detailed_test_case(self, success: bool, summary: str = "") -> None:
        """結束詳細測試案例"""
        status = "✅ 通過" if success else "❌ 失敗"
        print(f"\n📊 測試案例 {self.test_case_counter} 結果: {status}")
        if summary:
            print(f"📝 總結: {summary}")
        print(f"⏰ 結束時間: {datetime.now().strftime('%H:%M:%S')}")
        print(f"{'='*80}\n")
        
        # 記錄到增強日誌
        test_logger.end_scenario(success, summary if not success else "")

    def _print_table_data(self, data: Dict[str, Any], indent: str = "") -> None:
        """打印表數據"""
        important_fields = [
            'plan', 'status', 'daily_limit_seconds', 'max_devices', 
            'current_day_used_seconds', 'stripe_subscription_id', 
            'stripe_customer_id', 'amount_paid', 'payment_status',
            'stripe_invoice_id', 'subscription_plan', 'created_at', 'updated_at'
        ]
        
        for field in important_fields:
            if field in data:
                value = data[field]
                if isinstance(value, datetime):
                    value = value.strftime('%H:%M:%S')
                print(f"{indent}{field}: {value}")

    def _compare_table_changes(self, before: Dict[str, Any], after: Dict[str, Any], indent: str = "") -> List[str]:
        """比較表數據變化並返回變化列表"""
        print(f"{indent}🔄 數據變化:")
        
        # 檢查重要字段的變化
        important_fields = [
            'plan', 'status', 'daily_limit_seconds', 'max_devices', 
            'current_day_used_seconds', 'stripe_subscription_id',
            'stripe_customer_id', 'amount_paid', 'payment_status'
        ]
        
        changes = []
        for field in important_fields:
            before_val = before.get(field, 'N/A')
            after_val = after.get(field, 'N/A')
            
            if before_val != after_val:
                changes.append(f"{field}: {before_val} → {after_val}")
                print(f"{indent}  {field}: {before_val} → {after_val} ✅")
        
        if not changes:
            print(f"{indent}  無變化")
        
        return changes

    def cleanup_test_data(self) -> None:
        """清理測試數據"""
        if self.test_user_id:
            try:
                # 刪除用戶記錄
                self.db.collection('users').document(self.test_user_id).delete()
                # 刪除訂閱記錄
                self.db.collection('subscriptions').document(f"sub_{self.test_user_id}").delete()
                # 刪除購買記錄
                purchases_ref = self.db.collection('purchases').where('user_id', '==', self.test_user_id)
                for doc in purchases_ref.stream():
                    doc.reference.delete()
                print(f"🧹 測試數據清理完成: {self.test_user_id}")
            except Exception as e:
                print(f"⚠️ 清理測試數據時出錯: {str(e)}")

    def save_detailed_results_to_markdown(self) -> str:
        """保存詳細測試結果到 Markdown 文件"""
        try:
            # 確保結果目錄存在
            results_dir = os.path.join(os.path.dirname(__file__), 'results')
            os.makedirs(results_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"detailed_webhook_results_{timestamp}.md"
            filepath = os.path.join(results_dir, filename)
            
            # 使用增強日誌記錄器生成報告
            test_logger.save_results_to_markdown()
            
            print(f"📄 詳細測試報告已保存到: unit_test/results/ 目錄")
            return filepath
            
        except Exception as e:
            print(f"❌ 保存詳細測試報告失敗: {str(e)}")
            return ""


class TestUnifiedDetailedWebhook(unittest.TestCase):
    """統一的詳細 Webhook 測試類"""
    
    @classmethod
    def setUpClass(cls):
        """測試類設置"""
        if db is None:
            cls.skipTest(cls, "Firebase Admin SDK 未可用")
        
        cls.framework = UnifiedDetailedWebhookFramework(db)
        
        # 創建共享測試用戶
        cls.test_user_id = cls.framework.create_shared_test_user()

    @classmethod
    def tearDownClass(cls):
        """測試類清理"""
        if hasattr(cls, 'framework'):
            # 保存詳細測試報告
            cls.framework.save_detailed_results_to_markdown()
            # 清理測試數據
            cls.framework.cleanup_test_data()

    def setUp(self):
        """每個測試的設置"""
        self.timestamp = str(int(time.time()))

    def test_01_subscription_creation_detailed(self):
        """測試案例 1: 訂閱創建詳細流程"""

        self.framework.start_detailed_test_case(
            "訂閱創建 + 支付成功流程",
            "測試用戶從 FREE 升級到 PRO 計劃的完整流程，包括訂閱創建和首次支付"
        )

        user_id = self.test_user_id
        subscription_id = f"sub_{user_id}"

        try:
            # === 步驟 1: 查詢初始狀態 ===
            initial_subscription = self.framework.query_table_before_event(
                "subscriptions",
                subscription_id,
                "查詢用戶初始訂閱狀態"
            )

            initial_user = self.framework.query_table_before_event(
                "users",
                user_id,
                "查詢用戶初始資料"
            )

            # === 步驟 2: 模擬 Stripe 訂閱創建事件 ===
            stripe_customer_id = f'stripe_cus_{self.timestamp}'
            stripe_subscription_id = f'stripe_sub_{self.timestamp}'

            subscription_event_data = {
                'subscription_id': stripe_subscription_id,
                'customer_id': stripe_customer_id,
                'plan': 'PRO',
                'status': 'active',
                'price_id': 'price_pro_monthly'
            }

            self.framework.simulate_stripe_event(
                "customer.subscription.created",
                subscription_event_data
            )

            # === 步驟 3: 調用訂閱創建處理函數 ===
            # 模擬訂閱創建處理
            self._simulate_subscription_creation(user_id, subscription_id, stripe_customer_id, stripe_subscription_id)

            function_success = self.framework.call_webhook_function(
                "handle_subscription_created",
                subscription_event_data,
                True
            )

            # === 步驟 4: 查詢訂閱創建後狀態 ===
            after_creation_subscription = self.framework.query_table_after_event(
                "subscriptions",
                subscription_id,
                initial_subscription
            )

            after_creation_user = self.framework.query_table_after_event(
                "users",
                user_id,
                initial_user
            )

            # === 模擬支付成功事件 ===
            print(f"\n🔔 步驟 2b: 接收支付成功事件")

            payment_event_data = {
                'invoice_id': f'stripe_inv_{self.timestamp}',
                'customer_id': stripe_customer_id,
                'subscription_id': stripe_subscription_id,
                'amount': 1999,  # $19.99
                'status': 'paid',
                'plan': 'PRO'
            }

            self.framework.simulate_stripe_event(
                "invoice.payment_succeeded",
                payment_event_data
            )

            # === 步驟 3b: 調用支付處理函數 ===
            # 模擬支付處理
            self._simulate_payment_processing(user_id, payment_event_data)

            payment_success = self.framework.call_webhook_function(
                "handle_payment_succeeded",
                payment_event_data,
                True
            )

            # === 步驟 4b: 查詢支付後狀態 ===
            final_subscription = self.framework.query_table_after_event(
                "subscriptions",
                subscription_id,
                after_creation_subscription
            )

            # 查詢購買記錄
            purchases_query = self.framework.db.collection('purchases').where('user_id', '==', user_id)
            purchases = list(purchases_query.stream())
            print(f"\n🔍 查詢購買記錄")
            print(f"   購買記錄數量: {len(purchases)}")

            latest_purchase = None
            if purchases:
                latest_purchase = purchases[0].to_dict()
                print(f"   最新購買記錄:")
                self.framework._print_table_data(latest_purchase, "     ")

            # === 步驟 5: 驗證結果 ===
            assertions = [
                ("初始計劃應該是 FREE", "FREE", initial_subscription.get('plan')),
                ("升級後計劃應該是 PRO", "PRO", final_subscription.get('plan')),
                ("升級後配額應該是 10800s", 10800, final_subscription.get('daily_limit_seconds')),
                ("升級後設備限制應該是 2", 2, final_subscription.get('max_devices')),
                ("升級後狀態應該是 active", "active", final_subscription.get('status')),
                ("支付後使用量應該重置為 0", 0, final_subscription.get('current_day_used_seconds')),
                ("應該創建購買記錄", True, len(purchases) > 0)
            ]

            if latest_purchase:
                assertions.extend([
                    ("購買記錄金額正確", 1999, latest_purchase.get('amount_paid')),
                    ("購買記錄計劃正確", 'PRO', latest_purchase.get('subscription_plan')),
                    ("購買記錄狀態正確", 'succeeded', latest_purchase.get('payment_status'))
                ])

            all_passed = self.framework.verify_detailed_assertions(assertions)

            if all_passed:
                summary = "✅ 訂閱創建 + 支付流程完全成功: FREE → PRO + 購買記錄創建"
                self.framework.end_detailed_test_case(True, summary)
            else:
                summary = "❌ 部分驗證失敗"
                self.framework.end_detailed_test_case(False, summary)
                self.fail(summary)

        except Exception as e:
            self.framework.end_detailed_test_case(False, f"測試執行異常: {str(e)}")
            raise

    def test_02_subscription_upgrade_detailed(self):
        """測試案例 2: 訂閱升級詳細流程"""

        self.framework.start_detailed_test_case(
            "訂閱升級 + 支付成功流程",
            "測試用戶從 PRO 升級到 PREMIUM 計劃，包括計劃變更和支付處理"
        )

        user_id = self.test_user_id
        subscription_id = f"sub_{user_id}"

        try:
            # 首先確保用戶有 PRO 訂閱
            self._ensure_pro_subscription(user_id, subscription_id)

            # === 步驟 1: 查詢升級前狀態 ===
            before_upgrade_subscription = self.framework.query_table_before_event(
                "subscriptions",
                subscription_id,
                "查詢升級前訂閱狀態"
            )

            # === 步驟 2: 模擬 Stripe 訂閱升級事件 ===
            stripe_customer_id = f'stripe_cus_{self.timestamp}'
            stripe_subscription_id = f'stripe_sub_{self.timestamp}'

            upgrade_event_data = {
                'subscription_id': stripe_subscription_id,
                'customer_id': stripe_customer_id,
                'plan': 'PREMIUM',
                'status': 'active',
                'price_id': 'price_premium_monthly'
            }

            self.framework.simulate_stripe_event(
                "customer.subscription.updated",
                upgrade_event_data
            )

            # === 步驟 3: 調用升級處理函數 ===
            # 模擬升級處理
            self._simulate_subscription_upgrade(user_id, subscription_id, 'PREMIUM')

            function_success = self.framework.call_webhook_function(
                "handle_subscription_updated",
                upgrade_event_data,
                True
            )

            # === 步驟 4: 查詢升級後狀態 ===
            after_upgrade_subscription = self.framework.query_table_after_event(
                "subscriptions",
                subscription_id,
                before_upgrade_subscription
            )

            # === 模擬升級後的支付成功事件 ===
            payment_event_data = {
                'invoice_id': f'stripe_inv_premium_{self.timestamp}',
                'customer_id': stripe_customer_id,
                'subscription_id': stripe_subscription_id,
                'amount': 5999,  # $59.99
                'status': 'paid',
                'plan': 'PREMIUM'
            }

            self.framework.simulate_stripe_event(
                "invoice.payment_succeeded",
                payment_event_data
            )

            # 模擬支付處理
            self._simulate_payment_processing(user_id, payment_event_data)

            payment_success = self.framework.call_webhook_function(
                "handle_payment_succeeded",
                payment_event_data,
                True
            )

            # === 查詢最終狀態 ===
            final_subscription = self.framework.query_table_after_event(
                "subscriptions",
                subscription_id,
                after_upgrade_subscription
            )

            # 查詢 PREMIUM 購買記錄
            purchases_query = self.framework.db.collection('purchases').where('user_id', '==', user_id).where('subscription_plan', '==', 'PREMIUM')
            premium_purchases = list(purchases_query.stream())
            print(f"\n🔍 查詢 PREMIUM 購買記錄")
            print(f"   PREMIUM 購買記錄數量: {len(premium_purchases)}")

            # === 步驟 5: 驗證結果 ===
            assertions = [
                ("升級前計劃應該是 PRO", "PRO", before_upgrade_subscription.get('plan')),
                ("升級後計劃應該是 PREMIUM", "PREMIUM", final_subscription.get('plan')),
                ("升級後配額應該是 28800s", 28800, final_subscription.get('daily_limit_seconds')),
                ("升級後設備限制應該是 5", 5, final_subscription.get('max_devices')),
                ("升級後狀態應該是 active", "active", final_subscription.get('status')),
                ("支付後使用量應該重置為 0", 0, final_subscription.get('current_day_used_seconds')),
                ("應該創建 PREMIUM 購買記錄", True, len(premium_purchases) > 0)
            ]

            if premium_purchases:
                purchase = premium_purchases[0].to_dict()
                assertions.extend([
                    ("PREMIUM 購買記錄金額正確", 5999, purchase.get('amount_paid')),
                    ("PREMIUM 購買記錄計劃正確", 'PREMIUM', purchase.get('subscription_plan'))
                ])

            all_passed = self.framework.verify_detailed_assertions(assertions)

            if all_passed:
                summary = "✅ 訂閱升級 + 支付流程完全成功: PRO → PREMIUM + 購買記錄創建"
                self.framework.end_detailed_test_case(True, summary)
            else:
                summary = "❌ 部分驗證失敗"
                self.framework.end_detailed_test_case(False, summary)
                self.fail(summary)

        except Exception as e:
            self.framework.end_detailed_test_case(False, f"測試執行異常: {str(e)}")
            raise

    def _simulate_subscription_creation(self, user_id: str, subscription_id: str, stripe_customer_id: str, stripe_subscription_id: str):
        """模擬訂閱創建處理"""
        subscription_data = {
            'user_id': user_id,
            'plan': 'PRO',
            'status': 'active',
            'daily_limit_seconds': 10800,
            'max_devices': 2,
            'current_day_used_seconds': 0,
            'stripe_subscription_id': stripe_subscription_id,
            'stripe_customer_id': stripe_customer_id,
            'updated_at': datetime.now(timezone.utc)
        }
        self.framework.db.collection('subscriptions').document(subscription_id).update(subscription_data)

    def _simulate_subscription_upgrade(self, user_id: str, subscription_id: str, new_plan: str):
        """模擬訂閱升級處理"""
        plan_configs = {
            'PREMIUM': {'daily_limit_seconds': 28800, 'max_devices': 5}
        }

        config = plan_configs.get(new_plan, {})
        subscription_data = {
            'plan': new_plan,
            'daily_limit_seconds': config.get('daily_limit_seconds', 10800),
            'max_devices': config.get('max_devices', 2),
            'current_day_used_seconds': 0,  # 升級時重置
            'updated_at': datetime.now(timezone.utc)
        }
        self.framework.db.collection('subscriptions').document(subscription_id).update(subscription_data)

    def _simulate_payment_processing(self, user_id: str, payment_data: Dict[str, Any]):
        """模擬支付處理"""
        # 創建購買記錄
        purchase_data = {
            'user_id': user_id,
            'stripe_invoice_id': payment_data['invoice_id'],
            'stripe_customer_id': payment_data['customer_id'],
            'stripe_subscription_id': payment_data['subscription_id'],
            'amount_paid': payment_data['amount'],
            'subscription_plan': payment_data['plan'],
            'payment_status': 'succeeded',
            'created_at': datetime.now(timezone.utc)
        }

        purchase_id = f"purchase_{user_id}_{self.timestamp}"
        self.framework.db.collection('purchases').document(purchase_id).set(purchase_data)

        # 重置配額
        subscription_id = f"sub_{user_id}"
        self.framework.db.collection('subscriptions').document(subscription_id).update({
            'current_day_used_seconds': 0,
            'updated_at': datetime.now(timezone.utc)
        })

    def _ensure_pro_subscription(self, user_id: str, subscription_id: str):
        """確保用戶有 PRO 訂閱"""
        subscription_data = {
            'user_id': user_id,
            'plan': 'PRO',
            'status': 'active',
            'daily_limit_seconds': 10800,
            'max_devices': 2,
            'current_day_used_seconds': 1800,  # 已使用 30 分鐘
            'stripe_subscription_id': f'stripe_sub_{self.timestamp}',
            'stripe_customer_id': f'stripe_cus_{self.timestamp}',
            'updated_at': datetime.now(timezone.utc)
        }
        self.framework.db.collection('subscriptions').document(subscription_id).update(subscription_data)

if __name__ == '__main__':
    unittest.main()
