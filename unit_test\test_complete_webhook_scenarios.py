"""
完整的 Webhook 場景測試
包含詳細日誌記錄和 purchases 表驗證
"""

import unittest
import sys
import os
import json
import time
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend-api'))

# 導入增強的日誌記錄器
from test_enhanced_logging import test_logger, db

class CompleteWebhookTestFramework:
    """完整的 Webhook 測試框架"""
    
    def __init__(self, db):
        self.db = db
        self.test_users = []
        self.timestamp = str(int(time.time()))
        
    def create_test_user(self, user_id: str, stripe_customer_id: str, initial_plan: str = "FREE") -> Dict[str, Any]:
        """創建測試用戶"""
        user_data = {
            'email': f'{user_id}@test.com',
            'display_name': f'Test User {user_id}',
            'stripe_customer_id': stripe_customer_id,
            'current_subscription_id': f'sub_{user_id}',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        
        # 創建用戶記錄
        self.db.collection('users').document(user_id).set(user_data)
        
        # 創建初始訂閱記錄
        subscription_data = self.get_plan_config(initial_plan, user_id, stripe_customer_id)
        self.db.collection('subscriptions').document(f'sub_{user_id}').set(subscription_data)
        
        self.test_users.append(user_id)
        
        # 記錄數據庫變更
        test_logger.log_database_change('users', 'INSERT', {}, user_data, user_id)
        test_logger.log_database_change('subscriptions', 'INSERT', {}, subscription_data, user_id)
        
        return user_data
    
    def get_plan_config(self, plan: str, user_id: str, stripe_customer_id: str = None) -> Dict[str, Any]:
        """獲取計劃配置"""
        plan_configs = {
            "FREE": {"daily_limit_seconds": 600, "max_devices": 1},
            "STARTER": {"daily_limit_seconds": 3600, "max_devices": 1},
            "PRO": {"daily_limit_seconds": 10800, "max_devices": 2},
            "PREMIUM": {"daily_limit_seconds": 28800, "max_devices": 5},
            "MAX": {"daily_limit_seconds": -1, "max_devices": -1}
        }
        
        config = plan_configs.get(plan, plan_configs["FREE"])
        now = datetime.utcnow()
        
        return {
            'user_id': user_id,
            'plan': plan,
            'status': 'active',
            'daily_limit_seconds': config['daily_limit_seconds'],
            'max_devices': config['max_devices'],
            'current_day_used_seconds': 0,
            'last_daily_reset_date': now.strftime('%Y-%m-%d'),
            'last_daily_reset_at': now,
            'register_region_timezone': 'UTC+8',
            'usage_reset_hour': 0,
            'active_device_ids': [],
            'stripe_customer_id': stripe_customer_id,
            'created_at': now,
            'updated_at': now
        }
    
    def simulate_subscription_created_webhook(self, user_id: str, stripe_customer_id: str, 
                                            stripe_subscription_id: str, plan: str = "PRO"):
        """模擬訂閱創建 Webhook"""
        # 記錄 Webhook 調用
        subscription_data = {
            'id': stripe_subscription_id,
            'customer': stripe_customer_id,
            'status': 'active',
            'current_period_start': int(time.time()),
            'current_period_end': int(time.time()) + 30 * 24 * 3600,
            'cancel_at_period_end': False,
            'items': {
                'data': [{'price': {'id': f'price_test_{plan.lower()}_monthly', 'recurring': {'interval': 'month'}}}]
            }
        }
        
        test_logger.log_webhook_call(
            'customer.subscription.created',
            {'subscription': subscription_data},
            f"用戶 {user_id} 創建 {plan} 計劃訂閱"
        )
        
        # 獲取變更前數據
        before_subscription = self.get_subscription_data(user_id)
        
        # 執行訂閱創建邏輯
        plan_config = self.get_plan_config(plan, user_id, stripe_customer_id)
        now = datetime.utcnow()
        
        subscription_ref = self.db.collection('subscriptions').document(f'sub_{user_id}')
        update_data = {
            'plan': plan,
            'status': 'active',
            'daily_limit_seconds': plan_config['daily_limit_seconds'],
            'max_devices': plan_config['max_devices'],
            'current_day_used_seconds': 0,  # 立即重置配額
            'stripe_customer_id': stripe_customer_id,
            'stripe_subscription_id': stripe_subscription_id,
            'billing_cycle': 'monthly',
            'auto_renew': True,
            'current_period_start': datetime.fromtimestamp(subscription_data['current_period_start'], tz=timezone.utc),
            'current_period_end': datetime.fromtimestamp(subscription_data['current_period_end'], tz=timezone.utc),
            'next_billing_date': datetime.fromtimestamp(subscription_data['current_period_end'], tz=timezone.utc),
            'last_daily_reset_date': now.strftime('%Y-%m-%d'),
            'last_daily_reset_at': now,
            'webhook_events': {'customer.subscription.created': now},
            'updated_at': now
        }
        
        subscription_ref.update(update_data)
        
        # 獲取變更後數據
        after_subscription = self.get_subscription_data(user_id)
        
        # 記錄數據庫變更
        test_logger.log_database_change('subscriptions', 'UPDATE', before_subscription, after_subscription, user_id)
        
        return subscription_data
    
    def simulate_payment_succeeded_webhook(self, user_id: str, stripe_customer_id: str,
                                         stripe_subscription_id: str, plan: str = "PRO", amount: int = 1999):
        """模擬支付成功 Webhook"""
        invoice_id = f'in_test_{self.timestamp}_{user_id}'
        
        # 記錄 Webhook 調用
        invoice_data = {
            'id': invoice_id,
            'customer': stripe_customer_id,
            'subscription': stripe_subscription_id,
            'status': 'paid',
            'amount_paid': amount,
            'amount_due': 0,
            'currency': 'usd',
            'billing_reason': 'subscription_cycle',
            'created': int(time.time()),
            'period_start': int(time.time()),
            'period_end': int(time.time()) + 30 * 24 * 3600,
            'customer_email': f'{user_id}@test.com',
            'lines': {
                'data': [{
                    'type': 'subscription',
                    'price': {
                        'id': f'price_test_{plan.lower()}_monthly',
                        'unit_amount': amount,
                        'recurring': {'interval': 'month'}
                    }
                }]
            }
        }
        
        test_logger.log_webhook_call(
            'invoice.payment_succeeded',
            {'invoice': invoice_data},
            f"用戶 {user_id} 支付成功，金額 ${amount/100:.2f}"
        )
        
        # 獲取變更前數據
        before_subscription = self.get_subscription_data(user_id)
        before_purchases = self.get_user_purchases(user_id)
        
        # 創建購買記錄
        now = datetime.utcnow()
        purchase_data = {
            'stripe_invoice_id': invoice_id,
            'stripe_customer_id': stripe_customer_id,
            'stripe_subscription_id': stripe_subscription_id,
            'amount_total': amount,
            'amount_paid': amount,
            'amount_due': 0,
            'currency': 'USD',
            'customer_email': f'{user_id}@test.com',
            'invoice_status': 'paid',
            'billing_reason': 'subscription_cycle',
            'payment_status': 'succeeded',
            'subscription_plan': plan,
            'subscription_period': 'monthly',
            'unit_amount': amount,
            'webhook_event_type': 'invoice.payment_succeeded',
            'processed': True,
            'user_id': user_id,
            'source_app': 'webhook',
            'created_at': now,
            'updated_at': now
        }
        
        # 保存購買記錄
        purchase_ref = self.db.collection('purchases').document()
        purchase_ref.set(purchase_data)
        
        # 重置每日配額（新的計費週期開始）
        subscription_ref = self.db.collection('subscriptions').document(f'sub_{user_id}')
        subscription_ref.update({
            'current_day_used_seconds': 0,
            'last_daily_reset_date': now.strftime('%Y-%m-%d'),
            'last_daily_reset_at': now,
            'webhook_events.invoice.payment_succeeded': now,
            'updated_at': now
        })
        
        # 獲取變更後數據
        after_subscription = self.get_subscription_data(user_id)
        after_purchases = self.get_user_purchases(user_id)
        
        # 記錄數據庫變更
        test_logger.log_database_change('purchases', 'INSERT', {}, purchase_data, user_id)
        test_logger.log_database_change('subscriptions', 'UPDATE', before_subscription, after_subscription, user_id)
        
        return invoice_data, purchase_data
    
    def simulate_subscription_updated_webhook(self, user_id: str, stripe_customer_id: str,
                                            stripe_subscription_id: str, old_plan: str, new_plan: str):
        """模擬訂閱更新 Webhook"""
        # 記錄 Webhook 調用
        subscription_data = {
            'id': stripe_subscription_id,
            'customer': stripe_customer_id,
            'status': 'active',
            'current_period_start': int(time.time()),
            'current_period_end': int(time.time()) + 30 * 24 * 3600,
            'cancel_at_period_end': False,
            'items': {
                'data': [{'price': {'id': f'price_test_{new_plan.lower()}_monthly', 'recurring': {'interval': 'month'}}}]
            }
        }
        
        previous_attributes = {
            'items': {
                'data': [{'price': {'id': f'price_test_{old_plan.lower()}_monthly'}}]
            }
        }
        
        test_logger.log_webhook_call(
            'customer.subscription.updated',
            {'subscription': subscription_data, 'previous_attributes': previous_attributes},
            f"用戶 {user_id} 計劃變更: {old_plan} → {new_plan}"
        )
        
        # 獲取變更前數據
        before_subscription = self.get_subscription_data(user_id)
        
        # 執行升級處理
        now = datetime.utcnow()
        new_config = self.get_plan_config(new_plan, user_id, stripe_customer_id)
        
        subscription_ref = self.db.collection('subscriptions').document(f'sub_{user_id}')
        subscription_ref.update({
            'plan': new_plan,
            'daily_limit_seconds': new_config['daily_limit_seconds'],
            'max_devices': new_config['max_devices'],
            'current_day_used_seconds': 0,  # 升級時重置配額
            'last_daily_reset_date': now.strftime('%Y-%m-%d'),
            'last_daily_reset_at': now,
            'webhook_events.customer.subscription.updated': now,
            'updated_at': now
        })
        
        # 獲取變更後數據
        after_subscription = self.get_subscription_data(user_id)
        
        # 記錄數據庫變更
        test_logger.log_database_change('subscriptions', 'UPDATE', before_subscription, after_subscription, user_id)
        
        return subscription_data
    
    def simulate_subscription_deleted_webhook(self, user_id: str, stripe_customer_id: str, stripe_subscription_id: str):
        """模擬訂閱刪除 Webhook"""
        # 記錄 Webhook 調用
        subscription_data = {
            'id': stripe_subscription_id,
            'customer': stripe_customer_id,
            'status': 'canceled',
            'canceled_at': int(time.time()),
            'current_period_start': int(time.time()) - 15 * 24 * 3600,
            'current_period_end': int(time.time()),
        }
        
        test_logger.log_webhook_call(
            'customer.subscription.deleted',
            {'subscription': subscription_data},
            f"用戶 {user_id} 訂閱取消，降級到 FREE 計劃"
        )
        
        # 獲取變更前數據
        before_subscription = self.get_subscription_data(user_id)
        
        # 執行取消處理 - 降級到 FREE
        now = datetime.utcnow()
        
        subscription_ref = self.db.collection('subscriptions').document(f'sub_{user_id}')
        subscription_ref.update({
            'plan': 'FREE',
            'status': 'canceled',
            'daily_limit_seconds': 600,  # FREE 計劃 10 分鐘
            'max_devices': 1,
            'current_day_used_seconds': 0,  # 重置配額
            'stripe_subscription_id': None,
            'billing_cycle': None,
            'auto_renew': False,
            'next_billing_date': None,
            'cancel_at_period_end': False,
            'last_daily_reset_date': now.strftime('%Y-%m-%d'),
            'last_daily_reset_at': now,
            'webhook_events.customer.subscription.deleted': now,
            'updated_at': now
        })
        
        # 獲取變更後數據
        after_subscription = self.get_subscription_data(user_id)
        
        # 記錄數據庫變更
        test_logger.log_database_change('subscriptions', 'UPDATE', before_subscription, after_subscription, user_id)
        
        return subscription_data
    
    def get_subscription_data(self, user_id: str) -> Optional[Dict[str, Any]]:
        """獲取訂閱數據"""
        subscription_ref = self.db.collection('subscriptions').document(f'sub_{user_id}')
        subscription_doc = subscription_ref.get()
        return subscription_doc.to_dict() if subscription_doc.exists else None
    
    def get_user_data(self, user_id: str) -> Optional[Dict[str, Any]]:
        """獲取用戶數據"""
        user_ref = self.db.collection('users').document(user_id)
        user_doc = user_ref.get()
        return user_doc.to_dict() if user_doc.exists else None
    
    def get_user_purchases(self, user_id: str) -> List[Dict[str, Any]]:
        """獲取用戶的購買記錄"""
        purchases_ref = self.db.collection('purchases')
        # 簡化查詢，避免索引問題
        query = purchases_ref.where('user_id', '==', user_id)
        docs = query.stream()
        purchases = [doc.to_dict() for doc in docs]
        # 在內存中排序
        return sorted(purchases, key=lambda x: x.get('created_at', datetime.min), reverse=True)
    
    def cleanup_test_data(self):
        """清理測試數據"""
        for user_id in self.test_users:
            # 刪除用戶記錄
            self.db.collection('users').document(user_id).delete()
            # 刪除訂閱記錄
            self.db.collection('subscriptions').document(f'sub_{user_id}').delete()
            # 刪除購買記錄
            purchases = self.db.collection('purchases').where('user_id', '==', user_id).stream()
            for purchase in purchases:
                purchase.reference.delete()
        
        self.test_users.clear()

class TestCompleteWebhookScenarios(unittest.TestCase):
    """完整的 Webhook 場景測試"""

    @classmethod
    def setUpClass(cls):
        """測試類設置"""
        if db is None:
            cls.skipTest(cls, "Firebase Admin SDK 未可用")
        cls.framework = CompleteWebhookTestFramework(db)

    @classmethod
    def tearDownClass(cls):
        """測試類清理"""
        if hasattr(cls, 'framework'):
            cls.framework.cleanup_test_data()

        # 保存測試結果到 Markdown
        test_logger.save_results_to_markdown()

    def setUp(self):
        """每個測試的設置"""
        self.timestamp = str(int(time.time()))

    def test_01_subscription_creation_with_payment(self):
        """測試訂閱創建 + 支付成功完整流程"""
        test_logger.start_scenario(
            "訂閱創建 + 支付成功流程",
            "測試用戶從 FREE 升級到 PRO 計劃的完整流程，包括訂閱創建和首次支付"
        )

        try:
            user_id = f"create_pay_test_{self.timestamp}"
            stripe_customer_id = f"cus_test_{self.timestamp}"
            stripe_subscription_id = f"sub_test_{self.timestamp}"

            # 1. 創建測試用戶 (FREE 計劃)
            self.framework.create_test_user(user_id, stripe_customer_id, "FREE")

            # 驗證初始狀態
            initial_subscription = self.framework.get_subscription_data(user_id)
            test_logger.log_assertion(
                "初始計劃應該是 FREE",
                "FREE",
                initial_subscription['plan'],
                initial_subscription['plan'] == "FREE"
            )

            # 2. 模擬訂閱創建 Webhook (升級到 PRO)
            self.framework.simulate_subscription_created_webhook(
                user_id, stripe_customer_id, stripe_subscription_id, "PRO"
            )

            # 驗證訂閱創建後狀態
            after_creation = self.framework.get_subscription_data(user_id)
            test_logger.log_assertion(
                "訂閱創建後計劃應該是 PRO",
                "PRO",
                after_creation['plan'],
                after_creation['plan'] == "PRO"
            )

            test_logger.log_assertion(
                "訂閱創建後配額應該是 10800s (3小時)",
                10800,
                after_creation['daily_limit_seconds'],
                after_creation['daily_limit_seconds'] == 10800
            )

            test_logger.log_assertion(
                "訂閱創建後設備限制應該是 2",
                2,
                after_creation['max_devices'],
                after_creation['max_devices'] == 2
            )

            # 3. 模擬支付成功 Webhook
            invoice_data, purchase_data = self.framework.simulate_payment_succeeded_webhook(
                user_id, stripe_customer_id, stripe_subscription_id, "PRO", 1999
            )

            # 驗證購買記錄創建
            purchases = self.framework.get_user_purchases(user_id)
            test_logger.log_assertion(
                "應該創建 1 條購買記錄",
                1,
                len(purchases),
                len(purchases) == 1
            )

            if purchases:
                purchase = purchases[0]
                test_logger.log_assertion(
                    "購買記錄的發票 ID 應該正確",
                    invoice_data['id'],
                    purchase['stripe_invoice_id'],
                    purchase['stripe_invoice_id'] == invoice_data['id']
                )

                test_logger.log_assertion(
                    "購買記錄的支付金額應該是 $19.99",
                    1999,
                    purchase['amount_paid'],
                    purchase['amount_paid'] == 1999
                )

                test_logger.log_assertion(
                    "購買記錄的計劃應該是 PRO",
                    "PRO",
                    purchase['subscription_plan'],
                    purchase['subscription_plan'] == "PRO"
                )

                test_logger.log_assertion(
                    "購買記錄的支付狀態應該是 succeeded",
                    "succeeded",
                    purchase['payment_status'],
                    purchase['payment_status'] == "succeeded"
                )

            # 驗證支付後配額重置
            after_payment = self.framework.get_subscription_data(user_id)
            test_logger.log_assertion(
                "支付成功後當日使用量應該重置為 0",
                0,
                after_payment['current_day_used_seconds'],
                after_payment['current_day_used_seconds'] == 0
            )

            test_logger.end_scenario(True)

        except Exception as e:
            test_logger.end_scenario(False, str(e))
            raise

    def test_02_subscription_upgrade_with_payment(self):
        """測試訂閱升級 + 支付成功流程"""
        test_logger.start_scenario(
            "訂閱升級 + 支付成功流程",
            "測試用戶從 PRO 升級到 PREMIUM 計劃，包括計劃變更和支付處理"
        )

        try:
            user_id = f"upgrade_pay_test_{self.timestamp}"
            stripe_customer_id = f"cus_test_{self.timestamp}"
            stripe_subscription_id = f"sub_test_{self.timestamp}"

            # 1. 創建測試用戶並設置為 PRO 計劃
            self.framework.create_test_user(user_id, stripe_customer_id, "FREE")
            self.framework.simulate_subscription_created_webhook(
                user_id, stripe_customer_id, stripe_subscription_id, "PRO"
            )

            # 設置一些使用量
            subscription_ref = db.collection('subscriptions').document(f'sub_{user_id}')
            subscription_ref.update({'current_day_used_seconds': 3600})  # 1 小時

            # 驗證升級前狀態
            before_upgrade = self.framework.get_subscription_data(user_id)
            test_logger.log_assertion(
                "升級前計劃應該是 PRO",
                "PRO",
                before_upgrade['plan'],
                before_upgrade['plan'] == "PRO"
            )

            # 2. 模擬訂閱升級 Webhook (PRO → PREMIUM)
            self.framework.simulate_subscription_updated_webhook(
                user_id, stripe_customer_id, stripe_subscription_id, "PRO", "PREMIUM"
            )

            # 驗證升級後狀態
            after_upgrade = self.framework.get_subscription_data(user_id)
            test_logger.log_assertion(
                "升級後計劃應該是 PREMIUM",
                "PREMIUM",
                after_upgrade['plan'],
                after_upgrade['plan'] == "PREMIUM"
            )

            test_logger.log_assertion(
                "升級後配額應該增加到 28800s (8小時)",
                28800,
                after_upgrade['daily_limit_seconds'],
                after_upgrade['daily_limit_seconds'] == 28800
            )

            test_logger.log_assertion(
                "升級後設備限制應該增加到 5",
                5,
                after_upgrade['max_devices'],
                after_upgrade['max_devices'] == 5
            )

            test_logger.log_assertion(
                "升級時使用量應該重置為 0",
                0,
                after_upgrade['current_day_used_seconds'],
                after_upgrade['current_day_used_seconds'] == 0
            )

            # 3. 模擬升級後的支付成功 Webhook
            invoice_data, purchase_data = self.framework.simulate_payment_succeeded_webhook(
                user_id, stripe_customer_id, stripe_subscription_id, "PREMIUM", 5999
            )

            # 驗證購買記錄
            purchases = self.framework.get_user_purchases(user_id)
            premium_purchases = [p for p in purchases if p['subscription_plan'] == 'PREMIUM']

            test_logger.log_assertion(
                "應該有 PREMIUM 計劃的購買記錄",
                True,
                len(premium_purchases) > 0,
                len(premium_purchases) > 0
            )

            if premium_purchases:
                purchase = premium_purchases[0]
                test_logger.log_assertion(
                    "PREMIUM 購買記錄的金額應該是 $59.99",
                    5999,
                    purchase['amount_paid'],
                    purchase['amount_paid'] == 5999
                )

            test_logger.end_scenario(True)

        except Exception as e:
            test_logger.end_scenario(False, str(e))
            raise

    def test_03_subscription_cancellation_flow(self):
        """測試訂閱取消流程"""
        test_logger.start_scenario(
            "訂閱取消流程",
            "測試用戶取消 PREMIUM 訂閱，自動降級到 FREE 計劃"
        )

        try:
            user_id = f"cancel_test_{self.timestamp}"
            stripe_customer_id = f"cus_test_{self.timestamp}"
            stripe_subscription_id = f"sub_test_{self.timestamp}"

            # 1. 創建測試用戶並設置為 PREMIUM 計劃
            self.framework.create_test_user(user_id, stripe_customer_id, "FREE")
            self.framework.simulate_subscription_created_webhook(
                user_id, stripe_customer_id, stripe_subscription_id, "PREMIUM"
            )

            # 添加一些設備和使用量
            subscription_ref = db.collection('subscriptions').document(f'sub_{user_id}')
            subscription_ref.update({
                'current_day_used_seconds': 7200,  # 2 小時
                'active_device_ids': [f'device_1_{user_id}', f'device_2_{user_id}', f'device_3_{user_id}']
            })

            # 驗證取消前狀態
            before_cancel = self.framework.get_subscription_data(user_id)
            test_logger.log_assertion(
                "取消前計劃應該是 PREMIUM",
                "PREMIUM",
                before_cancel['plan'],
                before_cancel['plan'] == "PREMIUM"
            )

            test_logger.log_assertion(
                "取消前應該有 3 個設備",
                3,
                len(before_cancel['active_device_ids']),
                len(before_cancel['active_device_ids']) == 3
            )

            # 2. 模擬訂閱取消 Webhook
            self.framework.simulate_subscription_deleted_webhook(
                user_id, stripe_customer_id, stripe_subscription_id
            )

            # 驗證取消後狀態
            after_cancel = self.framework.get_subscription_data(user_id)
            test_logger.log_assertion(
                "取消後計劃應該降級到 FREE",
                "FREE",
                after_cancel['plan'],
                after_cancel['plan'] == "FREE"
            )

            test_logger.log_assertion(
                "取消後狀態應該是 canceled",
                "canceled",
                after_cancel['status'],
                after_cancel['status'] == "canceled"
            )

            test_logger.log_assertion(
                "取消後配額應該降到 600s (10分鐘)",
                600,
                after_cancel['daily_limit_seconds'],
                after_cancel['daily_limit_seconds'] == 600
            )

            test_logger.log_assertion(
                "取消後設備限制應該降到 1",
                1,
                after_cancel['max_devices'],
                after_cancel['max_devices'] == 1
            )

            test_logger.log_assertion(
                "取消後使用量應該重置為 0",
                0,
                after_cancel['current_day_used_seconds'],
                after_cancel['current_day_used_seconds'] == 0
            )

            test_logger.log_assertion(
                "取消後 Stripe 訂閱 ID 應該清空",
                None,
                after_cancel.get('stripe_subscription_id'),
                after_cancel.get('stripe_subscription_id') is None
            )

            # 設備列表仍然存在但超過限制
            devices_after = after_cancel.get('active_device_ids', [])
            if len(devices_after) > 1:
                print(f"    ⚠️ 設備超限警告: {len(devices_after)} 個設備超過 FREE 計劃限制 1 個")

            test_logger.end_scenario(True)

        except Exception as e:
            test_logger.end_scenario(False, str(e))
            raise

    def test_04_multiple_payments_tracking(self):
        """測試多次支付記錄追蹤"""
        test_logger.start_scenario(
            "多次支付記錄追蹤",
            "測試用戶多次支付的購買記錄正確創建和追蹤"
        )

        try:
            user_id = f"multi_pay_test_{self.timestamp}"
            stripe_customer_id = f"cus_test_{self.timestamp}"
            stripe_subscription_id = f"sub_test_{self.timestamp}"

            # 1. 創建測試用戶並設置為 PRO 計劃
            self.framework.create_test_user(user_id, stripe_customer_id, "FREE")
            self.framework.simulate_subscription_created_webhook(
                user_id, stripe_customer_id, stripe_subscription_id, "PRO"
            )

            # 2. 模擬第一次支付 (PRO 月費)
            invoice_1, purchase_1 = self.framework.simulate_payment_succeeded_webhook(
                user_id, stripe_customer_id, stripe_subscription_id, "PRO", 1999
            )

            # 3. 模擬升級到 PREMIUM
            self.framework.simulate_subscription_updated_webhook(
                user_id, stripe_customer_id, stripe_subscription_id, "PRO", "PREMIUM"
            )

            # 4. 模擬第二次支付 (PREMIUM 升級費用)
            invoice_2, purchase_2 = self.framework.simulate_payment_succeeded_webhook(
                user_id, stripe_customer_id, stripe_subscription_id, "PREMIUM", 5999
            )

            # 5. 模擬第三次支付 (PREMIUM 月費續費)
            time.sleep(1)  # 確保時間戳不同
            invoice_3, purchase_3 = self.framework.simulate_payment_succeeded_webhook(
                user_id, stripe_customer_id, stripe_subscription_id, "PREMIUM", 5999
            )

            # 驗證購買記錄
            purchases = self.framework.get_user_purchases(user_id)
            test_logger.log_assertion(
                "應該有 3 條購買記錄",
                3,
                len(purchases),
                len(purchases) == 3
            )

            # 驗證 PRO 計劃購買記錄
            pro_purchases = [p for p in purchases if p['subscription_plan'] == 'PRO']
            test_logger.log_assertion(
                "應該有 1 條 PRO 計劃購買記錄",
                1,
                len(pro_purchases),
                len(pro_purchases) == 1
            )

            # 驗證 PREMIUM 計劃購買記錄
            premium_purchases = [p for p in purchases if p['subscription_plan'] == 'PREMIUM']
            test_logger.log_assertion(
                "應該有 2 條 PREMIUM 計劃購買記錄",
                2,
                len(premium_purchases),
                len(premium_purchases) == 2
            )

            # 驗證總支付金額
            total_amount = sum(p['amount_paid'] for p in purchases)
            expected_total = 1999 + 5999 + 5999  # PRO + PREMIUM + PREMIUM 續費
            test_logger.log_assertion(
                f"總支付金額應該是 ${expected_total/100:.2f}",
                expected_total,
                total_amount,
                total_amount == expected_total
            )

            # 驗證所有購買記錄都有正確的用戶 ID
            all_user_ids_correct = all(p['user_id'] == user_id for p in purchases)
            test_logger.log_assertion(
                "所有購買記錄的用戶 ID 應該正確",
                True,
                all_user_ids_correct,
                all_user_ids_correct
            )

            # 驗證所有購買記錄都是成功狀態
            all_succeeded = all(p['payment_status'] == 'succeeded' for p in purchases)
            test_logger.log_assertion(
                "所有購買記錄的支付狀態應該是 succeeded",
                True,
                all_succeeded,
                all_succeeded
            )

            test_logger.end_scenario(True)

        except Exception as e:
            test_logger.end_scenario(False, str(e))
            raise

def run_complete_webhook_tests():
    """執行完整的 Webhook 測試"""
    print("🧪 開始完整的 Webhook 場景測試...")
    print("=" * 80)

    if db is None:
        print("❌ Firebase Admin SDK 未可用，跳過測試")
        return False

    # 創建測試套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestCompleteWebhookScenarios)

    # 執行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    print("=" * 80)

    if result.wasSuccessful():
        print("🎉 所有完整 Webhook 測試通過！")
        print("\n📋 測試場景總結:")
        print("✅ 訂閱創建 + 支付成功流程")
        print("✅ 訂閱升級 + 支付成功流程")
        print("✅ 訂閱取消流程")
        print("✅ 多次支付記錄追蹤")

        print("\n🔍 數據庫驗證完成:")
        print("  - Webhook 事件正確處理")
        print("  - 訂閱狀態正確更新")
        print("  - 購買記錄正確創建")
        print("  - 配額限制正確調整")
        print("  - 設備限制正確更新")
        print("  - purchases 表記錄完整")

        print("\n📄 詳細測試報告已保存到 unit_test/results/ 目錄")
        return True
    else:
        print(f"❌ 測試失敗: {len(result.failures)} 個失敗, {len(result.errors)} 個錯誤")
        return False

if __name__ == '__main__':
    success = run_complete_webhook_tests()
    sys.exit(0 if success else 1)
