"""
用戶訂閱關聯同步模組 - 確保雙向關聯一致性
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional
from firebase_admin import firestore
from .environment import get_db
from .constants import SUBSCRIPTION_PLANS

logger = logging.getLogger(__name__)

class UserSubscriptionSync:
    """用戶訂閱同步管理器"""
    
    def __init__(self):
        self.db = get_db()
    
    def create_subscription_with_sync(self, user_id: str, plan: str, source_app: str = 'desktop', **kwargs) -> Dict[str, Any]:
        """
        創建訂閱並同步用戶關聯
        
        Args:
            user_id: 用戶ID
            plan: 訂閱計劃
            source_app: 來源應用
            **kwargs: 其他訂閱參數
            
        Returns:
            Dict[str, Any]: 創建結果
        """
        try:
            now = datetime.utcnow()
            subscription_id = f"sub_{user_id}"
            
            # 獲取計劃配置
            plan_config = SUBSCRIPTION_PLANS.get(plan, SUBSCRIPTION_PLANS['FREE'])
            
            # 創建訂閱記錄
            subscription_data = {
                'user_id': user_id,
                'plan': plan,
                'status': 'active',
                
                # 配額設定（單一數據源）
                'daily_limit_seconds': plan_config['daily_limit_seconds'],
                'current_day_used_seconds': 0,
                'last_daily_reset_date': now.strftime('%Y-%m-%d'),
                'last_daily_reset_at': now,
                
                # 設備管理
                'max_devices': plan_config['max_devices'],
                'active_device_ids': [],
                'device_limit_exceeded_count': 0,
                'last_device_limit_check': now,
                
                # 地區化設定
                'register_region': kwargs.get('register_region', 'Hong Kong'),
                'register_region_timezone': kwargs.get('register_region_timezone', 'UTC+8'),
                'usage_reset_hour': kwargs.get('usage_reset_hour', 0),
                
                # 平台支援
                'supported_platforms': plan_config['supported_platforms'],
                'features': plan_config['features'],
                
                # Stripe 資訊（如果有）
                'stripe_customer_id': kwargs.get('stripe_customer_id'),
                'stripe_subscription_id': kwargs.get('stripe_subscription_id'),
                'billing_cycle': kwargs.get('billing_cycle', 'monthly'),
                'price_amount': plan_config.get('price_monthly', 0),
                'currency': kwargs.get('currency', 'USD'),
                'auto_renew': kwargs.get('auto_renew', True),
                'next_billing_date': kwargs.get('next_billing_date'),
                'cancel_at_period_end': kwargs.get('cancel_at_period_end', False),
                
                # 元數據
                'created_at': now,
                'updated_at': now,
                'created_by': 'system',
                'source_app': source_app
            }
            
            # 使用事務確保一致性
            @firestore.transactional
            def create_with_sync(transaction):
                # 創建訂閱
                subscription_ref = self.db.collection('subscriptions').document(subscription_id)
                transaction.set(subscription_ref, subscription_data)
                
                # 更新用戶關聯
                user_ref = self.db.collection('users').document(user_id)
                user_doc = user_ref.get(transaction=transaction)
                
                if user_doc.exists:
                    # 更新現有用戶
                    transaction.update(user_ref, {
                        'current_subscription_id': subscription_id,
                        'updated_at': now
                    })
                else:
                    # 創建新用戶（如果不存在）
                    user_data = {
                        'current_subscription_id': subscription_id,
                        'device_ids': [],
                        'onboarding_completed': False,
                        'first_device_registered': False,
                        'last_device_check': now,
                        'preferences': {
                            'language': 'zh-TW',
                            'notifications': True,
                            'auto_upgrade_prompts': True
                        },
                        'created_at': now,
                        'updated_at': now
                    }
                    transaction.set(user_ref, user_data)
                
                return subscription_data
            
            transaction = self.db.transaction()
            result = create_with_sync(transaction)
            
            logger.info(f"訂閱創建並同步成功: {user_id} -> {plan}")
            
            return {
                'success': True,
                'subscription_id': subscription_id,
                'subscription_data': result
            }
            
        except Exception as e:
            logger.error(f"創建訂閱失敗: {user_id}, 錯誤: {str(e)}")
            raise
    
    def update_subscription_with_sync(self, subscription_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新訂閱並同步相關資料
        
        Args:
            subscription_id: 訂閱ID
            updates: 更新資料
            
        Returns:
            Dict[str, Any]: 更新結果
        """
        try:
            now = datetime.utcnow()
            updates['updated_at'] = now
            
            # 使用事務確保一致性
            @firestore.transactional
            def update_with_sync(transaction):
                subscription_ref = self.db.collection('subscriptions').document(subscription_id)
                subscription_doc = subscription_ref.get(transaction=transaction)
                
                if not subscription_doc.exists:
                    raise ValueError(f"訂閱不存在: {subscription_id}")
                
                subscription_data = subscription_doc.to_dict()
                user_id = subscription_data['user_id']
                
                # 更新訂閱
                transaction.update(subscription_ref, updates)
                
                # 如果計劃變更，需要同步設備限制等資訊
                if 'plan' in updates:
                    new_plan = updates['plan']
                    plan_config = SUBSCRIPTION_PLANS.get(new_plan, SUBSCRIPTION_PLANS['FREE'])
                    
                    # 更新計劃相關配置
                    plan_updates = {
                        'daily_limit_seconds': plan_config['daily_limit_seconds'],
                        'max_devices': plan_config['max_devices'],
                        'supported_platforms': plan_config['supported_platforms'],
                        'features': plan_config['features']
                    }
                    transaction.update(subscription_ref, plan_updates)
                
                # 更新用戶表的相關資訊（如果需要）
                user_ref = self.db.collection('users').document(user_id)
                user_updates = {'updated_at': now}
                
                # 如果狀態變更為取消，清除用戶關聯
                if updates.get('status') == 'cancelled':
                    user_updates['current_subscription_id'] = None
                
                transaction.update(user_ref, user_updates)
                
                return subscription_data
            
            transaction = self.db.transaction()
            original_data = update_with_sync(transaction)
            
            logger.info(f"訂閱更新並同步成功: {subscription_id}")
            
            return {
                'success': True,
                'subscription_id': subscription_id,
                'updates': updates
            }
            
        except Exception as e:
            logger.error(f"更新訂閱失敗: {subscription_id}, 錯誤: {str(e)}")
            raise
    
    def cancel_subscription_with_sync(self, subscription_id: str, reason: str = 'user_request') -> Dict[str, Any]:
        """
        取消訂閱並同步清理
        
        Args:
            subscription_id: 訂閱ID
            reason: 取消原因
            
        Returns:
            Dict[str, Any]: 取消結果
        """
        try:
            now = datetime.utcnow()
            
            # 使用事務確保一致性
            @firestore.transactional
            def cancel_with_sync(transaction):
                subscription_ref = self.db.collection('subscriptions').document(subscription_id)
                subscription_doc = subscription_ref.get(transaction=transaction)
                
                if not subscription_doc.exists:
                    raise ValueError(f"訂閱不存在: {subscription_id}")
                
                subscription_data = subscription_doc.to_dict()
                user_id = subscription_data['user_id']
                
                # 更新訂閱狀態
                transaction.update(subscription_ref, {
                    'status': 'cancelled',
                    'cancelled_at': now,
                    'cancellation_reason': reason,
                    'updated_at': now
                })
                
                # 清除用戶關聯
                user_ref = self.db.collection('users').document(user_id)
                transaction.update(user_ref, {
                    'current_subscription_id': None,
                    'updated_at': now
                })
                
                # 停用所有設備
                devices_query = self.db.collection('devices').where('user_id', '==', user_id).where('is_active', '==', True)
                for device_doc in devices_query.stream():
                    transaction.update(device_doc.reference, {
                        'is_active': False,
                        'status': 'inactive',
                        'deactivated_at': now,
                        'deactivation_reason': 'subscription_cancelled'
                    })
                
                return subscription_data
            
            transaction = self.db.transaction()
            original_data = cancel_with_sync(transaction)
            
            logger.info(f"訂閱取消並同步成功: {subscription_id}, 原因: {reason}")
            
            return {
                'success': True,
                'subscription_id': subscription_id,
                'cancelled_at': now.isoformat() + 'Z',
                'reason': reason
            }
            
        except Exception as e:
            logger.error(f"取消訂閱失敗: {subscription_id}, 錯誤: {str(e)}")
            raise
    
    def verify_sync_integrity(self, user_id: str) -> Dict[str, Any]:
        """
        驗證用戶訂閱同步完整性
        
        Args:
            user_id: 用戶ID
            
        Returns:
            Dict[str, Any]: 驗證結果
        """
        try:
            issues = []
            
            # 檢查用戶記錄
            user_ref = self.db.collection('users').document(user_id)
            user_doc = user_ref.get()
            
            if not user_doc.exists:
                issues.append("用戶記錄不存在")
                return {'has_issues': True, 'issues': issues}
            
            user_data = user_doc.to_dict()
            subscription_id = user_data.get('current_subscription_id')
            
            if not subscription_id:
                issues.append("用戶沒有關聯的訂閱")
            else:
                # 檢查訂閱記錄
                subscription_ref = self.db.collection('subscriptions').document(subscription_id)
                subscription_doc = subscription_ref.get()
                
                if not subscription_doc.exists:
                    issues.append(f"訂閱記錄不存在: {subscription_id}")
                else:
                    subscription_data = subscription_doc.to_dict()
                    
                    # 檢查雙向關聯
                    if subscription_data.get('user_id') != user_id:
                        issues.append("訂閱的用戶ID不匹配")
                    
                    # 檢查設備列表同步
                    user_device_ids = set(user_data.get('device_ids', []))
                    subscription_device_ids = set(subscription_data.get('active_device_ids', []))
                    
                    if user_device_ids != subscription_device_ids:
                        issues.append("用戶和訂閱的設備列表不同步")
            
            return {
                'has_issues': len(issues) > 0,
                'issues': issues,
                'user_id': user_id,
                'subscription_id': subscription_id
            }
            
        except Exception as e:
            logger.error(f"驗證同步完整性失敗: {user_id}, 錯誤: {str(e)}")
            return {
                'has_issues': True,
                'issues': [f"驗證失敗: {str(e)}"],
                'user_id': user_id
            }

# 全域用戶訂閱同步管理器實例
user_subscription_sync = UserSubscriptionSync()
