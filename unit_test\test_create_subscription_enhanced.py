"""
測試增強版 create_subscription API
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend-api'))

class TestCreateSubscriptionEnhanced(unittest.TestCase):
    """增強版訂閱創建測試類"""
    
    def setUp(self):
        """測試設置"""
        self.test_user_id = "test_user_123"
        self.test_stripe_data = {
            "stripe_invoice_id": "in_test_123",
            "stripe_customer_id": "cus_test_123",
            "stripe_subscription_id": "sub_test_123",
            "stripe_payment_intent_id": "pi_test_123",
            "amount_total": 1999,
            "amount_paid": 1999,
            "payment_status": "succeeded",
            "invoice_created": "2025-01-01T00:00:00Z",
            "period_start": "2025-01-01T00:00:00Z",
            "period_end": "2025-02-01T00:00:00Z"
        }
    
    def test_create_subscription_function_exists(self):
        """測試訂閱創建函數存在"""
        try:
            from functions.subscriptions.create_subscription import create_subscription_handler
            
            # 測試函數存在
            self.assertIsNotNone(create_subscription_handler)
            
            print("✅ 增強版訂閱創建函數測試通過")
            
        except Exception as e:
            print(f"❌ 增強版訂閱創建函數測試失敗: {str(e)}")
            self.fail(f"增強版訂閱創建函數測試失敗: {str(e)}")
    
    def test_pro_plan_validation(self):
        """測試 PRO 計劃驗證邏輯"""
        try:
            # 測試 PRO 計劃的必要欄位
            pro_plan_data = {
                "plan": "PRO",
                "daily_limit_seconds": 10800,
                "max_devices": 2,
                "price_amount": 1999,
                "currency": "usd",
                "billing_cycle": "monthly"
            }
            
            # 合併 Stripe 資料
            full_data = {**pro_plan_data, **self.test_stripe_data}
            
            # 驗證所有必要欄位存在
            required_fields = [
                'plan', 'daily_limit_seconds', 'max_devices',
                'stripe_invoice_id', 'stripe_customer_id', 'stripe_subscription_id',
                'amount_total', 'payment_status', 'invoice_created'
            ]
            
            for field in required_fields:
                self.assertIn(field, full_data)
                self.assertIsNotNone(full_data[field])
            
            print("✅ PRO 計劃驗證邏輯測試通過")
            
        except Exception as e:
            print(f"❌ PRO 計劃驗證邏輯測試失敗: {str(e)}")
            self.fail(f"PRO 計劃驗證邏輯測試失敗: {str(e)}")
    
    def test_free_plan_validation(self):
        """測試 FREE 計劃驗證邏輯"""
        try:
            # FREE 計劃不需要 Stripe 資訊
            free_plan_data = {
                "plan": "FREE",
                "daily_limit_seconds": 600,
                "max_devices": 1
            }
            
            # 驗證 FREE 計劃的必要欄位
            required_fields = ['plan', 'daily_limit_seconds', 'max_devices']
            
            for field in required_fields:
                self.assertIn(field, free_plan_data)
                self.assertIsNotNone(free_plan_data[field])
            
            # FREE 計劃不應該有 Stripe 資訊
            stripe_fields = ['stripe_invoice_id', 'stripe_customer_id', 'amount_total']
            for field in stripe_fields:
                self.assertNotIn(field, free_plan_data)
            
            print("✅ FREE 計劃驗證邏輯測試通過")
            
        except Exception as e:
            print(f"❌ FREE 計劃驗證邏輯測試失敗: {str(e)}")
            self.fail(f"FREE 計劃驗證邏輯測試失敗: {str(e)}")
    
    @patch('functions.utils.environment.get_db')
    def test_subscription_data_structure(self, mock_get_db):
        """測試訂閱資料結構"""
        try:
            from functions.subscriptions.create_subscription import create_subscription_handler
            from datetime import datetime
            
            # 模擬資料庫
            mock_subscription_ref = Mock()
            mock_user_ref = Mock()
            
            mock_db = Mock()
            mock_db.collection.return_value.document.return_value = mock_subscription_ref
            mock_get_db.return_value = mock_db
            
            # 測試訂閱資料結構
            expected_structure = {
                # 基本資訊
                'user_id': str,
                'plan': str,
                'status': str,
                'source_app': str,
                
                # 計劃配置
                'daily_limit_seconds': int,
                'max_devices': int,
                'current_day_used_seconds': int,
                
                # 時區和重置設定
                'register_region_timezone': str,
                'usage_reset_hour': int,
                'last_daily_reset_date': str,
                'last_daily_reset_at': datetime,
                
                # 設備管理
                'active_device_ids': list,
                'last_device_limit_check': datetime,
                
                # 時間戳
                'created_at': datetime,
                'updated_at': datetime
            }
            
            # 驗證結構定義
            for field, field_type in expected_structure.items():
                self.assertIsNotNone(field)
                self.assertIsNotNone(field_type)
            
            print("✅ 訂閱資料結構測試通過")
            
        except Exception as e:
            print(f"❌ 訂閱資料結構測試失敗: {str(e)}")
            self.fail(f"訂閱資料結構測試失敗: {str(e)}")
    
    def test_stripe_data_integration(self):
        """測試 Stripe 資料整合"""
        try:
            # 測試 Stripe 資料的完整性
            stripe_fields = {
                'stripe_customer_id': 'cus_test_123',
                'stripe_subscription_id': 'sub_test_123',
                'stripe_invoice_id': 'in_test_123',
                'stripe_payment_intent_id': 'pi_test_123',
                'amount_total': 1999,
                'amount_paid': 1999,
                'currency': 'usd',
                'payment_status': 'succeeded',
                'price_amount': 1999,
                'billing_cycle': 'monthly',
                'invoice_created': '2025-01-01T00:00:00Z',
                'period_start': '2025-01-01T00:00:00Z',
                'period_end': '2025-02-01T00:00:00Z',
                'auto_renew': True,
                'next_billing_date': '2025-02-01T00:00:00Z'
            }
            
            # 驗證所有 Stripe 欄位
            for field, value in stripe_fields.items():
                self.assertIsNotNone(field)
                self.assertIsNotNone(value)
                
                # 驗證資料類型
                if field in ['amount_total', 'amount_paid', 'price_amount']:
                    self.assertIsInstance(value, int)
                elif field in ['auto_renew']:
                    self.assertIsInstance(value, bool)
                else:
                    self.assertIsInstance(value, str)
            
            print("✅ Stripe 資料整合測試通過")
            
        except Exception as e:
            print(f"❌ Stripe 資料整合測試失敗: {str(e)}")
            self.fail(f"Stripe 資料整合測試失敗: {str(e)}")
    
    def test_plan_validation(self):
        """測試計劃驗證"""
        try:
            # 測試有效計劃
            valid_plans = ['FREE', 'STARTER', 'PRO', 'PREMIUM', 'MAX']
            
            for plan in valid_plans:
                self.assertIn(plan, valid_plans)
            
            # 測試無效計劃
            invalid_plans = ['INVALID', 'TEST', 'UNKNOWN']
            
            for plan in invalid_plans:
                self.assertNotIn(plan, valid_plans)
            
            print("✅ 計劃驗證測試通過")
            
        except Exception as e:
            print(f"❌ 計劃驗證測試失敗: {str(e)}")
            self.fail(f"計劃驗證測試失敗: {str(e)}")
    
    def test_response_structure(self):
        """測試回應結構"""
        try:
            # 測試預期的回應結構
            expected_response = {
                'success': True,
                'data': {
                    'subscription': {
                        'subscription_id': str,
                        'user_id': str,
                        'plan': str,
                        'status': str,
                        'daily_limit_seconds': int,
                        'max_devices': int,
                        'current_day_used_seconds': int,
                        'stripe_customer_id': str,
                        'stripe_subscription_id': str,
                        'billing_cycle': str,
                        'auto_renew': bool,
                        'created_at': str,
                        'updated_at': str
                    }
                },
                'message': str
            }
            
            # 驗證結構定義
            self.assertIn('success', expected_response)
            self.assertIn('data', expected_response)
            self.assertIn('message', expected_response)
            
            # 驗證訂閱資料結構
            subscription = expected_response['data']['subscription']
            required_fields = [
                'subscription_id', 'user_id', 'plan', 'status',
                'daily_limit_seconds', 'max_devices', 'current_day_used_seconds'
            ]
            
            for field in required_fields:
                self.assertIn(field, subscription)
            
            print("✅ 回應結構測試通過")
            
        except Exception as e:
            print(f"❌ 回應結構測試失敗: {str(e)}")
            self.fail(f"回應結構測試失敗: {str(e)}")

def run_create_subscription_enhanced_tests():
    """執行增強版訂閱創建測試"""
    print("🧪 開始增強版訂閱創建測試...")
    print("=" * 50)
    
    # 創建測試套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestCreateSubscriptionEnhanced)
    
    # 執行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("=" * 50)
    
    if result.wasSuccessful():
        print("🎉 所有增強版訂閱創建測試通過！")
        print("\n📋 新功能特點:")
        print("✅ 接收完整 Stripe 支付資訊")
        print("✅ 前端提供準確的計劃配置")
        print("✅ 支援 FREE 和付費計劃")
        print("✅ 移除 monthly_limit_seconds")
        print("✅ 完整的支付和計費資訊")
        return True
    else:
        print(f"❌ 測試失敗: {len(result.failures)} 個失敗, {len(result.errors)} 個錯誤")
        
        if result.failures:
            print("\n失敗的測試:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
        
        if result.errors:
            print("\n錯誤的測試:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
        
        return False

if __name__ == '__main__':
    success = run_create_subscription_enhanced_tests()
    sys.exit(0 if success else 1)
