"""
Device registration functionality for SpeechPilot Firebase Functions
"""

import logging
from firebase_functions import https_fn
from firebase_admin import firestore
from typing import Dict, Any

from ..utils.validation import validate_auth, validate_device_id, validate_platform
from ..utils.database import get_user_data, get_user_devices
from ..utils.constants import SUBSCRIPTION_PLANS, ERROR_MESSAGES
from ..utils.error_handler import handle_error, handle_validation_error, log_function_start, log_function_success

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端"""
    return firestore.client()

def register_device_handler(request: https_fn.CallableRequest) -> Dict[str, Any]:
    """
    註冊設備
    
    Args:
        request: Firebase Functions 請求對象
        
    Returns:
        Dict[str, Any]: 註冊結果
    """
    try:
        # 驗證用戶認證
        user_id = validate_auth(request)
        
        # 獲取請求數據
        data = request.data or {}
        device_id = data.get('device_id')
        device_name = data.get('device_name', 'Unknown Device')
        platform = data.get('platform', 'unknown')
        app_version = data.get('app_version', 'unknown')
        device_info = data.get('device_info', {})
        
        logger.info(f"Registering device for user {user_id}: {device_id}")
        
        # 驗證必要參數
        if not device_id:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="device_id is required"
            )
        
        if not validate_device_id(device_id):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="Invalid device_id format"
            )
        
        if not validate_platform(platform):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="Invalid platform"
            )
        
        # 獲取用戶資料
        user_data = get_user_data(user_id)
        
        # 獲取用戶訂閱計劃
        subscription = user_data.get('subscription', {})
        user_plan = subscription.get('plan', 'FREE')
        plan_config = SUBSCRIPTION_PLANS.get(user_plan, SUBSCRIPTION_PLANS["FREE"])
        max_devices = plan_config["max_devices"]
        
        # 檢查設備是否已存在
        db = get_db()
        existing_device_query = db.collection('devices').where(
            'user_id', '==', user_id
        ).where(
            'device_id', '==', device_id
        ).limit(1).stream()
        
        existing_device = None
        for doc in existing_device_query:
            existing_device = doc
            break
        
        if existing_device:
            # 設備已存在，更新資訊
            existing_device.reference.update({
                'device_name': device_name,
                'platform': platform,
                'app_version': app_version,
                'device_info': device_info,
                'last_active_at': firestore.SERVER_TIMESTAMP,
                'updated_at': firestore.SERVER_TIMESTAMP,
                'is_active': True
            })
            
            logger.info(f"Updated existing device {device_id} for user {user_id}")
            
            return {
                "success": True,
                "device_id": device_id,
                "is_new": False,
                "message": "設備資訊已更新"
            }
        
        # 檢查設備數量限制
        if max_devices != -1:
            current_devices = get_user_devices(user_id)
            active_device_count = len([d for d in current_devices if d.get('is_active', True)])
            
            if active_device_count >= max_devices:
                raise https_fn.HttpsError(
                    code=https_fn.FunctionsErrorCode.RESOURCE_EXHAUSTED,
                    message=ERROR_MESSAGES["DEVICE_LIMIT_EXCEEDED"]
                )
        
        # 創建新設備記錄
        device_data = {
            'user_id': user_id,
            'device_id': device_id,
            'device_name': device_name,
            'platform': platform,
            'app_version': app_version,
            'device_info': device_info,
            'is_active': True,
            'created_at': firestore.SERVER_TIMESTAMP,
            'updated_at': firestore.SERVER_TIMESTAMP,
            'last_active_at': firestore.SERVER_TIMESTAMP,
            'registration_ip': getattr(request.raw_request, 'remote_addr', 'unknown')
        }
        
        # 添加設備到 Firestore
        device_ref = db.collection('devices').document()
        device_ref.set(device_data)
        
        # 更新用戶的設備計數
        user_ref = db.collection('users').document(user_id)
        current_devices = get_user_devices(user_id)
        active_device_count = len([d for d in current_devices if d.get('is_active', True)]) + 1
        
        user_ref.update({
            'device_count': active_device_count,
            'updated_at': firestore.SERVER_TIMESTAMP
        })
        
        result = {
            "success": True,
            "device_id": device_id,
            "is_new": True,
            "message": "設備註冊成功",
            "device_count": active_device_count,
            "max_devices": max_devices
        }
        
        logger.info(f"Device {device_id} registered successfully for user {user_id}")
        
        return result
        
    except https_fn.HttpsError:
        # 重新拋出 Firebase Functions 錯誤
        raise
    except Exception as e:
        raise handle_error(
            error=e,
            error_code="INTERNAL_ERROR",
            user_message="註冊設備時發生錯誤",
            context={"user_id": user_id, "device_id": device_id, "function": "register_device_v2"}
        )
