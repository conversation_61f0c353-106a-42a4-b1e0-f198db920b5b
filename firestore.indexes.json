{"indexes": [{"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "register_region_timezone", "order": "ASCENDING"}, {"fieldPath": "last_usage_reset_date", "order": "ASCENDING"}]}, {"collectionGroup": "subscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "plan", "order": "ASCENDING"}]}, {"collectionGroup": "devices", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "is_active", "order": "ASCENDING"}]}, {"collectionGroup": "devices", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "platform", "order": "ASCENDING"}]}, {"collectionGroup": "usage_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "usage_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "daily_reset_date", "order": "ASCENDING"}]}, {"collectionGroup": "usage_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "device_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}], "fieldOverrides": []}