"""
Stripe 訂閱生命週期事件處理函數
處理訂閱創建、更新、刪除事件
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from firebase_admin import firestore

from ..utils.constants import SUBSCRIPTION_PLANS

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端（環境感知）"""
    from ..utils.environment import get_db as get_env_db
    return get_env_db()

def get_plan_from_stripe_price_id(price_id: str) -> Optional[str]:
    """
    根據 Stripe Price ID 獲取計劃名稱

    Args:
        price_id: Stripe Price ID

    Returns:
        str: 計劃名稱 (STARTER, PRO, PREMIUM, MAX) 或 None
    """
    from ..utils.stripe_config import get_plan_from_stripe_price_id as get_plan_config
    return get_plan_config(price_id)

def get_user_id_from_customer(customer_id: str) -> Optional[str]:
    """
    根據 Stripe Customer ID 獲取用戶 ID
    
    Args:
        customer_id: Stripe Customer ID
        
    Returns:
        str: 用戶 ID 或 None
    """
    try:
        db = get_db()
        
        # 在 users 集合中查找 stripe_customer_id
        users_ref = db.collection('users')
        query = users_ref.where('stripe_customer_id', '==', customer_id).limit(1)
        docs = query.stream()
        
        for doc in docs:
            return doc.id
        
        # 如果在 users 中找不到，嘗試在 subscriptions 中查找
        subscriptions_ref = db.collection('subscriptions')
        query = subscriptions_ref.where('stripe_customer_id', '==', customer_id).limit(1)
        docs = query.stream()
        
        for doc in docs:
            subscription_data = doc.to_dict()
            return subscription_data.get('user_id')
        
        logger.error(f"找不到 Customer ID {customer_id} 對應的用戶")
        return None
        
    except Exception as e:
        logger.error(f"查找用戶錯誤: {str(e)}")
        return None

def update_user_subscription_limits(user_id: str, plan: str):
    """
    更新用戶的訂閱限制
    
    Args:
        user_id: 用戶 ID
        plan: 計劃名稱
    """
    try:
        db = get_db()
        plan_config = SUBSCRIPTION_PLANS.get(plan, SUBSCRIPTION_PLANS['FREE'])
        
        # 更新 users 表
        user_ref = db.collection('users').document(user_id)
        user_ref.update({
            'current_plan': plan,
            'daily_limit_seconds': plan_config['daily_limit_seconds'],
            'max_devices': plan_config['max_devices'],
            'updated_at': datetime.utcnow()
        })
        
        logger.info(f"用戶 {user_id} 限制已更新為 {plan}")
        
    except Exception as e:
        logger.error(f"更新用戶限制錯誤: {str(e)}")
        raise

def handle_subscription_created(subscription: Dict[str, Any]):
    """
    處理訂閱創建事件
    
    Args:
        subscription: Stripe Subscription 對象
    """
    try:
        customer_id = subscription['customer']
        subscription_id = subscription['id']
        
        # 獲取用戶 ID
        user_id = get_user_id_from_customer(customer_id)
        if not user_id:
            logger.error(f"找不到 Customer {customer_id} 對應的用戶")
            return
        
        # 獲取計劃信息
        price_id = subscription['items']['data'][0]['price']['id']
        plan = get_plan_from_stripe_price_id(price_id)
        
        if not plan:
            logger.error(f"找不到 Price ID {price_id} 對應的計劃")
            return
        
        plan_config = SUBSCRIPTION_PLANS[plan]
        
        # 獲取計費週期
        billing_cycle = 'yearly' if subscription['items']['data'][0]['price']['recurring']['interval'] == 'year' else 'monthly'
        
        db = get_db()
        now = datetime.utcnow()
        
        # 更新或創建訂閱記錄
        subscription_doc_id = f"sub_{user_id}"
        subscription_ref = db.collection('subscriptions').document(subscription_doc_id)
        
        subscription_data = {
            'user_id': user_id,
            'plan': plan,
            'status': subscription['status'],
            
            # 計劃配置
            'daily_limit_seconds': plan_config['daily_limit_seconds'],
            'max_devices': plan_config['max_devices'],
            'current_day_used_seconds': 0,  # 立即重置配額
            
            # Stripe 信息
            'stripe_customer_id': customer_id,
            'stripe_subscription_id': subscription_id,
            'billing_cycle': billing_cycle,
            'auto_renew': not subscription.get('cancel_at_period_end', False),
            
            # 時間信息
            'current_period_start': datetime.fromtimestamp(subscription['current_period_start'], tz=timezone.utc),
            'current_period_end': datetime.fromtimestamp(subscription['current_period_end'], tz=timezone.utc),
            'next_billing_date': datetime.fromtimestamp(subscription['current_period_end'], tz=timezone.utc),
            
            # 重置信息
            'last_daily_reset_date': now.strftime('%Y-%m-%d'),
            'last_daily_reset_at': now,
            
            # Webhook 事件追蹤
            'webhook_events': {
                'customer.subscription.created': now
            },
            
            'created_at': now,
            'updated_at': now
        }
        
        subscription_ref.set(subscription_data)
        
        # 更新用戶限制
        update_user_subscription_limits(user_id, plan)
        
        logger.info(f"訂閱創建成功: 用戶 {user_id} -> {plan}")
        
    except Exception as e:
        logger.error(f"處理訂閱創建錯誤: {str(e)}")
        raise

def handle_subscription_updated(subscription: Dict[str, Any], previous_attributes: Dict[str, Any]):
    """
    處理訂閱更新事件
    
    Args:
        subscription: 更新後的 Stripe Subscription 對象
        previous_attributes: 更新前的屬性
    """
    try:
        customer_id = subscription['customer']
        subscription_id = subscription['id']
        
        # 獲取用戶 ID
        user_id = get_user_id_from_customer(customer_id)
        if not user_id:
            logger.error(f"找不到 Customer {customer_id} 對應的用戶")
            return
        
        db = get_db()
        now = datetime.utcnow()
        
        # 檢查是否為計劃變更
        price_changed = 'items' in previous_attributes
        cancel_at_period_end_changed = 'cancel_at_period_end' in previous_attributes
        
        if price_changed:
            # 計劃升級/降級
            price_id = subscription['items']['data'][0]['price']['id']
            new_plan = get_plan_from_stripe_price_id(price_id)
            
            if new_plan:
                plan_config = SUBSCRIPTION_PLANS[new_plan]
                
                # 立即更新配額限制（升級時立即生效）
                subscription_ref = db.collection('subscriptions').document(f"sub_{user_id}")
                subscription_ref.update({
                    'plan': new_plan,
                    'daily_limit_seconds': plan_config['daily_limit_seconds'],
                    'max_devices': plan_config['max_devices'],
                    'current_day_used_seconds': 0,  # 重置當日使用量
                    'last_daily_reset_date': now.strftime('%Y-%m-%d'),
                    'last_daily_reset_at': now,
                    'webhook_events.customer.subscription.updated': now,
                    'updated_at': now
                })
                
                # 更新用戶限制
                update_user_subscription_limits(user_id, new_plan)
                
                logger.info(f"計劃變更成功: 用戶 {user_id} -> {new_plan}")
        
        if cancel_at_period_end_changed:
            # 取消狀態變更
            subscription_ref = db.collection('subscriptions').document(f"sub_{user_id}")
            subscription_ref.update({
                'cancel_at_period_end': subscription.get('cancel_at_period_end', False),
                'auto_renew': not subscription.get('cancel_at_period_end', False),
                'webhook_events.customer.subscription.updated': now,
                'updated_at': now
            })
            
            logger.info(f"取消狀態更新: 用戶 {user_id}, cancel_at_period_end: {subscription.get('cancel_at_period_end', False)}")
        
        # 更新其他訂閱信息
        subscription_ref = db.collection('subscriptions').document(f"sub_{user_id}")
        subscription_ref.update({
            'status': subscription['status'],
            'current_period_start': datetime.fromtimestamp(subscription['current_period_start'], tz=timezone.utc),
            'current_period_end': datetime.fromtimestamp(subscription['current_period_end'], tz=timezone.utc),
            'next_billing_date': datetime.fromtimestamp(subscription['current_period_end'], tz=timezone.utc),
            'webhook_events.customer.subscription.updated': now,
            'updated_at': now
        })
        
    except Exception as e:
        logger.error(f"處理訂閱更新錯誤: {str(e)}")
        raise

def handle_subscription_deleted(subscription: Dict[str, Any]):
    """
    處理訂閱刪除事件 - 降級到 FREE 計劃
    
    Args:
        subscription: Stripe Subscription 對象
    """
    try:
        customer_id = subscription['customer']
        subscription_id = subscription['id']
        
        # 獲取用戶 ID
        user_id = get_user_id_from_customer(customer_id)
        if not user_id:
            logger.error(f"找不到 Customer {customer_id} 對應的用戶")
            return
        
        db = get_db()
        now = datetime.utcnow()
        
        # 降級到 FREE 計劃
        free_plan_config = SUBSCRIPTION_PLANS['FREE']
        
        subscription_ref = db.collection('subscriptions').document(f"sub_{user_id}")
        subscription_ref.update({
            'plan': 'FREE',
            'status': 'canceled',
            'daily_limit_seconds': free_plan_config['daily_limit_seconds'],
            'max_devices': free_plan_config['max_devices'],
            'current_day_used_seconds': 0,  # 重置配額
            
            # 清除 Stripe 信息
            'stripe_subscription_id': None,
            'billing_cycle': None,
            'auto_renew': False,
            'next_billing_date': None,
            'cancel_at_period_end': False,
            
            # 重置信息
            'last_daily_reset_date': now.strftime('%Y-%m-%d'),
            'last_daily_reset_at': now,
            
            # Webhook 事件追蹤
            'webhook_events.customer.subscription.deleted': now,
            'updated_at': now
        })
        
        # 更新用戶限制到 FREE 計劃
        update_user_subscription_limits(user_id, 'FREE')
        
        logger.info(f"訂閱刪除，用戶 {user_id} 已降級到 FREE 計劃")
        
    except Exception as e:
        logger.error(f"處理訂閱刪除錯誤: {str(e)}")
        raise
