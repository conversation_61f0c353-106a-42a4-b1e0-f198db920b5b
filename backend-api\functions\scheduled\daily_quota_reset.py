"""
每日配額重置定時任務
每小時執行，根據用戶時區重置每日配額
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List
from firebase_functions import scheduler_fn
from firebase_admin import firestore

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端（環境感知）"""
    from ..utils.environment import get_db as get_env_db
    return get_env_db()

def get_timezone_offset_hours(timezone_str: str) -> int:
    """
    解析時區字符串並返回 UTC 偏移小時數
    
    Args:
        timezone_str: 時區字符串，如 "UTC+8", "UTC-5"
        
    Returns:
        int: UTC 偏移小時數
    """
    try:
        if timezone_str.startswith('UTC'):
            offset_str = timezone_str[3:]  # 移除 "UTC" 前綴
            if offset_str.startswith('+'):
                return int(offset_str[1:])
            elif offset_str.startswith('-'):
                return -int(offset_str[1:])
            else:
                return 0  # UTC
        else:
            # 處理其他時區格式，這裡簡化處理
            return 0
    except:
        logger.warning(f"無法解析時區: {timezone_str}")
        return 0

def get_users_to_reset(current_utc_hour: int) -> List[Dict[str, Any]]:
    """
    獲取需要在當前 UTC 小時重置配額的用戶
    
    Args:
        current_utc_hour: 當前 UTC 小時 (0-23)
        
    Returns:
        List[Dict[str, Any]]: 需要重置的用戶列表
    """
    try:
        db = get_db()
        users_to_reset = []
        
        # 查詢所有有訂閱的用戶
        subscriptions_ref = db.collection('subscriptions')
        subscriptions = subscriptions_ref.stream()
        
        current_date = datetime.utcnow().strftime('%Y-%m-%d')
        
        for subscription_doc in subscriptions:
            subscription_data = subscription_doc.to_dict()
            user_id = subscription_data.get('user_id')
            
            if not user_id:
                continue
            
            # 獲取用戶時區
            timezone_str = subscription_data.get('register_region_timezone', 'UTC+8')
            usage_reset_hour = subscription_data.get('usage_reset_hour', 0)  # 預設 00:00
            last_reset_date = subscription_data.get('last_daily_reset_date', '')
            
            # 計算用戶時區的重置時間對應的 UTC 時間
            timezone_offset = get_timezone_offset_hours(timezone_str)
            user_reset_utc_hour = (usage_reset_hour - timezone_offset) % 24
            
            # 檢查是否需要重置
            should_reset = (
                current_utc_hour == user_reset_utc_hour and  # 到了重置時間
                last_reset_date != current_date  # 今天還沒重置過
            )
            
            if should_reset:
                users_to_reset.append({
                    'user_id': user_id,
                    'subscription_id': subscription_doc.id,
                    'timezone': timezone_str,
                    'reset_hour': usage_reset_hour,
                    'current_used': subscription_data.get('current_day_used_seconds', 0)
                })
        
        return users_to_reset
        
    except Exception as e:
        logger.error(f"獲取需要重置的用戶錯誤: {str(e)}")
        return []

def reset_user_daily_quota(user_id: str, subscription_id: str) -> bool:
    """
    重置單個用戶的每日配額
    
    Args:
        user_id: 用戶 ID
        subscription_id: 訂閱 ID
        
    Returns:
        bool: 是否成功重置
    """
    try:
        db = get_db()
        now = datetime.utcnow()
        current_date = now.strftime('%Y-%m-%d')
        
        # 更新訂閱記錄
        subscription_ref = db.collection('subscriptions').document(subscription_id)
        subscription_ref.update({
            'current_day_used_seconds': 0,
            'last_daily_reset_date': current_date,
            'last_daily_reset_at': now,
            'updated_at': now
        })
        
        logger.info(f"用戶 {user_id} 每日配額重置成功")
        return True
        
    except Exception as e:
        logger.error(f"重置用戶 {user_id} 配額錯誤: {str(e)}")
        return False

@scheduler_fn.on_schedule(schedule="0 * * * *", timezone="UTC")  # 每小時執行
def daily_quota_reset_handler(event: scheduler_fn.ScheduledEvent) -> None:
    """
    每日配額重置定時任務處理函數
    
    Args:
        event: 定時事件對象
    """
    try:
        current_utc = datetime.utcnow()
        current_utc_hour = current_utc.hour
        
        logger.info(f"開始執行每日配額重置任務，當前 UTC 時間: {current_utc}, 小時: {current_utc_hour}")
        
        # 獲取需要重置的用戶
        users_to_reset = get_users_to_reset(current_utc_hour)
        
        if not users_to_reset:
            logger.info("沒有用戶需要重置配額")
            return
        
        logger.info(f"找到 {len(users_to_reset)} 個用戶需要重置配額")
        
        # 批量重置用戶配額
        success_count = 0
        failed_count = 0
        
        for user_info in users_to_reset:
            user_id = user_info['user_id']
            subscription_id = user_info['subscription_id']
            
            try:
                if reset_user_daily_quota(user_id, subscription_id):
                    success_count += 1
                    logger.info(f"用戶 {user_id} 配額重置成功 (時區: {user_info['timezone']}, 重置時間: {user_info['reset_hour']}:00)")
                else:
                    failed_count += 1
                    
            except Exception as e:
                failed_count += 1
                logger.error(f"重置用戶 {user_id} 配額失敗: {str(e)}")
        
        logger.info(f"每日配額重置任務完成: 成功 {success_count} 個，失敗 {failed_count} 個")
        
        # 記錄重置日誌（可選）
        if success_count > 0 or failed_count > 0:
            db = get_db()
            reset_log_ref = db.collection('quota_reset_logs').document()
            reset_log_ref.set({
                'reset_time': current_utc,
                'utc_hour': current_utc_hour,
                'users_processed': len(users_to_reset),
                'success_count': success_count,
                'failed_count': failed_count,
                'users_reset': [user['user_id'] for user in users_to_reset if success_count > 0]
            })
        
    except Exception as e:
        logger.error(f"每日配額重置任務錯誤: {str(e)}")
        raise

def manual_reset_user_quota(user_id: str) -> Dict[str, Any]:
    """
    手動重置用戶配額（用於測試或管理）
    
    Args:
        user_id: 用戶 ID
        
    Returns:
        Dict[str, Any]: 重置結果
    """
    try:
        db = get_db()
        
        # 查找用戶的訂閱
        subscription_id = f"sub_{user_id}"
        subscription_ref = db.collection('subscriptions').document(subscription_id)
        subscription_doc = subscription_ref.get()
        
        if not subscription_doc.exists:
            return {
                'success': False,
                'message': f'用戶 {user_id} 沒有訂閱記錄'
            }
        
        subscription_data = subscription_doc.to_dict()
        old_used_seconds = subscription_data.get('current_day_used_seconds', 0)
        
        # 重置配額
        if reset_user_daily_quota(user_id, subscription_id):
            return {
                'success': True,
                'message': f'用戶 {user_id} 配額重置成功',
                'data': {
                    'user_id': user_id,
                    'old_used_seconds': old_used_seconds,
                    'new_used_seconds': 0,
                    'reset_time': datetime.utcnow().isoformat()
                }
            }
        else:
            return {
                'success': False,
                'message': f'用戶 {user_id} 配額重置失敗'
            }
        
    except Exception as e:
        logger.error(f"手動重置用戶 {user_id} 配額錯誤: {str(e)}")
        return {
            'success': False,
            'message': f'重置錯誤: {str(e)}'
        }

def get_quota_reset_status() -> Dict[str, Any]:
    """
    獲取配額重置狀態統計
    
    Returns:
        Dict[str, Any]: 重置狀態統計
    """
    try:
        db = get_db()
        current_date = datetime.utcnow().strftime('%Y-%m-%d')
        
        # 統計今天已重置和未重置的用戶
        subscriptions_ref = db.collection('subscriptions')
        subscriptions = subscriptions_ref.stream()
        
        reset_today = 0
        not_reset_today = 0
        total_users = 0
        
        for subscription_doc in subscriptions:
            subscription_data = subscription_doc.to_dict()
            total_users += 1
            
            last_reset_date = subscription_data.get('last_daily_reset_date', '')
            if last_reset_date == current_date:
                reset_today += 1
            else:
                not_reset_today += 1
        
        return {
            'success': True,
            'data': {
                'current_date': current_date,
                'total_users': total_users,
                'reset_today': reset_today,
                'not_reset_today': not_reset_today,
                'reset_percentage': round((reset_today / total_users * 100) if total_users > 0 else 0, 2)
            }
        }
        
    except Exception as e:
        logger.error(f"獲取配額重置狀態錯誤: {str(e)}")
        return {
            'success': False,
            'message': f'獲取狀態錯誤: {str(e)}'
        }
