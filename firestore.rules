rules_version='2'

service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Subscriptions collection
    match /subscriptions/{subscriptionId} {
      allow read, write: if request.auth != null;
    }

    // Devices collection
    match /devices/{deviceId} {
      allow read, write: if request.auth != null;
    }

    // Purchases collection (admin/functions only)
    match /purchases/{purchaseId} {
      allow read: if request.auth != null;
      allow write: if false; // Only functions can write
    }

    // Device action logs (admin/functions only)
    match /device_action_logs/{logId} {
      allow read: if request.auth != null;
      allow write: if false; // Only functions can write
    }

    // Fallback rule for development (expires 2025-08-26)
    match /{document=**} {
      allow read, write: if request.time < timestamp.date(2025, 8, 26);
    }
  }
}
