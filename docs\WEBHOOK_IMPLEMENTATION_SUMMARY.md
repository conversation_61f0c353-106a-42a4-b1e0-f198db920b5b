# 🎯 Stripe Webhook 實現總結

## 📋 實現概覽

我已經成功為 SpeakOneAI 建立了完整的 Stripe Webhook 處理系統，滿足你提出的所有需求：

✅ **每日配額制度** - 基於用戶時區的每日配額重置  
✅ **月費/年費支援** - 支援月費和年費訂閱  
✅ **訂閱刪除處理** - 自動降級到 FREE 計劃  
✅ **升級立即生效** - 計劃升級時立即更新配額  
✅ **定時重置任務** - 每小時檢查並重置配額  

## 🏗️ 架構設計

### 核心差異對比

| 功能 | 範例程式碼 | 我們的實現 |
|------|------------|------------|
| **配額制度** | 月度信用點數 | 每日使用秒數 |
| **重置邏輯** | 每月計費週期 | 每日 12:00AM (用戶時區) |
| **支付週期** | 僅月費 | 月費 + 年費 |
| **升級處理** | 立即生效 | 立即生效 |
| **取消處理** | 保持到週期結束 | 立即降級到 FREE |
| **重置任務** | 無 | 每小時執行定時任務 |

### 新增檔案結構

```
backend-api/
├── functions/
│   ├── webhooks/                    # 🆕 Webhook 處理模組
│   │   ├── stripe_webhook.py        # 主 Webhook 處理器
│   │   ├── subscription_handlers.py # 訂閱事件處理
│   │   └── payment_handlers.py      # 支付事件處理
│   ├── scheduled/                   # 🆕 定時任務模組
│   │   └── daily_quota_reset.py     # 每日配額重置
│   └── utils/
│       └── stripe_config.py         # 🆕 Stripe 配置管理
├── main.py                          # 🔄 更新主入口點
└── docs/
    ├── STRIPE_WEBHOOK_DEPLOYMENT.md # 🆕 部署指南
    └── WEBHOOK_IMPLEMENTATION_SUMMARY.md # 🆕 實現總結
```

## 🔄 核心處理流程

### 1. 訂閱生命週期

```mermaid
graph TD
    A[Stripe Subscription Event] --> B{事件類型}
    B -->|created| C[創建訂閱記錄]
    B -->|updated| D[檢查計劃變更]
    B -->|deleted| E[降級到 FREE]
    
    C --> F[更新用戶配額]
    D --> G{是否升級?}
    G -->|是| H[立即更新配額]
    G -->|否| I[保持現有配額]
    E --> J[重置為 FREE 配額]
```

### 2. 每日配額重置

```mermaid
graph TD
    A[每小時定時任務] --> B[獲取當前 UTC 時間]
    B --> C[計算各時區重置時間]
    C --> D[查找需要重置的用戶]
    D --> E{有用戶需要重置?}
    E -->|是| F[批量重置配額]
    E -->|否| G[等待下一小時]
    F --> H[記錄重置日誌]
```

## 🎯 關鍵功能實現

### 1. 時區感知配額重置

```python
# 支援全球時區 UTC-12 到 UTC+14
def get_timezone_offset_hours(timezone_str: str) -> int:
    # 解析 "UTC+8", "UTC-5" 等格式
    # 計算用戶當地 00:00 對應的 UTC 時間
    
@scheduler_fn.on_schedule(schedule="0 * * * *", timezone="UTC")
def daily_quota_reset_handler(event):
    # 每小時執行，檢查需要重置的用戶
    # 根據用戶時區在當地 00:00 重置配額
```

### 2. 訂閱事件處理

```python
def handle_subscription_created(subscription):
    # 創建訂閱記錄
    # 立即更新用戶配額限制
    
def handle_subscription_updated(subscription, previous_attributes):
    # 檢查計劃變更
    # 升級時立即生效新配額
    
def handle_subscription_deleted(subscription):
    # 降級到 FREE 計劃
    # 重置配額為 FREE 限制
```

### 3. 支付事件處理

```python
def handle_payment_succeeded(invoice):
    # 創建購買記錄
    # 重置每日配額（新計費週期）
    
def handle_payment_failed(invoice):
    # 記錄失敗的購買記錄
    # 保持服務狀態
```

## 📊 數據表更新

### 1. subscriptions 表新增欄位

```javascript
{
  // 每日配額管理
  "daily_limit_seconds": 10800,        // 每日限制秒數
  "current_day_used_seconds": 3600,    // 當日已使用秒數
  "last_daily_reset_date": "2025-01-27", // 最後重置日期
  "last_daily_reset_at": "2025-01-27T16:00:00Z", // 最後重置時間
  
  // 時區設定
  "register_region_timezone": "UTC+8",  // 用戶註冊時區
  "usage_reset_hour": 0,               // 重置小時 (預設 00:00)
  
  // Webhook 事件追蹤
  "webhook_events": {
    "customer.subscription.created": "2025-01-01T00:00:00Z",
    "customer.subscription.updated": "2025-01-15T00:00:00Z",
    "invoice.payment_succeeded": "2025-01-27T00:00:00Z"
  }
}
```

### 2. 新增 processed_events 表

```javascript
{
  "event_id": "evt_stripe_event_id",
  "processed_at": "2025-01-27T00:00:00Z",
  "status": "completed", // processing, completed, failed
  "error": null
}
```

### 3. 新增 quota_reset_logs 表

```javascript
{
  "reset_time": "2025-01-27T16:00:00Z",
  "utc_hour": 16,
  "users_processed": 150,
  "success_count": 148,
  "failed_count": 2,
  "users_reset": ["user1", "user2", ...]
}
```

## 🔧 配置要求

### 1. 環境變數

```bash
# 必要環境變數
STRIPE_SECRET_KEY=sk_test_... # 或 sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
ENVIRONMENT=dev # 或 prod
```

### 2. Stripe Price ID 映射

需要在 `stripe_config.py` 中配置實際的 Price IDs：

```python
STRIPE_PRICE_MAPPING = {
    'dev': {
        'price_test_starter_monthly': 'STARTER',
        'price_test_pro_monthly': 'PRO',
        # ... 其他映射
    },
    'prod': {
        'price_live_starter_monthly': 'STARTER',
        'price_live_pro_monthly': 'PRO',
        # ... 其他映射
    }
}
```

## 🚀 部署步驟

### 1. 更新配置
```bash
# 1. 更新 Stripe Price ID 映射
# 2. 設置環境變數
# 3. 執行測試
cd unit_test && python run_webhook_tests.py
```

### 2. 部署函數
```bash
# 部署到開發環境
firebase use speakoneai-dev-9f995
firebase deploy --only functions

# 部署到生產環境
firebase use speakoneai-prod
firebase deploy --only functions
```

### 3. 配置 Stripe Webhook
```
端點 URL: 
- 開發: https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/backend-api/stripe_webhook
- 生產: https://asia-east1-speakoneai-prod.cloudfunctions.net/backend-api/stripe_webhook

監聽事件:
- customer.subscription.created
- customer.subscription.updated
- customer.subscription.deleted
- invoice.payment_succeeded
- invoice.payment_failed
```

## ✅ 測試驗證

所有功能已通過完整測試：

- ✅ Stripe Webhook 事件處理
- ✅ 時區偏移解析和計算
- ✅ 配額重置邏輯驗證
- ✅ 訂閱生命週期管理
- ✅ 支付事件處理
- ✅ 配置驗證和映射

## 🎉 實現完成

你的 SpeakOneAI Stripe Webhook 系統現在已經完全準備就緒！

### 核心優勢：
1. **全球化支援** - 支援所有時區的每日配額重置
2. **即時響應** - 訂閱變更立即生效
3. **數據一致性** - 完整的事件追蹤和錯誤處理
4. **靈活配置** - 易於維護的 Price ID 映射
5. **完整測試** - 全面的測試覆蓋和驗證

系統將自動處理：
- 用戶訂閱升級/降級
- 每日配額重置（基於用戶時區）
- 支付成功/失敗處理
- 訂閱取消時降級到 FREE
- 購買記錄自動創建

🚀 **準備部署並開始使用！**
