"""
Constants and configuration for SpeechPilot Firebase Functions
"""

from typing import Dict, Any
from enum import Enum

class SubscriptionPlan(Enum):
    """訂閱計劃枚舉"""
    FREE = "FREE"
    STARTER = "STARTER"
    PRO = "PRO"
    PREMIUM = "PREMIUM"
    MAX = "MAX"

class Platform(Enum):
    """平台枚舉"""
    WINDOWS = "windows"
    MACOS = "macos"
    IOS = "ios"
    ANDROID = "android"

class Features(Enum):
    """功能枚舉"""
    AI_SPEECH_TO_TEXT = "ai-speech-to-text"
    DIRECT_SPEECH_TO_TEXT = "direct-speech-to-text"
    REAL_TIME_TRANSCRIPTION = "real-time-transcription"
    VOICE_COMMANDS = "voice-commands"

# 訂閱計劃配置（基於 Subscription.md）
SUBSCRIPTION_PLANS: Dict[str, Dict[str, Any]] = {
    "FREE": {
        "daily_limit_seconds": 10 * 60,  # 10 minutes per day
        "max_devices": 1,
        "supported_platforms": ["windows", "macos", "ios", "android"],  # 所有平台
        "concurrent_sessions": 1,
        "features": ["基本語音轉文字", "每日 10 分鐘", "所有平台"],
        "price_monthly": 0,
        "price_yearly": 0
    },
    "STARTER": {
        "daily_limit_seconds": 60 * 60,  # 1 hour per day
        "max_devices": 1,
        "supported_platforms": ["windows", "macos", "ios", "android"],
        "concurrent_sessions": 1,
        "features": ["進階語音轉文字", "每日 1 小時", "所有平台"],
        "price_monthly": 999,  # $9.99
        "price_yearly": 9900   # $99
    },
    "PRO": {
        "daily_limit_seconds": 3 * 60 * 60,  # 3 hours per day
        "max_devices": 2,
        "supported_platforms": ["windows", "macos", "ios", "android"],
        "concurrent_sessions": 1,
        "features": ["專業語音轉文字", "每日 3 小時", "2 台設備", "優先支援"],
        "price_monthly": 1999,  # $19.99
        "price_yearly": 19900   # $199
    },
    "PREMIUM": {
        "daily_limit_seconds": 8 * 60 * 60,  # 8 hours per day
        "max_devices": 5,
        "supported_platforms": ["windows", "macos", "ios", "android"],
        "concurrent_sessions": 2,
        "features": ["頂級語音轉文字", "每日 8 小時", "5 台設備", "企業級支援"],
        "price_monthly": 5999,  # $59.99
        "price_yearly": 55900   # $559
    },
    "MAX": {
        "daily_limit_seconds": -1,  # Unlimited hours per day
        "max_devices": -1,  # Unlimited device limit
        "supported_platforms": ["windows", "macos", "ios", "android"],
        "concurrent_sessions": -1,
        "features": ["無限語音轉文字", "無限制使用", "無限設備", "專屬客服", "API 存取"],
        "price_monthly": 12999,  # $129.99
        "price_yearly": 109900   # $1099
    }
}

# 錯誤訊息
ERROR_MESSAGES = {
    "UNAUTHENTICATED": "用戶未認證",
    "USER_NOT_FOUND": "用戶資料不存在",
    "DEVICE_NOT_FOUND": "設備不存在",
    "INVALID_DEVICE": "無效的設備",
    "DEVICE_LIMIT_EXCEEDED": "設備數量超過限制",
    "PLATFORM_NOT_SUPPORTED": "您的訂閱計劃不支援此平台",
    "CONCURRENT_LIMIT_EXCEEDED": "同時使用設備數量超過限制",
    "DEVICE_NOT_AUTHORIZED": "設備未授權，請重新註冊",
    "USAGE_LIMIT_EXCEEDED": "使用量超過限制",
    "INVALID_SUBMISSION": "無效的使用量提交",
    "PERMISSION_DENIED": "權限不足",
    "INTERNAL_ERROR": "內部錯誤"
}

# 預設值
DEFAULT_WEB_PORTAL_URL = "https://speechpilot.com"
DEFAULT_UPGRADE_PATH = "/upgrade"

# Token 計算配置
TOKEN_CALCULATION = {
    "ai_speech_to_text": 10,  # 每秒約 10 tokens
    "direct_speech_to_text": 2  # 每秒約 2 tokens
}

# 設備狀態枚舉
class DeviceStatus(Enum):
    """設備狀態枚舉"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    REMOVED = "removed"
    UNLINKED = "unlinked"

# 設備操作類型
class DeviceAction(Enum):
    """設備操作類型"""
    LOGIN = "login"
    LOGOUT = "logout"
    REGISTER = "register"
    VALIDATE = "validate"
    REMOVE = "remove"
    UNLINK = "unlink"
