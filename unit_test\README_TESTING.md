# SpeakOneAI 測試套件說明

## 概述

本測試套件提供完整的 SpeakOneAI 訂閱系統測試，包括 Webhook 處理、訂閱管理、配額重置等核心功能。

## 主要測試腳本

### 🚀 `run_all_subscription_tests.py` (主要入口)

**這是您應該使用的主要測試腳本**，整合了所有測試功能：

```bash
cd unit_test
python run_all_subscription_tests.py
```

#### 測試範圍：
1. **基礎 Webhook 功能測試** - Stripe 事件處理基礎功能
2. **配額重置邏輯測試** - 每日配額重置和時區處理
3. **訂閱場景測試** - 升級、降級、取消等場景
4. **Webhook 整合測試** - Stripe 事件與數據庫整合
5. **完整 Webhook 場景測試** - 使用共享測試用戶的完整流程測試

## 測試架構改進

### ✅ 解決的問題

**原問題**: 支付 Webhook 測試中出現用戶表 INSERT 操作，而不是預期的 UPDATE 操作

**解決方案**: 
- 在測試開始時創建一個共享測試用戶（使用 `create_or_update_user` 方法）
- 所有後續測試都使用這個共享用戶，避免重複創建用戶
- 支付處理時只會對現有用戶進行 UPDATE 操作

### 🔧 新的測試流程

```
🏗️  測試開始: 創建共享測試用戶 (使用 create_or_update_user)
📋 測試 1: 共享用戶 → 訂閱創建 + 支付 (UPDATE 操作)
📋 測試 2: 共享用戶 → 訂閱升級 + 支付 (UPDATE 操作)  
📋 測試 3: 共享用戶 → 訂閱取消 (UPDATE 操作)
📋 測試 4: 共享用戶 → 多次支付追蹤 (UPDATE 操作)
```

## 核心測試文件

### 📁 主要測試模塊

- **`test_complete_webhook_scenarios.py`** - 完整 Webhook 場景測試（使用共享用戶）
- **`test_subscription_scenarios.py`** - 訂閱場景測試
- **`test_webhook_integration.py`** - Webhook 整合測試
- **`test_stripe_webhook.py`** - 基礎 Stripe Webhook 測試
- **`test_daily_quota_reset.py`** - 配額重置測試

### 📁 支援文件

- **`test_enhanced_logging.py`** - 增強的測試日誌記錄器
- **`load_env.py`** - 環境變數載入
- **`cleanup_dev_data.py`** - 測試數據清理

## 測試報告

測試執行後會自動生成詳細報告：
- 位置: `unit_test/results/`
- 格式: `test_results_YYYYMMDD_HHMMSS.md`
- 內容: 包含所有測試步驟、數據庫變更、斷言結果

## 環境要求

### 必要依賴
```bash
pip install firebase-admin
pip install python-dotenv
```

### 環境配置
- Firebase 服務帳戶金鑰文件
- `.env.dev` 文件配置
- Stripe 測試密鑰（可選，用於完整測試）

## 測試結果解讀

### ✅ 成功指標
```
🎉 所有測試通過！

✅ 驗證完成的功能:
  ✅ Stripe Webhook 事件處理
  ✅ 訂閱生命週期管理
  ✅ 每日配額重置邏輯
  ✅ 升級/降級場景處理
  ✅ 設備限制管理
  ✅ 支付事件處理
  ✅ 數據庫狀態一致性
  ✅ 購買記錄創建
  ✅ 取消到 FREE 計劃
  ✅ 時區感知配額重置
  ✅ 共享測試用戶架構
  ✅ 完整 Webhook 場景流程
```

### ❌ 故障排除

如果測試失敗，檢查：
1. Firebase Admin SDK 是否正確安裝和配置
2. 服務帳戶金鑰文件是否存在
3. 網絡連接是否正常
4. 環境變數是否正確設置

## 快速開始

1. **安裝依賴**:
   ```bash
   pip install firebase-admin python-dotenv
   ```

2. **配置環境**:
   - 確保 Firebase 服務帳戶金鑰文件存在
   - 檢查 `.env.dev` 配置

3. **運行測試**:
   ```bash
   cd unit_test
   python run_all_subscription_tests.py
   ```

4. **查看結果**:
   - 控制台輸出顯示即時結果
   - 詳細報告保存在 `results/` 目錄

## 重要改進

### 🎯 共享測試用戶架構
- **問題**: 每個測試創建新用戶，導致支付時看起來像用戶創建
- **解決**: 使用共享測試用戶，所有測試圍繞同一用戶進行
- **效果**: 支付處理時只會 UPDATE 現有用戶，符合實際業務邏輯

### 📊 增強的測試日誌
- 詳細的時間戳記錄 (HH:MM:SS 格式)
- 清楚的數據庫變更追蹤
- 自動生成 Markdown 測試報告
- 區分測試設置和實際測試階段

### 🔧 回退機制
- 如果 `create_or_update_user` 導入失敗，自動回退到舊方法
- 確保測試的穩定性和向後兼容性

## 支援的測試場景

- ✅ 訂閱創建 + 首次支付
- ✅ 訂閱升級 + 支付處理
- ✅ 訂閱取消 + 降級到 FREE
- ✅ 多次支付記錄追蹤
- ✅ 設備限制管理
- ✅ 配額重置邏輯
- ✅ 數據一致性驗證
- ✅ 邊界情況處理

這個測試套件現在提供了完整、可靠的 SpeakOneAI 訂閱系統驗證，確保所有核心功能都能正確運行。
