# 📚 SpeechPilot V2.3 數據字典

## 📋 **概覽**

**版本**: V2.3 - 統一 API 架構 + 購買記錄整合
**更新日期**: 2025-01-27
**開發項目**: speakoneai-dev-9f995 (default database)
**生產項目**: speakoneai-prod (default database)

### 🎯 **V2.3 架構重點**
- **統一 API**: 整合用戶、設備、訂閱管理為統一 API
- **購買記錄**: 完整的 Stripe 支付資訊整合
- **設備狀態**: 增強的設備生命週期管理
- **配額優化**: 移除冗餘欄位，統一數據源
- **每日配額制**: 基於用戶註冊地區時區的每日配額重置

---

## 📊 **集合概覽**

| 集合名稱 | 用途 | 主要欄位 | 關聯 |
|---------|------|----------|------|
| `users` | 用戶基本資料 | email, current_subscription_id, device_ids | → subscriptions |
| `subscriptions` | 訂閱和配額管理 | plan, daily_limit, usage, timezone | ← users |
| `devices` | 設備註冊和管理 | device_id, platform, status, is_authorized | ← users |
| `usage_logs` | 使用記錄聚合 | duration, tokens, session_count | ← users |
| `purchases` | 購買記錄 | stripe_invoice_id, amount, status, plan | ← users |


---

## 🗄️ **數據庫架構**

### 1. 👤 **users** 集合

**用途**: 用戶基本資訊和每日配額追蹤

```javascript
{
  // 文檔 ID: user_id (Firebase Auth UID)
  
  // === 基本資訊 ===
  "email": "<EMAIL>",
  "display_name": "John Doe", 
  "auth_provider": "google", // google, apple, email
  "email_verified": true,
  "profile_image": "https://...",
  "sign_up_platform": "windows", // 註冊時使用的平台
  "app_version": "1.0.0",
  "is_active": true,
  "is_banned": false,
  "created_at": "2025-01-01T00:00:00Z",
  "last_login_at": "2025-01-01T00:00:00Z", 
  "updated_at": "2025-01-01T00:00:00Z",

  // === 訂閱關聯 ===
  "current_subscription_id": "sub_xxx", // 當前訂閱ID (唯一訂閱相關欄位)

  // === 業務流程狀態 ===
  "onboarding_completed": true, // 是否完成 Onboarding
  "first_device_registered": true, // 是否已註冊第一台設備
  "last_device_check": "2025-01-01T00:00:00Z", // 最後設備檢查時間

  // === 用戶偏好 ===
  "preferences": {
    "language": "zh-TW",
    "notifications": true,
    "auto_upgrade_prompts": true // 是否顯示自動升級提示
  }
}
```

**索引需求**:
- `email` (唯一)
- `current_subscription_id`
- `register_region_timezone` (用於定時重置任務)
- `last_usage_reset_date` (用於重置檢查)

---

### 2. 📋 **subscriptions** 集合

**用途**: 訂閱計劃管理和每日配額配置

```javascript
{
  // 文檔 ID: sub_{user_id}
  
  // === 基本資訊 ===
  "user_id": "firebase-auth-uid",
  "plan": "PRO", // FREE, STARTER, PRO, PREMIUM, MAX
  "status": "active", // active, cancelled, past_due, unpaid
  
  // === 每日配額設定 ===
  "daily_limit_seconds": 10800, // 每日限制秒數（3小時 = 10800秒）
  "current_day_used_seconds": 3600, // 當日已使用秒數
  "last_daily_reset_date": "2025-01-01", // 最後每日重置日期
  "last_daily_reset_at": "2025-01-01T16:00:00Z", // 最後重置時間戳（UTC）
  
  // === 設備管理 ===
  "max_devices": 2, // 最大設備數量
  "active_device_ids": ["device_1", "device_2"], // 活躍設備列表
  "device_limit_exceeded_count": 0, // 設備超限次數統計
  "last_device_limit_check": "2025-01-01T00:00:00Z",
  
  // === 平台支援 ===
  "supported_platforms": ["windows", "macos", "ios", "android"],
  "features": ["語音轉文字"], // 支援的功能列表
  
  // === Stripe 計費資訊 ===
  "stripe_customer_id": "cus_xxx",
  "stripe_subscription_id": "sub_xxx", 
  "billing_cycle": "monthly", // monthly, annually
  "price_amount": 1999, // 價格（分）
  "currency": "USD",
  "auto_renew": true,
  "next_billing_date": "2025-02-01T00:00:00Z",
  "cancel_at_period_end": false,
  
  // === Stripe Webhook 事件追蹤 ===
  "webhook_events": {
    "customer.subscription.created": "2025-01-01T00:00:00Z",
    "customer.subscription.updated": "2025-01-15T00:00:00Z",
    "invoice.payment_succeeded": "2025-01-01T00:00:00Z"
  },
  
  // === 元數據 ===
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-01T00:00:00Z",
  "created_by": "system", // system, admin, user
  "source_app": "desktop" // desktop, mobile, web
}
```

**索引需求**:
- `user_id` (唯一)
- `plan`
- `status`
- `stripe_subscription_id`
- `last_daily_reset_date` (用於重置檢查)

---

### 3. 📱 **devices** 集合

**用途**: 設備註冊和授權管理

```javascript
{
  // 文檔 ID: device_id (設備唯一標識符)
  
  // === 基本資訊 ===
  "device_id": "device_unique_id",
  "user_id": "firebase-auth-uid",
  "subscription_id": "sub_firebase-auth-uid",
  "device_name": "John's MacBook Pro",
  "platform": "macos", // windows, macos, ios, android
  "app_version": "1.0.0",
  "os_version": "14.2.1",
  "device_fingerprint": "unique_device_hash",
  
  // === 狀態管理 (V2.3 增強) ===
  "is_active": true, // 設備是否活躍
  "is_authorized": true, // 設備是否已授權
  "status": "active", // active, inactive, removed, unlinked
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-01T00:00:00Z",
  "last_active_at": "2025-01-01T12:00:00Z",
  "removed_at": null, // 設備移除時間
  "unlinked_at": null, // 設備解綁時間
  
  // === 註冊資訊 ===
  "registration_ip": "*************",
  "user_agent": "SpeechPilot/1.0.0 (macOS 14.2.1)"
}
```

**索引需求**:
- `user_id`
- `subscription_id`
- `is_active`
- `platform`

---

### 4. 📝 **usage_logs** 集合 (聚合表)

**用途**: 使用記錄聚合和 OpenAI Token 追蹤

**文檔 ID 格式**: `{user_id}_{device_id}_{feature_type}_{date}`

```javascript
{
  // 文檔 ID: user123_device456_ai-speech-to-text_2025-01-27

  // === 基本資訊 ===
  "user_id": "firebase-auth-uid",
  "device_id": "device_unique_id",
  "subscription_id": "sub_firebase-auth-uid",
  "feature_type": "ai-speech-to-text", // ai-speech-to-text, direct-speech-to-text
  "platform": "windows",
  "session_date": "2025-01-27",

  // === 聚合使用量 ===
  "duration_seconds": 1500, // 累加的總使用時長（秒）
  "session_count": 5, // 累加的會話數量

  // === OpenAI Token 使用量 (頂層欄位，聚合累加) ===
  "prompt_tokens": 150,      // Chat Completion API 累加
  "completion_tokens": 300,  // Chat Completion API 累加
  "input_tokens": 75,        // Realtime API 累加
  "output_tokens": 125,      // Realtime API 累加
  "total_tokens": 650,       // 所有 token 總和

  // === 時間追蹤 ===
  "first_session_at": "2025-01-27T08:00:00Z", // 第一次會話時間
  "last_session_at": "2025-01-27T18:30:00Z",  // 最後一次會話時間
  "created_at": "2025-01-27T08:00:00Z",
  "updated_at": "2025-01-27T18:30:00Z"
}
```

**聚合邏輯**:
- **相同用戶 + 設備 + 功能類型 + 日期**: 累加所有數值欄位
- **不同功能類型**: 分別記錄 (ai-speech-to-text vs direct-speech-to-text)
- **跨日期**: 創建新記錄

**索引需求**:
- `user_id`
- `device_id`
- `feature_type`
- `session_date`

---

## 📊 **訂閱計劃配置**

### 🎯 **計劃詳情**

| 計劃 | 每日限制 | 設備數量 | 月費 | 年費 |
|------|----------|----------|------|------|
| **FREE** | 10 分鐘 (600秒) | 1 台 | $0 | $0 |
| **STARTER** | 1 小時 (3600秒) | 1 台 | $9.99 | $99 |
| **PRO** | 3 小時 (10800秒) | 2 台 | $19.99 | $199 |
| **PREMIUM** | 8 小時 (28800秒) | 5 台 | $59.99 | $559 |
| **MAX** | 無限制 (-1) | 無限制 (-1) | $129.99 | $1099 |

### 🌍 **地區化配額重置**

**重置邏輯**:
- 每個用戶根據 `register_region_timezone` 在當地時間 00:00 重置配額
- 定時任務每小時檢查需要重置的用戶
- 重置時更新 `current_day_used_seconds = 0`
- 更新 `last_usage_reset_date` 和 `last_usage_reset_at`

**支援時區**:
- UTC-12 到 UTC+14 的所有時區
- 主要地區：美國、歐洲、亞洲、澳洲

---

## 🔄 **定時任務需求**

### 📅 **每日配額重置任務**

**執行頻率**: 每小時執行一次
**任務邏輯**:
1. 計算當前 UTC 時間對應的各時區本地時間
2. 找出需要在當前小時重置的時區
3. 查詢該時區的所有用戶
4. 批量重置用戶的每日配額
5. 記錄重置日誌

**實施方式**:
- Cloud Scheduler + Cloud Functions
- 或 Firebase Functions 定時觸發器

### 5. 💳 **purchases** 集合

**用途**: 購買記錄和 Stripe 支付資訊

```javascript
{
  // 文檔 ID: 自動生成

  // === Stripe 相關資訊 ===
  "stripe_invoice_id": "in_**********",
  "stripe_customer_id": "cus_**********",
  "stripe_subscription_id": "sub_**********",
  "stripe_payment_intent_id": "pi_**********",

  // === 金額資訊 ===
  "amount_total": 2999, // 總金額（分）
  "amount_subtotal": 2999, // 小計
  "amount_tax": [], // 稅額詳情
  "amount_paid": 2999, // 已付金額
  "amount_due": 0, // 應付金額
  "amount_remaining": 0, // 剩餘金額

  // === 貨幣和地區 ===
  "currency": "usd",
  "account_country": "US",

  // === 客戶資訊 ===
  "customer_email": "<EMAIL>",
  "customer_name": "John Doe",
  "customer_phone": "+**********",
  "customer_address": {...},

  // === 發票狀態 ===
  "invoice_status": "paid", // draft, open, paid, uncollectible, void
  "collection_method": "charge_automatically",
  "billing_reason": "subscription_cycle",

  // === 時間資訊 ===
  "invoice_created": "2025-01-01T00:00:00Z",
  "period_start": "2025-01-01T00:00:00Z",
  "period_end": "2025-02-01T00:00:00Z",
  "due_date": "2025-01-01T00:00:00Z",

  // === 支付狀態 ===
  "payment_status": "succeeded", // succeeded, failed, requires_action, pending
  "payment_method": "pm_**********",
  "payment_attempted": true,
  "attempt_count": 1,

  // === 訂閱相關 ===
  "subscription_plan": "PRO",
  "subscription_period": "month",
  "unit_amount": 2999,

  // === 元數據 ===
  "stripe_metadata": {...},
  "webhook_event_type": "invoice.payment_succeeded",

  // === 內部狀態 ===
  "processed": true,
  "processing_errors": [],

  // === 應用相關 ===
  "user_id": "user_123",
  "source_app": "web",

  // === 時間戳 ===
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-01T00:00:00Z"
}
```

### 6. 📱 **device_action_logs** 集合 (已移除)

**狀態**: 已移除 - 簡化架構，減少不必要的日誌記錄

---

## 🔍 **查詢模式優化**

### 常用查詢
1. **配額檢查**: `users/{user_id}` → 單文檔查詢
2. **設備驗證**: `devices/{device_id}` → 單文檔查詢  
3. **使用記錄**: `usage_logs` where `user_id` = xxx
4. **重置任務**: `users` where `register_region_timezone` = xxx

### 性能優化
- 用戶表包含冗餘欄位避免跨表查詢
- 設備列表在用戶和訂閱表中同步
- 每日配額在用戶和訂閱表中同步
- 適當的複合索引支援複雜查詢

這個架構支援全球化的每日配額管理，為 SpeechPilot 的國際化運營提供了堅實的技術基礎！
