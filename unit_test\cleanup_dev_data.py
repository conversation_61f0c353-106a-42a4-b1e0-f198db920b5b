#!/usr/bin/env python3
"""
清理開發環境數據腳本
清空 speakoneai-dev 數據庫中的所有測試數據
"""

import os
import firebase_admin
from firebase_admin import credentials, firestore

def initialize_firebase():
    """初始化 Firebase Admin SDK"""
    try:
        # 檢查是否已經初始化
        try:
            firebase_admin.get_app()
            print("✅ Firebase Admin 已初始化")
            return True
        except ValueError:
            pass
        
        # 設置服務帳戶金鑰路徑
        service_account_path = "speakoneai-dev-9f995-firebase-adminsdk-fbsvc-5f34f67ccf.json"
        
        if not os.path.exists(service_account_path):
            print(f"❌ 找不到服務帳戶金鑰文件: {service_account_path}")
            return False
        
        # 初始化 Firebase Admin，指定開發數據庫
        cred = credentials.Certificate(service_account_path)
        firebase_admin.initialize_app(cred, {
            'databaseURL': 'https://speakoneai-dev-9f995-fs-speakoneai-dev-rtdb.firebaseio.com/'
        })
        
        print("✅ Firebase Admin 初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ Firebase 初始化失敗: {str(e)}")
        return False

def delete_collection(db, collection_name):
    """刪除集合中的所有文檔"""
    try:
        # 設置數據庫ID為開發環境
        if hasattr(db, '_database_id'):
            db._database_id = 'fs-speakoneai-dev'
        
        collection_ref = db.collection(collection_name)
        docs = collection_ref.get()
        
        deleted_count = 0
        for doc in docs:
            doc.reference.delete()
            deleted_count += 1
        
        print(f"✅ 已刪除 {collection_name} 集合中的 {deleted_count} 個文檔")
        return deleted_count
        
    except Exception as e:
        print(f"❌ 刪除 {collection_name} 集合失敗: {str(e)}")
        return 0

def main():
    """主函數"""
    print("🧹 開始清理開發環境數據")
    print("=" * 50)
    print("⚠️  警告：這將刪除 speakoneai-dev 數據庫中的所有數據！")
    
    # 確認操作
    confirm = input("請輸入 'YES' 確認清理開發環境數據: ")
    if confirm != 'YES':
        print("❌ 操作已取消")
        return
    
    # 初始化 Firebase
    if not initialize_firebase():
        print("❌ Firebase 初始化失敗，無法進行清理")
        return
    
    # 獲取 Firestore 客戶端（使用預設數據庫）
    db = firestore.client()
    
    # 要清理的集合列表
    collections = [
        'users',
        'subscriptions', 
        'devices',
        'usage_logs',
        'billing_history'
    ]
    
    total_deleted = 0
    
    # 逐個清理集合
    for collection_name in collections:
        print(f"\n🗑️  清理 {collection_name} 集合...")
        deleted = delete_collection(db, collection_name)
        total_deleted += deleted
    
    print("\n" + "=" * 50)
    print("🎉 清理完成！")
    print(f"📊 總共刪除了 {total_deleted} 個文檔")
    print("✅ 開發環境數據已清空，可以進行新的測試")

if __name__ == "__main__":
    main()
