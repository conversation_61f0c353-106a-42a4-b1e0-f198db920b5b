"""
測試 purchases 表初始化
"""

import unittest
import sys
import os
import time
from datetime import datetime

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend-api'))

class TestPurchasesTableInit(unittest.TestCase):
    """測試 purchases 表初始化"""
    
    def setUp(self):
        """測試設置"""
        self.timestamp = str(int(time.time()))
        self.test_user_id = f"test_user_{self.timestamp}"
        
    def test_create_subscription_with_purchase_record(self):
        """測試創建訂閱時初始化 purchases 表"""
        print("\n💳 測試 purchases 表初始化")
        print("=" * 40)
        
        try:
            # 模擬付費訂閱創建請求
            subscription_data = {
                # 計劃資訊
                "plan": "PRO",
                "daily_limit_seconds": 10800,
                "max_devices": 2,
                "price_amount": 1999,
                "currency": "usd",
                "billing_cycle": "monthly",
                
                # Stripe 支付資訊
                "stripe_invoice_id": f"in_test_{self.timestamp}",
                "stripe_customer_id": f"cus_test_{self.timestamp}",
                "stripe_subscription_id": f"sub_test_{self.timestamp}",
                "stripe_payment_intent_id": f"pi_test_{self.timestamp}",
                "amount_total": 1999,
                "amount_paid": 1999,
                "payment_status": "succeeded",
                "invoice_created": datetime.utcnow().isoformat(),
                "period_start": datetime.utcnow().isoformat(),
                "period_end": "2025-02-27T00:00:00Z",
                
                # 客戶資訊
                "customer_email": f"test_{self.timestamp}@example.com",
                "customer_name": "Test User"
            }
            
            # 模擬 purchases 表記錄結構
            expected_purchase_record = {
                # Stripe 相關資訊
                "stripe_invoice_id": subscription_data["stripe_invoice_id"],
                "stripe_customer_id": subscription_data["stripe_customer_id"],
                "stripe_subscription_id": subscription_data["stripe_subscription_id"],
                "stripe_payment_intent_id": subscription_data["stripe_payment_intent_id"],
                
                # 金額資訊
                "amount_total": subscription_data["amount_total"],
                "amount_subtotal": subscription_data["amount_total"],
                "amount_paid": subscription_data["amount_paid"],
                "amount_due": 0,
                "amount_remaining": 0,
                
                # 貨幣和地區
                "currency": subscription_data["currency"],
                "account_country": "US",
                
                # 客戶資訊
                "customer_email": subscription_data["customer_email"],
                "customer_name": subscription_data["customer_name"],
                
                # 發票狀態
                "invoice_status": "paid",
                "collection_method": "charge_automatically",
                "billing_reason": "subscription_cycle",
                
                # 支付狀態
                "payment_status": subscription_data["payment_status"],
                "payment_attempted": True,
                "attempt_count": 1,
                
                # 訂閱相關
                "subscription_plan": subscription_data["plan"],
                "subscription_period": subscription_data["billing_cycle"],
                "unit_amount": subscription_data["price_amount"],
                
                # 內部狀態
                "processed": True,
                "processing_errors": [],
                
                # 應用相關
                "user_id": self.test_user_id,
                "source_app": "web",
                
                # 時間戳
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            # 驗證購買記錄結構
            self.assertEqual(expected_purchase_record["stripe_invoice_id"], subscription_data["stripe_invoice_id"])
            self.assertEqual(expected_purchase_record["amount_total"], 1999)
            self.assertEqual(expected_purchase_record["payment_status"], "succeeded")
            self.assertEqual(expected_purchase_record["subscription_plan"], "PRO")
            self.assertTrue(expected_purchase_record["processed"])
            
            print("✅ purchases 表記錄結構正確")
            print(f"   發票 ID: {expected_purchase_record['stripe_invoice_id']}")
            print(f"   客戶 ID: {expected_purchase_record['stripe_customer_id']}")
            print(f"   訂閱計劃: {expected_purchase_record['subscription_plan']}")
            print(f"   支付金額: ${expected_purchase_record['amount_total']/100:.2f}")
            print(f"   支付狀態: {expected_purchase_record['payment_status']}")
            print(f"   處理狀態: {'已處理' if expected_purchase_record['processed'] else '未處理'}")
            
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")
            self.fail(f"purchases 表初始化測試失敗: {str(e)}")
    
    def test_free_subscription_no_purchase_record(self):
        """測試 FREE 訂閱不創建 purchases 記錄"""
        print("\n🆓 測試 FREE 訂閱不創建 purchases 記錄")
        print("=" * 40)
        
        try:
            # 模擬 FREE 訂閱創建
            free_subscription_data = {
                "plan": "FREE",
                "daily_limit_seconds": 600,
                "max_devices": 1,
                "source_app": "desktop"
                # 沒有 Stripe 相關資訊
            }
            
            # 檢查是否應該創建購買記錄
            should_create_purchase = (
                free_subscription_data["plan"] != "FREE" and 
                "stripe_invoice_id" in free_subscription_data
            )
            
            # 驗證邏輯
            self.assertFalse(should_create_purchase)
            self.assertEqual(free_subscription_data["plan"], "FREE")
            self.assertNotIn("stripe_invoice_id", free_subscription_data)
            
            print("✅ FREE 訂閱正確處理")
            print(f"   訂閱計劃: {free_subscription_data['plan']}")
            print(f"   創建購買記錄: {'是' if should_create_purchase else '否'}")
            print(f"   原因: {'FREE 計劃無需支付' if not should_create_purchase else ''}")
            
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")
            self.fail(f"FREE 訂閱測試失敗: {str(e)}")
    
    def test_purchases_table_structure(self):
        """測試 purchases 表結構完整性"""
        print("\n📊 測試 purchases 表結構")
        print("=" * 40)
        
        try:
            # 定義完整的 purchases 表結構
            purchases_table_structure = {
                # Stripe 相關資訊
                "stripe_fields": [
                    "stripe_invoice_id",
                    "stripe_customer_id", 
                    "stripe_subscription_id",
                    "stripe_payment_intent_id"
                ],
                
                # 金額資訊
                "amount_fields": [
                    "amount_total",
                    "amount_subtotal",
                    "amount_tax",
                    "amount_paid",
                    "amount_due",
                    "amount_remaining"
                ],
                
                # 狀態資訊
                "status_fields": [
                    "invoice_status",
                    "payment_status",
                    "collection_method",
                    "billing_reason"
                ],
                
                # 客戶資訊
                "customer_fields": [
                    "customer_email",
                    "customer_name",
                    "customer_phone",
                    "customer_address"
                ],
                
                # 訂閱資訊
                "subscription_fields": [
                    "subscription_plan",
                    "subscription_period",
                    "unit_amount"
                ],
                
                # 內部管理
                "internal_fields": [
                    "user_id",
                    "processed",
                    "processing_errors",
                    "source_app",
                    "created_at",
                    "updated_at"
                ]
            }
            
            # 驗證結構完整性
            total_fields = sum(len(fields) for fields in purchases_table_structure.values())
            required_categories = len(purchases_table_structure)
            
            self.assertGreaterEqual(total_fields, 25)  # 至少 25 個欄位
            self.assertEqual(required_categories, 6)   # 6 個類別
            
            print("✅ purchases 表結構完整")
            print(f"   總欄位數: {total_fields}")
            print(f"   欄位類別: {required_categories}")
            
            for category, fields in purchases_table_structure.items():
                print(f"   {category}: {len(fields)} 個欄位")
            
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")
            self.fail(f"purchases 表結構測試失敗: {str(e)}")

def run_purchases_table_init_tests():
    """執行 purchases 表初始化測試"""
    print("🧪 開始 purchases 表初始化測試...")
    print("=" * 50)
    
    # 創建測試套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestPurchasesTableInit)
    
    # 執行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("=" * 50)
    
    if result.wasSuccessful():
        print("🎉 所有 purchases 表初始化測試通過！")
        print("\n📋 測試場景總結:")
        print("✅ 付費訂閱創建購買記錄")
        print("✅ FREE 訂閱不創建購買記錄")
        print("✅ purchases 表結構完整")
        print("\n🚀 purchases 表初始化驗證完成！")
        return True
    else:
        print(f"❌ 測試失敗: {len(result.failures)} 個失敗, {len(result.errors)} 個錯誤")
        
        if result.failures:
            print("\n失敗的測試:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
        
        if result.errors:
            print("\n錯誤的測試:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
        
        return False

if __name__ == '__main__':
    success = run_purchases_table_init_tests()
    sys.exit(0 if success else 1)
