"""
Enhanced device management functionality for SpeechPilot Firebase Functions
"""

import logging
import uuid
from datetime import datetime, timedelta
from firebase_functions import https_fn
from firebase_admin import firestore
from typing import Dict, Any, List, Optional

from ..utils.validation import validate_auth, validate_device_id, validate_platform
from ..utils.database import get_user_data
from ..utils.constants import SUBSCRIPTION_PLANS, ERROR_MESSAGES, Platform

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端"""
    return firestore.client()

def get_device_fingerprint(device_info: Dict[str, Any]) -> str:
    """
    生成設備指紋用於唯一識別設備
    
    Args:
        device_info: 設備資訊
        
    Returns:
        str: 設備指紋
    """
    # 組合關鍵設備資訊生成指紋
    fingerprint_data = [
        device_info.get('device_model', ''),
        device_info.get('os_version', ''),
        device_info.get('cpu_info', ''),
        device_info.get('memory_info', ''),
        device_info.get('screen_resolution', ''),
        device_info.get('timezone', ''),
        device_info.get('language', '')
    ]
    
    # 生成基於設備資訊的唯一標識
    fingerprint_string = '|'.join(str(item) for item in fingerprint_data)
    return str(hash(fingerprint_string))

def collect_device_data(platform: str, device_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    收集並標準化設備資料
    
    Args:
        platform: 平台類型
        device_info: 原始設備資訊
        
    Returns:
        Dict[str, Any]: 標準化的設備資料
    """
    standardized_data = {
        'platform': platform,
        'device_fingerprint': get_device_fingerprint(device_info),
        'collected_at': firestore.SERVER_TIMESTAMP
    }
    
    # 根據平台收集特定資料
    if platform == Platform.WINDOWS.value:
        standardized_data.update({
            'os_version': device_info.get('os_version'),
            'cpu_info': device_info.get('cpu_info'),
            'memory_gb': device_info.get('memory_gb'),
            'computer_name': device_info.get('computer_name'),
            'username': device_info.get('username'),
            'domain': device_info.get('domain'),
            'screen_resolution': device_info.get('screen_resolution'),
            'timezone': device_info.get('timezone')
        })
    
    elif platform == Platform.MACOS.value:
        standardized_data.update({
            'os_version': device_info.get('os_version'),
            'model_identifier': device_info.get('model_identifier'),
            'cpu_info': device_info.get('cpu_info'),
            'memory_gb': device_info.get('memory_gb'),
            'computer_name': device_info.get('computer_name'),
            'username': device_info.get('username'),
            'screen_resolution': device_info.get('screen_resolution'),
            'timezone': device_info.get('timezone')
        })
    
    elif platform == Platform.IOS.value:
        standardized_data.update({
            'ios_version': device_info.get('ios_version'),
            'device_model': device_info.get('device_model'),  # iPhone 14 Pro
            'device_name': device_info.get('device_name'),    # User's iPhone
            'identifier_for_vendor': device_info.get('identifier_for_vendor'),
            'screen_scale': device_info.get('screen_scale'),
            'screen_size': device_info.get('screen_size'),
            'timezone': device_info.get('timezone'),
            'language': device_info.get('language'),
            'region': device_info.get('region')
        })
    
    elif platform == Platform.ANDROID.value:
        standardized_data.update({
            'android_version': device_info.get('android_version'),
            'device_model': device_info.get('device_model'),
            'device_brand': device_info.get('device_brand'),
            'device_name': device_info.get('device_name'),
            'android_id': device_info.get('android_id'),
            'screen_density': device_info.get('screen_density'),
            'screen_size': device_info.get('screen_size'),
            'timezone': device_info.get('timezone'),
            'language': device_info.get('language'),
            'region': device_info.get('region')
        })
    
    return standardized_data

def check_platform_support(user_plan: str, platform: str) -> bool:
    """
    檢查用戶計劃是否支援指定平台
    
    Args:
        user_plan: 用戶訂閱計劃
        platform: 平台類型
        
    Returns:
        bool: 是否支援
    """
    plan_config = SUBSCRIPTION_PLANS.get(user_plan, SUBSCRIPTION_PLANS["FREE"])
    supported_platforms = plan_config.get("supported_platforms", [])
    return platform in supported_platforms

def get_active_devices(user_id: str) -> List[Dict[str, Any]]:
    """
    獲取用戶的活躍設備列表
    
    Args:
        user_id: 用戶ID
        
    Returns:
        List[Dict[str, Any]]: 活躍設備列表
    """
    db = get_db()
    devices_query = db.collection('devices').where(
        'user_id', '==', user_id
    ).where(
        'is_active', '==', True
    ).stream()
    
    devices = []
    for doc in devices_query:
        device_data = doc.to_dict()
        device_data['id'] = doc.id
        devices.append(device_data)
    
    return devices

def check_concurrent_usage_limit(user_id: str, user_plan: str) -> Dict[str, Any]:
    """
    檢查同時使用設備數量限制
    
    Args:
        user_id: 用戶ID
        user_plan: 用戶訂閱計劃
        
    Returns:
        Dict[str, Any]: 檢查結果
    """
    plan_config = SUBSCRIPTION_PLANS.get(user_plan, SUBSCRIPTION_PLANS["FREE"])
    max_concurrent = plan_config.get("concurrent_sessions", 1)
    
    if max_concurrent == -1:
        return {
            "can_use": True,
            "current_sessions": 0,
            "max_concurrent": -1
        }
    
    # 檢查最近5分鐘內有活動的設備
    five_minutes_ago = datetime.now() - timedelta(minutes=5)

    db = get_db()
    active_sessions_query = db.collection('usage_sessions').where(
        'user_id', '==', user_id
    ).where(
        'is_active', '==', True
    ).where(
        'last_activity', '>=', five_minutes_ago
    ).stream()
    
    current_sessions = len(list(active_sessions_query))
    
    return {
        "can_use": current_sessions < max_concurrent,
        "current_sessions": current_sessions,
        "max_concurrent": max_concurrent
    }

def register_device_enhanced(request: https_fn.CallableRequest) -> Dict[str, Any]:
    """
    增強版設備註冊功能
    
    Args:
        request: Firebase Functions 請求對象
        
    Returns:
        Dict[str, Any]: 註冊結果
    """
    try:
        # 驗證用戶認證
        user_id = validate_auth(request)
        
        # 獲取請求數據
        data = request.data or {}
        device_id = data.get('device_id')
        device_name = data.get('device_name', 'Unknown Device')
        platform = data.get('platform', 'unknown')
        app_version = data.get('app_version', 'unknown')
        device_info = data.get('device_info', {})
        
        logger.info(f"Enhanced device registration for user {user_id}: {device_id}")
        
        # 驗證必要參數
        if not device_id or not validate_device_id(device_id):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="Invalid device_id"
            )
        
        if not validate_platform(platform):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="Invalid platform"
            )
        
        # 獲取用戶資料
        user_data = get_user_data(user_id)
        subscription = user_data.get('subscription', {})
        user_plan = subscription.get('plan', 'FREE')
        
        # 檢查平台支援
        if not check_platform_support(user_plan, platform):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.PERMISSION_DENIED,
                message=ERROR_MESSAGES["PLATFORM_NOT_SUPPORTED"]
            )
        
        # 收集標準化設備資料
        standardized_device_data = collect_device_data(platform, device_info)
        
        # 檢查設備是否已存在
        db = get_db()
        existing_device_query = db.collection('devices').where(
            'user_id', '==', user_id
        ).where(
            'device_id', '==', device_id
        ).limit(1).stream()
        
        existing_device = None
        for doc in existing_device_query:
            existing_device = doc
            break
        
        if existing_device:
            # 更新現有設備
            existing_device.reference.update({
                'device_name': device_name,
                'app_version': app_version,
                'last_active_at': firestore.SERVER_TIMESTAMP,
                'updated_at': firestore.SERVER_TIMESTAMP,
                'is_active': True,
                **standardized_device_data
            })
            
            return {
                "success": True,
                "device_id": device_id,
                "is_new": False,
                "message": "設備資訊已更新",
                "platform_supported": True
            }
        
        # 檢查設備數量限制
        plan_config = SUBSCRIPTION_PLANS.get(user_plan, SUBSCRIPTION_PLANS["FREE"])
        max_devices = plan_config["max_devices"]
        
        if max_devices != -1:
            active_devices = get_active_devices(user_id)
            if len(active_devices) >= max_devices:
                raise https_fn.HttpsError(
                    code=https_fn.FunctionsErrorCode.RESOURCE_EXHAUSTED,
                    message=ERROR_MESSAGES["DEVICE_LIMIT_EXCEEDED"]
                )
        
        # 創建新設備記錄
        device_data = {
            'user_id': user_id,
            'device_id': device_id,
            'device_name': device_name,
            'app_version': app_version,
            'is_active': True,
            'created_at': firestore.SERVER_TIMESTAMP,
            'updated_at': firestore.SERVER_TIMESTAMP,
            'last_active_at': firestore.SERVER_TIMESTAMP,
            'registration_ip': getattr(request.raw_request, 'remote_addr', 'unknown'),
            **standardized_device_data
        }
        
        # 添加設備到 Firestore
        device_ref = db.collection('devices').document()
        device_ref.set(device_data)
        
        logger.info(f"Enhanced device {device_id} registered for user {user_id}")
        
        return {
            "success": True,
            "device_id": device_id,
            "is_new": True,
            "message": "設備註冊成功",
            "platform_supported": True,
            "device_count": len(get_active_devices(user_id)),
            "max_devices": max_devices
        }
        
    except https_fn.HttpsError:
        raise
    except Exception as e:
        logger.error(f"Error in enhanced device registration: {str(e)}")
        raise https_fn.HttpsError(
            code=https_fn.FunctionsErrorCode.INTERNAL,
            message=ERROR_MESSAGES["INTERNAL_ERROR"]
        )
