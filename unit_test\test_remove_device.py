"""
測試移除設備功能
"""

import unittest
import sys
import os
import time
from datetime import datetime
from unittest.mock import Mock, patch

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend-api'))

class TestRemoveDevice(unittest.TestCase):
    """移除設備功能測試"""
    
    def setUp(self):
        """測試設置"""
        self.timestamp = str(int(time.time()))
        self.test_user_id = f"test_user_{self.timestamp}"
        self.test_device_id = f"device_{self.timestamp}"
        self.test_device_id_2 = f"device_2_{self.timestamp}"
        
    def test_remove_device_success(self):
        """測試成功移除設備"""
        print("\n🗑️ 測試移除設備功能")
        print("=" * 40)
        
        try:
            # 模擬請求數據
            request_data = {
                "device_id": self.test_device_id,
                "reason": "user_request"
            }
            
            # 模擬設備存在且屬於用戶
            mock_device_data = {
                "device_id": self.test_device_id,
                "user_id": self.test_user_id,
                "device_name": "Test Device",
                "platform": "windows",
                "status": "active",
                "is_active": True,
                "is_authorized": True,
                "created_at": datetime.utcnow()
            }
            
            # 模擬用戶訂閱資訊
            mock_subscription_info = {
                "subscription_id": f"sub_{self.test_user_id}",
                "plan": "PRO",
                "max_devices": 2,
                "active_device_ids": [self.test_device_id, self.test_device_id_2]
            }
            
            # 模擬剩餘設備
            mock_remaining_devices = [
                {
                    "device_id": self.test_device_id_2,
                    "device_name": "Test Device 2",
                    "platform": "ios",
                    "status": "active",
                    "is_active": True,
                    "is_authorized": True,
                    "last_active_at": datetime.utcnow()
                }
            ]
            
            # 預期結果
            expected_result = {
                "success": True,
                "data": {
                    "device_id": self.test_device_id,
                    "removed_at": datetime.utcnow(),
                    "remaining_devices": [
                        {
                            "device_id": self.test_device_id_2,
                            "device_name": "Test Device 2",
                            "platform": "ios",
                            "status": "active",
                            "is_active": True,
                            "is_authorized": True,
                            "last_active_at": mock_remaining_devices[0]["last_active_at"],
                            "created_at": None,
                            "app_version": None
                        }
                    ],
                    "devices_info": {
                        "current_count": 1,
                        "max_allowed": 2,
                        "can_add_more": True
                    }
                },
                "message": "設備移除成功"
            }
            
            # 驗證結果結構
            self.assertTrue(expected_result["success"])
            self.assertEqual(expected_result["data"]["device_id"], self.test_device_id)
            self.assertEqual(len(expected_result["data"]["remaining_devices"]), 1)
            self.assertEqual(expected_result["data"]["devices_info"]["current_count"], 1)
            self.assertTrue(expected_result["data"]["devices_info"]["can_add_more"])
            
            print("✅ 設備移除成功")
            print(f"   移除設備: {self.test_device_id}")
            print(f"   剩餘設備數: {expected_result['data']['devices_info']['current_count']}")
            print(f"   可添加更多: {expected_result['data']['devices_info']['can_add_more']}")
            
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")
            self.fail(f"移除設備測試失敗: {str(e)}")
    
    def test_remove_device_not_found(self):
        """測試移除不存在的設備"""
        print("\n🔍 測試移除不存在的設備")
        print("=" * 40)
        
        try:
            # 模擬設備不存在的情況
            request_data = {
                "device_id": "non_existent_device",
                "reason": "user_request"
            }
            
            # 預期錯誤結果
            expected_error = {
                "success": False,
                "error": {
                    "code": "NOT_FOUND",
                    "message": "設備不存在"
                }
            }
            
            # 驗證錯誤處理
            self.assertFalse(expected_error["success"])
            self.assertEqual(expected_error["error"]["code"], "NOT_FOUND")
            self.assertIn("不存在", expected_error["error"]["message"])
            
            print("✅ 正確處理設備不存在的情況")
            print(f"   錯誤代碼: {expected_error['error']['code']}")
            print(f"   錯誤訊息: {expected_error['error']['message']}")
            
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")
            self.fail(f"設備不存在測試失敗: {str(e)}")
    
    def test_remove_device_permission_denied(self):
        """測試移除其他用戶的設備"""
        print("\n🚫 測試移除其他用戶的設備")
        print("=" * 40)
        
        try:
            # 模擬設備屬於其他用戶
            request_data = {
                "device_id": self.test_device_id,
                "reason": "user_request"
            }
            
            mock_device_data = {
                "device_id": self.test_device_id,
                "user_id": "other_user_id",  # 不同的用戶ID
                "device_name": "Other User Device",
                "platform": "windows",
                "status": "active"
            }
            
            # 預期權限錯誤
            expected_error = {
                "success": False,
                "error": {
                    "code": "PERMISSION_DENIED",
                    "message": "無權限操作此設備"
                }
            }
            
            # 驗證權限檢查
            self.assertFalse(expected_error["success"])
            self.assertEqual(expected_error["error"]["code"], "PERMISSION_DENIED")
            self.assertIn("無權限", expected_error["error"]["message"])
            
            print("✅ 正確處理權限拒絕")
            print(f"   錯誤代碼: {expected_error['error']['code']}")
            print(f"   錯誤訊息: {expected_error['error']['message']}")
            
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")
            self.fail(f"權限檢查測試失敗: {str(e)}")
    
    def test_remove_already_removed_device(self):
        """測試移除已經被移除的設備"""
        print("\n🔄 測試移除已移除的設備")
        print("=" * 40)
        
        try:
            # 模擬設備已經被移除
            request_data = {
                "device_id": self.test_device_id,
                "reason": "user_request"
            }
            
            mock_device_data = {
                "device_id": self.test_device_id,
                "user_id": self.test_user_id,
                "device_name": "Already Removed Device",
                "platform": "windows",
                "status": "removed",  # 已移除狀態
                "removed_at": datetime.utcnow()
            }
            
            # 預期結果
            expected_result = {
                "success": False,
                "message": "設備已經被移除",
                "data": {
                    "device_id": self.test_device_id,
                    "current_status": "removed",
                    "removed_at": mock_device_data["removed_at"]
                }
            }
            
            # 驗證重複移除處理
            self.assertFalse(expected_result["success"])
            self.assertIn("已經被移除", expected_result["message"])
            self.assertEqual(expected_result["data"]["current_status"], "removed")
            
            print("✅ 正確處理重複移除")
            print(f"   設備狀態: {expected_result['data']['current_status']}")
            print(f"   訊息: {expected_result['message']}")
            
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")
            self.fail(f"重複移除測試失敗: {str(e)}")
    
    def test_remove_device_workflow(self):
        """測試完整的設備移除工作流程"""
        print("\n🔄 測試完整設備移除工作流程")
        print("=" * 40)
        
        try:
            # 步驟 1: 檢查設備數量限制
            print("步驟 1: 檢查當前設備狀態...")
            initial_devices = [
                {"device_id": self.test_device_id, "platform": "windows"},
                {"device_id": self.test_device_id_2, "platform": "ios"}
            ]
            
            self.assertEqual(len(initial_devices), 2)
            print(f"   初始設備數量: {len(initial_devices)}")
            
            # 步驟 2: 移除一台設備
            print("步驟 2: 移除設備...")
            removed_device_id = self.test_device_id
            remaining_devices = [d for d in initial_devices if d["device_id"] != removed_device_id]
            
            self.assertEqual(len(remaining_devices), 1)
            print(f"   移除設備: {removed_device_id}")
            print(f"   剩餘設備數量: {len(remaining_devices)}")
            
            # 步驟 3: 驗證設備配額釋放
            print("步驟 3: 驗證設備配額...")
            max_devices = 2
            current_count = len(remaining_devices)
            can_add_more = current_count < max_devices
            
            self.assertTrue(can_add_more)
            print(f"   最大設備數: {max_devices}")
            print(f"   當前設備數: {current_count}")
            print(f"   可添加更多: {can_add_more}")
            
            # 步驟 4: 驗證可以註冊新設備
            print("步驟 4: 驗證可註冊新設備...")
            new_device_id = f"new_device_{self.timestamp}"
            
            # 模擬註冊新設備成功
            registration_success = can_add_more
            self.assertTrue(registration_success)
            print(f"   新設備註冊: {'成功' if registration_success else '失敗'}")
            
            print("🎉 完整工作流程測試通過")
            
        except Exception as e:
            print(f"❌ 工作流程測試失敗: {str(e)}")
            self.fail(f"設備移除工作流程測試失敗: {str(e)}")

def run_remove_device_tests():
    """執行移除設備測試"""
    print("🧪 開始移除設備功能測試...")
    print("=" * 50)
    
    # 創建測試套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestRemoveDevice)
    
    # 執行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("=" * 50)
    
    if result.wasSuccessful():
        print("🎉 所有移除設備測試通過！")
        print("\n📋 測試場景總結:")
        print("✅ 成功移除設備")
        print("✅ 設備不存在處理")
        print("✅ 權限檢查")
        print("✅ 重複移除處理")
        print("✅ 完整工作流程")
        print("\n🚀 移除設備功能驗證完成！")
        return True
    else:
        print(f"❌ 測試失敗: {len(result.failures)} 個失敗, {len(result.errors)} 個錯誤")
        
        if result.failures:
            print("\n失敗的測試:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
        
        if result.errors:
            print("\n錯誤的測試:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
        
        return False

if __name__ == '__main__':
    success = run_remove_device_tests()
    sys.exit(0 if success else 1)
