# 🔥 Web 團隊 Firebase SDK 使用指南

**目標**: 使用 Firebase SDK 的 Callable Functions 替代手動 HTTP 調用  
**優勢**: 自動認證、錯誤處理、類型安全、性能優化  

---

## 🚀 **立即修復 500 錯誤**

### **問題根源**
您目前使用的是手動 HTTP 調用，但我們的 API 是 Firebase Callable Functions，需要使用 Firebase SDK。

### **解決方案**
使用 Firebase Functions SDK 的 `httpsCallable` 替代 `fetch`。

---

## 📦 **1. 安裝依賴**

```bash
npm install firebase
```

---

## 🔧 **2. 設置 Firebase Functions**

```javascript
// lib/firebase.js
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFunctions, httpsCallable } from 'firebase/functions';

const firebaseConfig = {
  apiKey: "AIzaSyAHmnBBM3pVQTWWX5t5034_ffw0YxekNAo",
  authDomain: "speakoneai-dev-9f995.firebaseapp.com",
  projectId: "speakoneai-dev-9f995",
  storageBucket: "speakoneai-dev-9f995.firebasestorage.app",
  messagingSenderId: "368906331419",
  appId: "1:368906331419:web:4d2d174bdc3aceb8a11fbe"
};

// 初始化 Firebase
const app = initializeApp(firebaseConfig);

// 初始化服務
export const auth = getAuth(app);
export const functions = getFunctions(app, 'asia-east1'); // 重要：指定區域

// 創建 API 函數
export const api = {
  // 用戶管理
  getUserDashboard: httpsCallable(functions, 'get_user_dashboard'),
  createOrUpdateUser: httpsCallable(functions, 'create_or_update_user'),
  
  // 訂閱管理
  createSubscription: httpsCallable(functions, 'create_subscription'),
  
  // 設備管理
  validateOrRegisterDevice: httpsCallable(functions, 'validate_or_register_device'),
  removeDevice: httpsCallable(functions, 'remove_device'),
  
  // 使用量管理
  checkUsageBeforeRecording: httpsCallable(functions, 'check_usage_before_recording'),
  submitUsage: httpsCallable(functions, 'submit_usage'),
  calculateUsageAfterRecording: httpsCallable(functions, 'calculate_usage_after_recording')
};
```

---

## 🔄 **3. 替換現有的 API 調用**

### **❌ 舊的 HTTP 調用方式**

```javascript
// 不要再使用這種方式
const response = await fetch(`${BACKEND_API_URL}/get_user_dashboard`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
```

### **✅ 新的 Firebase SDK 方式**

```javascript
import { api } from '../lib/firebase';

// 簡單調用，Firebase 自動處理認證
const result = await api.getUserDashboard();

if (result.data.success) {
  const dashboardData = result.data.data;
  console.log('用戶儀表板:', dashboardData);
} else {
  console.error('錯誤:', result.data.error);
}
```

---

## 📋 **4. 完整的 API 使用範例**

### **用戶儀表板**

```javascript
// hooks/useDashboard.js
import { useState, useEffect } from 'react';
import { api } from '../lib/firebase';

export const useDashboard = () => {
  const [dashboard, setDashboard] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadDashboard = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await api.getUserDashboard();
      
      if (result.data.success) {
        setDashboard(result.data.data);
      } else {
        throw new Error(result.data.error?.message || '獲取儀表板失敗');
      }
    } catch (err) {
      console.error('儀表板錯誤:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboard();
  }, []);

  return { dashboard, loading, error, reload: loadDashboard };
};
```

### **設備管理**

```javascript
// hooks/useDevices.js
import { api } from '../lib/firebase';

export const useDevices = () => {
  const removeDevice = async (deviceId, reason = 'user_request') => {
    try {
      const result = await api.removeDevice({
        device_id: deviceId,
        reason: reason
      });

      if (result.data.success) {
        return result.data.data;
      } else {
        throw new Error(result.data.error?.message || '移除設備失敗');
      }
    } catch (error) {
      console.error('移除設備錯誤:', error);
      throw error;
    }
  };

  const validateOrRegisterDevice = async (deviceData) => {
    try {
      const result = await api.validateOrRegisterDevice(deviceData);
      return result.data;
    } catch (error) {
      console.error('設備驗證錯誤:', error);
      throw error;
    }
  };

  return { removeDevice, validateOrRegisterDevice };
};
```

### **訂閱管理**

```javascript
// hooks/useSubscription.js
import { api } from '../lib/firebase';

export const useSubscription = () => {
  const createSubscription = async (subscriptionData) => {
    try {
      const result = await api.createSubscription(subscriptionData);

      if (result.data.success) {
        return result.data.data;
      } else {
        throw new Error(result.data.error?.message || '創建訂閱失敗');
      }
    } catch (error) {
      console.error('創建訂閱錯誤:', error);
      throw error;
    }
  };

  return { createSubscription };
};
```

### **使用量管理**

```javascript
// hooks/useUsage.js
import { api } from '../lib/firebase';

export const useUsage = () => {
  const submitUsage = async (usageData) => {
    try {
      const result = await api.submitUsage(usageData);

      if (result.data.success) {
        return result.data.data;
      } else {
        throw new Error(result.data.error?.message || '提交使用量失敗');
      }
    } catch (error) {
      console.error('提交使用量錯誤:', error);
      throw error;
    }
  };

  const checkUsage = async (checkData) => {
    try {
      const result = await api.checkUsageBeforeRecording(checkData);
      return result.data;
    } catch (error) {
      console.error('檢查使用量錯誤:', error);
      throw error;
    }
  };

  return { submitUsage, checkUsage };
};
```

---

## 🎯 **5. 錯誤處理**

### **統一錯誤處理**

```javascript
// utils/errorHandler.js
export const handleApiError = (error) => {
  console.error('API 錯誤:', error);

  // Firebase Functions 錯誤
  if (error.code) {
    switch (error.code) {
      case 'unauthenticated':
        return '用戶未認證，請重新登入';
      case 'permission-denied':
        return '權限不足';
      case 'not-found':
        return '資源不存在';
      case 'already-exists':
        return '資源已存在';
      case 'resource-exhausted':
        return '資源配額已用完';
      case 'failed-precondition':
        return '操作條件不滿足';
      case 'internal':
        return '內部服務錯誤';
      default:
        return error.message || '未知錯誤';
    }
  }

  return error.message || '操作失敗';
};
```

---

## 📊 **6. 完整的組件範例**

```javascript
// components/Dashboard.jsx
import React from 'react';
import { useDashboard } from '../hooks/useDashboard';
import { useDevices } from '../hooks/useDevices';
import { handleApiError } from '../utils/errorHandler';

const Dashboard = () => {
  const { dashboard, loading, error, reload } = useDashboard();
  const { removeDevice } = useDevices();

  const handleRemoveDevice = async (deviceId) => {
    try {
      await removeDevice(deviceId);
      // 重新載入儀表板
      await reload();
      alert('設備移除成功');
    } catch (error) {
      alert(handleApiError(error));
    }
  };

  if (loading) return <div>載入中...</div>;
  if (error) return <div>錯誤: {error}</div>;

  return (
    <div>
      <h1>用戶儀表板</h1>
      
      {/* 用戶資訊 */}
      <section>
        <h2>用戶資訊</h2>
        <p>Email: {dashboard.user.email}</p>
        <p>計劃: {dashboard.subscription.plan}</p>
      </section>

      {/* 設備列表 */}
      <section>
        <h2>設備列表</h2>
        {dashboard.devices.map(device => (
          <div key={device.device_id}>
            <span>{device.device_name}</span>
            <button onClick={() => handleRemoveDevice(device.device_id)}>
              移除
            </button>
          </div>
        ))}
      </section>

      {/* 使用量統計 */}
      <section>
        <h2>使用量統計</h2>
        <p>今日已用: {dashboard.usage.daily_used_seconds} 秒</p>
        <p>每日限制: {dashboard.usage.daily_limit_seconds} 秒</p>
      </section>
    </div>
  );
};

export default Dashboard;
```

---

## ⚡ **7. 立即行動清單**

### **步驟 1: 移除舊代碼**
- [ ] 刪除所有 `fetch` 調用到 Firebase Functions
- [ ] 移除手動 Authorization header 處理
- [ ] 移除 `BACKEND_API_URL` 環境變數

### **步驟 2: 安裝和設置**
- [ ] `npm install firebase`
- [ ] 創建 `lib/firebase.js` 文件
- [ ] 設置 Firebase Functions 區域為 `asia-east1`

### **步驟 3: 替換 API 調用**
- [ ] 替換 `get_user_dashboard` 調用
- [ ] 替換 `remove_device` 調用
- [ ] 替換其他所有 API 調用

### **步驟 4: 測試**
- [ ] 測試用戶儀表板載入
- [ ] 測試設備移除功能
- [ ] 測試錯誤處理

---

## 🎉 **預期結果**

使用 Firebase SDK 後：
- ✅ **500 錯誤消失**: 自動認證處理
- ✅ **更好的錯誤處理**: 統一的錯誤格式
- ✅ **更簡潔的代碼**: 不需要手動處理 token
- ✅ **更好的性能**: 自動重試和緩存
- ✅ **類型安全**: TypeScript 支援

**立即開始遷移，所有 500 錯誤都會解決！** 🚀
