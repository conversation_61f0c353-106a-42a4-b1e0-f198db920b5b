"""
創建訂閱記錄 - 接收完整 Stripe 支付資訊
前端發送完整的 Stripe invoice.payment_succeeded webhook 資料和計劃詳情
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from firebase_functions import https_fn
from firebase_admin import firestore

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端（環境感知）"""
    from ..utils.environment import get_db as get_env_db
    return get_env_db()

def create_subscription_handler(req: https_fn.CallableRequest) -> Dict[str, Any]:
    """
    創建訂閱記錄 - 接收完整 Stripe 支付資訊

    Args:
        req: Firebase Functions 請求對象
        req.data: {
            // === 計劃資訊 (前端提供) ===
            "plan": "FREE" | "STARTER" | "PRO" | "PREMIUM" | "MAX",
            "daily_limit_seconds": 600,  // 每日限制秒數
            "max_devices": 1,  // 最大設備數
            "price_amount": 999,  // 價格（分）
            "currency": "usd",  // 貨幣
            "billing_cycle": "monthly" | "yearly",  // 計費週期

            // === Stripe 支付資訊 (來自 webhook) ===
            "stripe_invoice_id": "in_1234567890",
            "stripe_customer_id": "cus_1234567890",
            "stripe_subscription_id": "sub_1234567890",
            "stripe_payment_intent_id": "pi_1234567890",
            "amount_total": 999,  // 總金額（分）
            "amount_paid": 999,  // 已付金額（分）
            "payment_status": "succeeded",  // 支付狀態
            "invoice_created": "2025-01-01T00:00:00Z",  // 發票創建時間
            "period_start": "2025-01-01T00:00:00Z",  // 計費週期開始
            "period_end": "2025-02-01T00:00:00Z",  // 計費週期結束

            // === 可選資訊 ===
            "source_app": "desktop" | "mobile" | "web",
            "register_region_timezone": "UTC+8",
            "usage_reset_hour": 0
        }

    Returns:
        Dict[str, Any]: 創建結果
    """
    try:
        # 驗證用戶認證
        if not req.auth:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.UNAUTHENTICATED,
                message='用戶未認證'
            )

        data = req.data or {}
        user_id = req.auth.uid

        # 驗證必要的計劃資訊
        plan = data.get('plan')
        if not plan:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message='缺少計劃資訊'
            )

        # 驗證必要的 Stripe 資訊（付費計劃需要）
        if plan != 'FREE':
            required_stripe_fields = [
                'stripe_invoice_id', 'stripe_customer_id', 'stripe_subscription_id',
                'amount_total', 'payment_status', 'invoice_created'
            ]
            for field in required_stripe_fields:
                if not data.get(field):
                    raise https_fn.HttpsError(
                        code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                        message=f'付費計劃缺少必要的 Stripe 資訊: {field}'
                    )
        
        # 驗證計劃名稱
        valid_plans = ['FREE', 'STARTER', 'PRO', 'PREMIUM', 'MAX']
        if plan not in valid_plans:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message=f'無效的訂閱計劃: {plan}。支援的計劃: {valid_plans}'
            )

        # 驗證必要的計劃資訊
        required_plan_fields = ['daily_limit_seconds', 'max_devices']
        for field in required_plan_fields:
            if data.get(field) is None:
                raise https_fn.HttpsError(
                    code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                    message=f'缺少必要的計劃資訊: {field}'
                )

        db = get_db()
        now = datetime.utcnow()

        # 檢查是否已經有訂閱記錄（使用 sub_{user_id} 格式）
        subscription_id = f"sub_{user_id}"
        subscription_ref = db.collection('subscriptions').document(subscription_id)
        subscription_doc = subscription_ref.get()

        # 創建訂閱記錄（使用前端提供的完整資訊）
        subscription_data = {
            # === 基本資訊 ===
            'user_id': user_id,
            'plan': plan,
            'status': 'active',
            'source_app': data.get('source_app', 'web'),

            # === 計劃配置（前端提供） ===
            'daily_limit_seconds': data.get('daily_limit_seconds'),
            'max_devices': data.get('max_devices'),
            'current_day_used_seconds': 0,

            # === 時區和重置設定 ===
            'register_region_timezone': data.get('register_region_timezone', 'UTC+8'),
            'usage_reset_hour': data.get('usage_reset_hour', 0),
            'last_daily_reset_date': now.strftime('%Y-%m-%d'),
            'last_daily_reset_at': now,

            # === 設備管理 ===
            'active_device_ids': [],
            'last_device_limit_check': now,

            # === 時間戳 ===
            'created_at': now,
            'updated_at': now
        }

        # === Stripe 資訊（付費計劃） ===
        if plan != 'FREE':
            subscription_data.update({
                'stripe_customer_id': data.get('stripe_customer_id'),
                'stripe_subscription_id': data.get('stripe_subscription_id'),
                'stripe_invoice_id': data.get('stripe_invoice_id'),
                'stripe_payment_intent_id': data.get('stripe_payment_intent_id'),

                # === 支付資訊 ===
                'amount_total': data.get('amount_total'),
                'amount_paid': data.get('amount_paid'),
                'currency': data.get('currency', 'usd'),
                'payment_status': data.get('payment_status'),

                # === 計費資訊 ===
                'price_amount': data.get('price_amount'),
                'billing_cycle': data.get('billing_cycle', 'monthly'),
                'invoice_created': data.get('invoice_created'),
                'period_start': data.get('period_start'),
                'period_end': data.get('period_end'),

                # === 自動續費 ===
                'auto_renew': data.get('auto_renew', True),
                'next_billing_date': data.get('period_end')
            })
        else:
            # FREE 計劃的預設值
            subscription_data.update({
                'stripe_customer_id': None,
                'stripe_subscription_id': None,
                'auto_renew': False,
                'next_billing_date': None
            })

        # 保存訂閱記錄
        subscription_ref.set(subscription_data)

        # 更新用戶表的訂閱關聯
        user_ref = db.collection('users').document(user_id)
        user_ref.update({
            'current_subscription_id': subscription_id,
            'updated_at': now
        })

        # === 創建購買記錄（付費計劃） ===
        if plan != 'FREE' and data.get('stripe_invoice_id'):
            purchase_data = {
                # === Stripe 相關資訊 ===
                'stripe_invoice_id': data.get('stripe_invoice_id'),
                'stripe_customer_id': data.get('stripe_customer_id'),
                'stripe_subscription_id': data.get('stripe_subscription_id'),
                'stripe_payment_intent_id': data.get('stripe_payment_intent_id'),

                # === 金額資訊 ===
                'amount_total': data.get('amount_total'),
                'amount_subtotal': data.get('amount_total'),  # 假設無稅
                'amount_tax': [],
                'amount_paid': data.get('amount_paid'),
                'amount_due': 0,
                'amount_remaining': 0,

                # === 貨幣和地區 ===
                'currency': data.get('currency', 'usd'),
                'account_country': 'US',

                # === 客戶資訊 ===
                'customer_email': data.get('customer_email', ''),
                'customer_name': data.get('customer_name', ''),

                # === 發票狀態 ===
                'invoice_status': 'paid',
                'collection_method': 'charge_automatically',
                'billing_reason': 'subscription_cycle',

                # === 時間資訊 ===
                'invoice_created': data.get('invoice_created'),
                'period_start': data.get('period_start'),
                'period_end': data.get('period_end'),
                'due_date': data.get('invoice_created'),

                # === 支付狀態 ===
                'payment_status': data.get('payment_status', 'succeeded'),
                'payment_attempted': True,
                'attempt_count': 1,

                # === 訂閱相關 ===
                'subscription_plan': plan,
                'subscription_period': data.get('billing_cycle', 'monthly'),
                'unit_amount': data.get('price_amount'),

                # === 元數據 ===
                'stripe_metadata': {},
                'webhook_event_type': 'invoice.payment_succeeded',

                # === 內部狀態 ===
                'processed': True,
                'processing_errors': [],

                # === 應用相關 ===
                'user_id': user_id,
                'source_app': data.get('source_app', 'web'),

                # === 時間戳 ===
                'created_at': now,
                'updated_at': now
            }

            # 保存購買記錄
            purchase_ref = db.collection('purchases').document()
            purchase_ref.set(purchase_data)

            logger.info(f"購買記錄創建成功: {user_id} -> {plan} (Invoice: {data.get('stripe_invoice_id')})")

        logger.info(f"訂閱創建成功: {user_id} -> {plan}")

        return {
            'success': True,
            'data': {
                'subscription': {
                    'subscription_id': subscription_id,
                    'user_id': user_id,
                    'plan': plan,
                    'status': 'active',
                    'daily_limit_seconds': subscription_data['daily_limit_seconds'],
                    'max_devices': subscription_data['max_devices'],
                    'current_day_used_seconds': 0,
                    'stripe_customer_id': subscription_data.get('stripe_customer_id'),
                    'stripe_subscription_id': subscription_data.get('stripe_subscription_id'),
                    'billing_cycle': subscription_data.get('billing_cycle'),
                    'auto_renew': subscription_data.get('auto_renew', False),
                    'created_at': subscription_data['created_at'],
                    'updated_at': subscription_data['updated_at']
                }
            },
            'message': f'{plan} 訂閱創建成功'
        }



    except https_fn.HttpsError:
        raise
    except Exception as e:
        logger.error(f"❌ create_subscription 錯誤: {str(e)}")
        raise https_fn.HttpsError(
            code=https_fn.FunctionsErrorCode.INTERNAL,
            message='內部錯誤'
        )
