#!/bin/bash
# Deploy to Production Environment
# Usage: ./deploy-prod.sh

set -e

echo "🚀 Deploying to Production Environment..."

# Configuration
ENVFILE=".env.prod"
RUNTIME="python311"
REGION="asia-east1"

# Check if env file exists
if [ ! -f "$ENVFILE" ]; then
    echo "❌ Error: $ENVFILE not found!"
    echo "Please create $ENVFILE with your production configuration."
    exit 1
fi

# Extract environment variables (skip comments and empty lines)
VARS=$(grep -v '^#' $ENVFILE | grep -v '^$' | xargs | tr ' ' ',')

if [ -z "$VARS" ]; then
    echo "❌ Error: No environment variables found in $ENVFILE"
    exit 1
fi

echo "📋 Environment variables: $VARS"

# Confirmation prompt for production
echo "⚠️  You are about to deploy to PRODUCTION!"
read -p "Are you sure? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "❌ Deployment cancelled."
    exit 1
fi

# Deploy Firebase Functions
echo "🔧 Deploying Firebase Functions..."
gcloud functions deploy backend-api \
  --runtime $RUNTIME \
  --trigger-http \
  --allow-unauthenticated \
  --region $REGION \
  --set-env-vars "$VARS" \
  --memory 512MB \
  --timeout 60s

echo "✅ Production deployment completed!"
echo "🌐 Function URL: https://$REGION-speakoneai-prod.cloudfunctions.net/backend-api"
