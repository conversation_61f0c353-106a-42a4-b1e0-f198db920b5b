"""
移除設備功能
"""

import logging
from datetime import datetime
from typing import Dict, Any
from firebase_functions import https_fn
from firebase_admin import firestore

from ..utils.validation import validate_auth, validate_device_id
from ..utils.constants import DeviceStatus
from ..utils.environment import get_db

logger = logging.getLogger(__name__)

def remove_device_handler(req: https_fn.CallableRequest) -> Dict[str, Any]:
    """
    移除設備處理器
    
    Args:
        req: Firebase Functions 請求對象
        req.data: {
            "device_id": "unique-device-id",
            "reason": "user_request" // 可選，移除原因
        }
        
    Returns:
        Dict[str, Any]: 移除結果
        {
            "success": true,
            "data": {
                "device_id": "unique-device-id",
                "removed_at": "2025-01-27T10:00:00Z",
                "remaining_devices": [...],
                "devices_info": {
                    "current_count": 1,
                    "max_allowed": 2,
                    "can_add_more": true
                }
            },
            "message": "設備移除成功"
        }
    """
    try:
        # 驗證用戶認證
        if not req.auth:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.UNAUTHENTICATED,
                message='用戶未認證'
            )

        user_id = req.auth.uid
        data = req.data or {}
        
        # 驗證必要參數
        device_id = data.get('device_id')
        if not device_id or not validate_device_id(device_id):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message='無效的設備ID'
            )
        
        reason = data.get('reason', 'user_request')
        
        logger.info(f"處理設備移除: user={user_id}, device={device_id}, reason={reason}")
        
        db = get_db()
        
        # 1. 檢查設備是否存在且屬於該用戶
        device_doc = db.collection('devices').document(device_id).get()
        if not device_doc.exists:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.NOT_FOUND,
                message='設備不存在'
            )
        
        device_data = device_doc.to_dict()
        if device_data.get('user_id') != user_id:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.PERMISSION_DENIED,
                message='無權限操作此設備'
            )
        
        # 2. 檢查設備當前狀態
        current_status = device_data.get('status', 'active')
        if current_status == DeviceStatus.REMOVED.value:
            return {
                'success': False,
                'message': '設備已經被移除',
                'data': {
                    'device_id': device_id,
                    'current_status': current_status,
                    'removed_at': device_data.get('removed_at')
                }
            }
        
        # 3. 獲取用戶訂閱資訊
        subscription_info = get_user_subscription_info(db, user_id)
        if not subscription_info:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.FAILED_PRECONDITION,
                message='用戶沒有有效訂閱'
            )
        
        # 4. 更新設備狀態為已移除
        now = datetime.utcnow()
        device_ref = db.collection('devices').document(device_id)
        device_ref.update({
            'status': DeviceStatus.REMOVED.value,
            'is_active': False,
            'is_authorized': False,
            'removed_at': now,
            'updated_at': now
        })
        
        # 5. 同步設備列表到用戶和訂閱表
        remaining_devices = sync_device_lists_after_removal(db, user_id, subscription_info['subscription_id'], device_id)
        
        # 6. 設備操作記錄已移除 (簡化架構)
        
        # 7. 獲取更新後的設備統計
        max_devices = subscription_info.get('max_devices', 1)
        current_count = len(remaining_devices)
        can_add_more = current_count < max_devices if max_devices != -1 else True
        
        return {
            'success': True,
            'data': {
                'device_id': device_id,
                'removed_at': now,
                'remaining_devices': [format_device_info(d) for d in remaining_devices],
                'devices_info': {
                    'current_count': current_count,
                    'max_allowed': max_devices,
                    'can_add_more': can_add_more
                }
            },
            'message': '設備移除成功'
        }
        
    except https_fn.HttpsError:
        raise
    except Exception as e:
        logger.error(f"設備移除失敗: {user_id}, 錯誤: {str(e)}")
        raise https_fn.HttpsError(
            code=https_fn.FunctionsErrorCode.INTERNAL,
            message=f'設備移除失敗: {str(e)}'
        )

def get_user_subscription_info(db, user_id: str) -> Dict[str, Any]:
    """獲取用戶訂閱資訊"""
    try:
        # 從用戶表獲取訂閱ID
        user_doc = db.collection('users').document(user_id).get()
        if not user_doc.exists:
            return None
        
        user_data = user_doc.to_dict()
        subscription_id = user_data.get('current_subscription_id')
        
        if not subscription_id:
            return None
        
        # 從訂閱表獲取詳細資訊
        subscription_doc = db.collection('subscriptions').document(subscription_id).get()
        if not subscription_doc.exists:
            return None
        
        subscription_data = subscription_doc.to_dict()
        subscription_data['subscription_id'] = subscription_id
        
        return subscription_data
        
    except Exception as e:
        logger.error(f"獲取用戶訂閱資訊失敗: {user_id}, 錯誤: {str(e)}")
        return None

def sync_device_lists_after_removal(db, user_id: str, subscription_id: str, removed_device_id: str) -> list:
    """移除設備後同步設備列表"""
    try:
        # 獲取剩餘的活躍設備
        remaining_devices = []
        devices_query = db.collection('devices').where('user_id', '==', user_id).where('is_active', '==', True)
        
        for doc in devices_query.stream():
            device_data = doc.to_dict()
            if device_data['device_id'] != removed_device_id:
                device_data['doc_ref'] = doc.reference
                device_data['doc_id'] = doc.id
                remaining_devices.append(device_data)
        
        # 更新設備ID列表
        device_ids = [device['device_id'] for device in remaining_devices]
        now = datetime.utcnow()
        
        # 更新用戶表
        user_ref = db.collection('users').document(user_id)
        user_ref.update({
            'device_ids': device_ids,
            'last_device_check': now,
            'updated_at': now
        })
        
        # 更新訂閱表
        subscription_ref = db.collection('subscriptions').document(subscription_id)
        subscription_ref.update({
            'active_device_ids': device_ids,
            'last_device_limit_check': now,
            'updated_at': now
        })
        
        return remaining_devices
        
    except Exception as e:
        logger.error(f"同步設備列表失敗: {user_id}, 錯誤: {str(e)}")
        return []

# device_action_logs 功能已移除

def format_device_info(device: Dict[str, Any]) -> Dict[str, Any]:
    """格式化設備資訊"""
    return {
        'device_id': device.get('device_id'),
        'device_name': device.get('device_name'),
        'platform': device.get('platform'),
        'status': device.get('status'),
        'is_active': device.get('is_active'),
        'is_authorized': device.get('is_authorized'),
        'last_active_at': device.get('last_active_at'),
        'created_at': device.get('created_at'),
        'app_version': device.get('app_version')
    }
