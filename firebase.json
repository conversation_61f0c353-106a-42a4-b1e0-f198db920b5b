{"functions": {"source": "backend-api", "runtime": "python311", "region": "asia-east1", "ignore": ["__pycache__", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local", ".env", "venv", ".venv", "node_modules"]}, "firestore": {"database": "(default)", "location": "asia-east2", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "emulators": {"functions": {"port": 9999}, "firestore": {"port": 9998}, "ui": {"enabled": true, "port": 9997}, "singleProjectMode": true}}