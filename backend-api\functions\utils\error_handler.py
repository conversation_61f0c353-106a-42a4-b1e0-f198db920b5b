"""
環境感知的錯誤處理工具
根據環境自動決定錯誤信息的詳細程度
"""

import logging
import traceback
from firebase_functions import https_fn
from typing import Any, Optional, Dict
from .environment import is_development, is_production
from .constants import ERROR_MESSAGES

logger = logging.getLogger(__name__)

class EnvironmentAwareErrorHandler:
    """環境感知的錯誤處理器"""
    
    @staticmethod
    def handle_error(
        error: Exception,
        error_code: str = "INTERNAL_ERROR",
        user_message: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> https_fn.HttpsError:
        """
        處理錯誤並根據環境返回適當的錯誤信息
        
        Args:
            error: 原始錯誤對象
            error_code: 錯誤代碼（用於查找用戶友好的錯誤信息）
            user_message: 自定義用戶錯誤信息
            context: 額外的上下文信息
            
        Returns:
            https_fn.HttpsError: Firebase Functions 錯誤對象
        """
        # 如果已經是 Firebase Functions 錯誤，直接重新拋出
        if isinstance(error, https_fn.HttpsError):
            return error
        
        # 記錄詳細錯誤信息到日誌
        error_details = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "error_code": error_code,
            "context": context or {}
        }
        
        if is_development():
            # 開發環境：記錄完整的堆疊追蹤
            error_details["traceback"] = traceback.format_exc()
            logger.error(f"❌ 詳細錯誤信息: {error_details}")
        else:
            # 生產環境：只記錄基本錯誤信息
            logger.error(f"❌ 錯誤: {error_code} - {str(error)}")
        
        # 決定返回給客戶端的錯誤信息
        if is_development():
            # 開發環境：返回詳細錯誤信息
            detailed_message = user_message or ERROR_MESSAGES.get(error_code, "內部錯誤")
            
            # 在開發環境中包含更多調試信息
            debug_info = {
                "original_error": str(error),
                "error_type": type(error).__name__,
                "context": context or {}
            }
            
            return https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INTERNAL,
                message=f"{detailed_message} [DEV] {str(error)}",
                details=debug_info
            )
        else:
            # 生產環境：只返回用戶友好的錯誤信息
            safe_message = user_message or ERROR_MESSAGES.get(error_code, "內部錯誤")
            
            return https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INTERNAL,
                message=safe_message
            )
    
    @staticmethod
    def handle_validation_error(
        error_code: str,
        user_message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> https_fn.HttpsError:
        """
        處理驗證錯誤
        
        Args:
            error_code: 錯誤代碼
            user_message: 自定義錯誤信息
            details: 錯誤詳情
            
        Returns:
            https_fn.HttpsError: Firebase Functions 錯誤對象
        """
        message = user_message or ERROR_MESSAGES.get(error_code, "驗證失敗")
        
        # 根據錯誤代碼決定 Firebase Functions 錯誤類型
        if error_code == "UNAUTHENTICATED":
            code = https_fn.FunctionsErrorCode.UNAUTHENTICATED
        elif error_code in ["USER_NOT_FOUND", "DEVICE_NOT_FOUND"]:
            code = https_fn.FunctionsErrorCode.NOT_FOUND
        elif error_code == "PERMISSION_DENIED":
            code = https_fn.FunctionsErrorCode.PERMISSION_DENIED
        elif error_code in ["USAGE_LIMIT_EXCEEDED", "DEVICE_LIMIT_EXCEEDED"]:
            code = https_fn.FunctionsErrorCode.RESOURCE_EXHAUSTED
        elif error_code in ["INVALID_DEVICE", "INVALID_SUBMISSION"]:
            code = https_fn.FunctionsErrorCode.INVALID_ARGUMENT
        else:
            code = https_fn.FunctionsErrorCode.FAILED_PRECONDITION
        
        if is_development():
            # 開發環境：包含詳細信息和 [DEV] 標記
            return https_fn.HttpsError(
                code=code,
                message=f"{message} [DEV]",
                details=details
            )
        else:
            # 生產環境：只返回基本信息
            return https_fn.HttpsError(
                code=code,
                message=message
            )

# 便捷函數
def handle_error(
    error: Exception,
    error_code: str = "INTERNAL_ERROR",
    user_message: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None
) -> https_fn.HttpsError:
    """便捷的錯誤處理函數"""
    return EnvironmentAwareErrorHandler.handle_error(
        error=error,
        error_code=error_code,
        user_message=user_message,
        context=context
    )

def handle_validation_error(
    error_code: str,
    user_message: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> https_fn.HttpsError:
    """便捷的驗證錯誤處理函數"""
    return EnvironmentAwareErrorHandler.handle_validation_error(
        error_code=error_code,
        user_message=user_message,
        details=details
    )

def log_function_start(function_name: str, user_id: Optional[str] = None, **kwargs):
    """記錄函數開始執行"""
    if is_development():
        logger.info(f"🚀 [{function_name}] 開始執行 - 用戶: {user_id}, 參數: {kwargs}")
    else:
        logger.info(f"🚀 [{function_name}] 開始執行 - 用戶: {user_id}")

def log_function_success(function_name: str, user_id: Optional[str] = None, **kwargs):
    """記錄函數成功執行"""
    if is_development():
        logger.info(f"✅ [{function_name}] 執行成功 - 用戶: {user_id}, 結果: {kwargs}")
    else:
        logger.info(f"✅ [{function_name}] 執行成功 - 用戶: {user_id}")
