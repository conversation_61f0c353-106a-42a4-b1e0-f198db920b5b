"""
時區和日期管理模組 - 標準化時區處理
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, Tuple, List
import re

logger = logging.getLogger(__name__)

class TimezoneManager:
    """時區管理器"""
    
    # 支援的時區映射
    TIMEZONE_MAPPING = {
        # 亞洲
        'Hong Kong': 'UTC+8',
        'Taiwan': 'UTC+8',
        'China': 'UTC+8',
        'Japan': 'UTC+9',
        'Korea': 'UTC+9',
        'Singapore': 'UTC+8',
        'Thailand': 'UTC+7',
        'Vietnam': 'UTC+7',
        'India': 'UTC+5:30',
        
        # 歐洲
        'United Kingdom': 'UTC+0',
        'Germany': 'UTC+1',
        'France': 'UTC+1',
        'Italy': 'UTC+1',
        'Spain': 'UTC+1',
        'Netherlands': 'UTC+1',
        'Sweden': 'UTC+1',
        'Norway': 'UTC+1',
        'Russia': 'UTC+3',
        
        # 美洲
        'United States': 'UTC-5',  # 東部時間
        'Canada': 'UTC-5',
        'Mexico': 'UTC-6',
        'Brazil': 'UTC-3',
        'Argentina': 'UTC-3',
        'Chile': 'UTC-3',
        
        # 大洋洲
        'Australia': 'UTC+10',  # 東部時間
        'New Zealand': 'UTC+12',
        
        # 非洲
        'South Africa': 'UTC+2',
        'Egypt': 'UTC+2',
        'Nigeria': 'UTC+1',
    }
    
    @classmethod
    def parse_timezone(cls, timezone_str: str) -> timezone:
        """
        解析時區字串為 timezone 對象
        
        Args:
            timezone_str: 時區字串 (如 'UTC+8', 'UTC-5', 'UTC+5:30')
            
        Returns:
            timezone: Python timezone 對象
        """
        try:
            if not timezone_str or timezone_str == 'UTC':
                return timezone.utc
            
            # 解析 UTC±H 或 UTC±H:M 格式
            pattern = r'UTC([+-])(\d{1,2})(?::(\d{2}))?'
            match = re.match(pattern, timezone_str)
            
            if not match:
                logger.warning(f"無法解析時區: {timezone_str}, 使用 UTC+8")
                return timezone(timedelta(hours=8))
            
            sign = 1 if match.group(1) == '+' else -1
            hours = int(match.group(2))
            minutes = int(match.group(3)) if match.group(3) else 0
            
            total_minutes = sign * (hours * 60 + minutes)
            return timezone(timedelta(minutes=total_minutes))
            
        except Exception as e:
            logger.error(f"解析時區失敗: {timezone_str}, 錯誤: {str(e)}, 使用 UTC+8")
            return timezone(timedelta(hours=8))
    
    @classmethod
    def get_region_timezone(cls, region: str) -> str:
        """
        根據地區獲取時區
        
        Args:
            region: 地區名稱
            
        Returns:
            str: 時區字串
        """
        return cls.TIMEZONE_MAPPING.get(region, 'UTC+8')
    
    @classmethod
    def get_local_date(cls, timezone_str: str, utc_datetime: Optional[datetime] = None) -> str:
        """
        獲取指定時區的本地日期
        
        Args:
            timezone_str: 時區字串
            utc_datetime: UTC 時間（預設為當前時間）
            
        Returns:
            str: 本地日期 (YYYY-MM-DD)
        """
        try:
            if utc_datetime is None:
                utc_datetime = datetime.now(timezone.utc)
            elif utc_datetime.tzinfo is None:
                utc_datetime = utc_datetime.replace(tzinfo=timezone.utc)
            
            local_tz = cls.parse_timezone(timezone_str)
            local_datetime = utc_datetime.astimezone(local_tz)
            
            return local_datetime.strftime('%Y-%m-%d')
            
        except Exception as e:
            logger.error(f"獲取本地日期失敗: {timezone_str}, 錯誤: {str(e)}")
            # 回退到 UTC 日期
            return utc_datetime.strftime('%Y-%m-%d')
    
    @classmethod
    def get_reset_time(cls, timezone_str: str, reset_hour: int = 0, target_date: Optional[str] = None) -> datetime:
        """
        獲取指定日期的重置時間（UTC）
        
        Args:
            timezone_str: 時區字串
            reset_hour: 重置小時 (0-23)
            target_date: 目標日期 (YYYY-MM-DD)，預設為今天
            
        Returns:
            datetime: 重置時間（UTC）
        """
        try:
            local_tz = cls.parse_timezone(timezone_str)
            
            if target_date is None:
                target_date = cls.get_local_date(timezone_str)
            
            # 解析目標日期
            year, month, day = map(int, target_date.split('-'))
            
            # 創建本地重置時間
            local_reset_time = datetime(year, month, day, reset_hour, 0, 0, tzinfo=local_tz)
            
            # 轉換為 UTC
            utc_reset_time = local_reset_time.astimezone(timezone.utc)
            
            return utc_reset_time
            
        except Exception as e:
            logger.error(f"獲取重置時間失敗: {timezone_str}, 錯誤: {str(e)}")
            # 回退到當前 UTC 時間
            return datetime.now(timezone.utc)
    
    @classmethod
    def should_reset_quota(cls, last_reset_date: Optional[str], timezone_str: str, reset_hour: int = 0) -> Tuple[bool, Dict[str, Any]]:
        """
        檢查是否應該重置配額
        
        Args:
            last_reset_date: 上次重置日期 (YYYY-MM-DD)
            timezone_str: 時區字串
            reset_hour: 重置小時
            
        Returns:
            Tuple[bool, Dict[str, Any]]: (是否需要重置, 重置資訊)
        """
        try:
            current_local_date = cls.get_local_date(timezone_str)
            
            # 如果沒有重置記錄或日期不同，需要重置
            if not last_reset_date or last_reset_date != current_local_date:
                reset_time_utc = cls.get_reset_time(timezone_str, reset_hour, current_local_date)
                
                return True, {
                    'reset_date': current_local_date,
                    'reset_time_utc': reset_time_utc,
                    'timezone': timezone_str,
                    'reset_hour': reset_hour
                }
            
            return False, {}
            
        except Exception as e:
            logger.error(f"檢查配額重置失敗: {str(e)}")
            return False, {}
    
    @classmethod
    def format_datetime_with_timezone(cls, dt: datetime, timezone_str: str) -> Dict[str, str]:
        """
        格式化日期時間，同時提供本地時間和 UTC 時間
        
        Args:
            dt: 日期時間
            timezone_str: 時區字串
            
        Returns:
            Dict[str, str]: 格式化的日期時間
        """
        try:
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            
            local_tz = cls.parse_timezone(timezone_str)
            local_dt = dt.astimezone(local_tz)
            utc_dt = dt.astimezone(timezone.utc)
            
            return {
                'local_date': local_dt.strftime('%Y-%m-%d'),
                'local_datetime': local_dt.strftime('%Y-%m-%d %H:%M:%S'),
                'local_timezone': timezone_str,
                'utc_datetime': utc_dt.strftime('%Y-%m-%dT%H:%M:%SZ'),
                'utc_timestamp': utc_dt
            }
            
        except Exception as e:
            logger.error(f"格式化日期時間失敗: {str(e)}")
            return {
                'local_date': dt.strftime('%Y-%m-%d'),
                'local_datetime': dt.strftime('%Y-%m-%d %H:%M:%S'),
                'local_timezone': 'UTC',
                'utc_datetime': dt.strftime('%Y-%m-%dT%H:%M:%SZ'),
                'utc_timestamp': dt
            }
    
    @classmethod
    def get_timezones_for_reset(cls, current_utc_hour: int) -> List[str]:
        """
        獲取在指定 UTC 小時需要重置的時區列表
        
        Args:
            current_utc_hour: 當前 UTC 小時 (0-23)
            
        Returns:
            List[str]: 需要重置的時區列表
        """
        reset_timezones = []
        
        for region, timezone_str in cls.TIMEZONE_MAPPING.items():
            try:
                local_tz = cls.parse_timezone(timezone_str)
                
                # 計算該時區的當前小時
                utc_now = datetime.now(timezone.utc).replace(hour=current_utc_hour, minute=0, second=0, microsecond=0)
                local_now = utc_now.astimezone(local_tz)
                
                # 如果本地時間是 0 點，則需要重置
                if local_now.hour == 0:
                    if timezone_str not in reset_timezones:
                        reset_timezones.append(timezone_str)
                        
            except Exception as e:
                logger.warning(f"檢查時區重置失敗: {region}/{timezone_str}, 錯誤: {str(e)}")
        
        return reset_timezones

# 全域時區管理器實例
timezone_manager = TimezoneManager()
