"""
SpeechPilot V2.3 業務流程測試
基於新的統一 API 架構
"""

import unittest
import sys
import os
import time
from datetime import datetime
from unittest.mock import Mock, patch

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend-api'))

class TestBusinessWorkflowsV23(unittest.TestCase):
    """V2.3 業務流程測試類"""
    
    def setUp(self):
        """測試設置"""
        self.timestamp = str(int(time.time()))
        self.test_user_id = f"test_user_{self.timestamp}"
        self.test_email = f"test_{self.timestamp}@example.com"
        self.test_device_id = f"device_{self.timestamp}"
        
    def test_scenario_1_new_user_onboarding_v23(self):
        """場景 1: 新用戶首次登入 (V2.3 統一 API)"""
        print("\n🆕 場景 1: 新用戶首次登入 (V2.3)")
        print("=" * 50)
        
        try:
            # 步驟 1: 創建新用戶
            print("步驟 1: 創建新用戶...")
            user_data = {
                "email": self.test_email,
                "display_name": "Test User V2.3",
                "platform": "windows",
                "app_version": "2.3.0"
            }
            
            # 模擬 create_or_update_user 成功
            self.assertIsNotNone(user_data)
            print("✅ 用戶創建成功")
            
            # 步驟 2: 統一設備驗證/註冊
            print("步驟 2: 統一設備驗證/註冊...")
            device_data = {
                "device_id": self.test_device_id,
                "device_name": "Test Windows PC V2.3",
                "platform": "windows",
                "app_version": "2.3.0",
                "device_info": {
                    "os_version": "10.0.26100",
                    "fingerprint": f"test_fingerprint_{self.timestamp}"
                }
            }
            
            # 模擬 validate_or_register_device 返回 code: 2 (已註冊)
            device_result = {
                "success": True,
                "code": 2,
                "message": "設備註冊成功",
                "data": {
                    "device": device_data,
                    "subscription": {
                        "plan": "FREE",
                        "daily_limit_seconds": 600,
                        "max_devices": 1
                    },
                    "devices_info": {
                        "current_count": 1,
                        "max_allowed": 1,
                        "can_add_more": False
                    }
                }
            }
            
            self.assertEqual(device_result["code"], 2)
            self.assertTrue(device_result["success"])
            print("✅ 設備註冊成功 (code: 2)")
            
            # 步驟 3: 獲取用戶儀表板
            print("步驟 3: 獲取用戶儀表板...")
            dashboard_result = {
                "success": True,
                "data": {
                    "user": {
                        "user_id": self.test_user_id,
                        "email": self.test_email,
                        "onboarding_completed": True,
                        "first_device_registered": True
                    },
                    "subscription": {
                        "plan": "FREE",
                        "status": "active",
                        "daily_limit_seconds": 600,
                        "current_day_used_seconds": 0,
                        "max_devices": 1
                    },
                    "devices": {
                        "active_devices": [device_data],
                        "device_count": 1,
                        "active_session_count": 0
                    },
                    "usage": {
                        "daily": {
                            "limit_seconds": 600,
                            "used_seconds": 0,
                            "remaining_seconds": 600,
                            "usage_percentage": 0,
                            "can_use": True
                        }
                    }
                }
            }
            
            self.assertTrue(dashboard_result["success"])
            self.assertTrue(dashboard_result["data"]["user"]["onboarding_completed"])
            print("✅ 用戶儀表板獲取成功")
            
            # 步驟 4: 使用前配額檢查
            print("步驟 4: 使用前配額檢查...")
            usage_check_result = {
                "success": True,
                "data": {
                    "can_use": True,
                    "remaining_seconds": 600,
                    "daily_limit": 600,
                    "estimated_sessions_remaining": 2
                }
            }
            
            self.assertTrue(usage_check_result["data"]["can_use"])
            self.assertEqual(usage_check_result["data"]["remaining_seconds"], 600)
            print("✅ 配額檢查通過")
            
            print("🎉 場景 1 完成: 新用戶 Onboarding 成功")
            
        except Exception as e:
            print(f"❌ 場景 1 失敗: {str(e)}")
            self.fail(f"新用戶 Onboarding 失敗: {str(e)}")
    
    def test_scenario_2_existing_user_login_v23(self):
        """場景 2: 現有用戶登入 (V2.3 統一 API)"""
        print("\n👤 場景 2: 現有用戶登入 (V2.3)")
        print("=" * 50)
        
        try:
            # 步驟 1: 用戶重新登入
            print("步驟 1: 用戶重新登入...")
            user_update_data = {
                "email": self.test_email,
                "display_name": "Updated Test User",
                "app_version": "2.3.1"  # 更新版本
            }
            
            self.assertIsNotNone(user_update_data)
            print("✅ 用戶資訊更新成功")
            
            # 步驟 2: 統一設備驗證
            print("步驟 2: 統一設備驗證...")
            device_validation_result = {
                "success": True,
                "code": 1,  # 設備已驗證
                "message": "設備驗證成功",
                "data": {
                    "device": {
                        "device_id": self.test_device_id,
                        "status": "active",
                        "is_active": True,
                        "is_authorized": True,
                        "last_active_at": datetime.utcnow().isoformat()
                    },
                    "subscription": {
                        "plan": "FREE",
                        "status": "active"
                    }
                }
            }
            
            self.assertEqual(device_validation_result["code"], 1)
            self.assertTrue(device_validation_result["success"])
            print("✅ 設備驗證成功 (code: 1)")
            
            # 步驟 3: 獲取最新儀表板狀態
            print("步驟 3: 獲取最新儀表板狀態...")
            dashboard_result = {
                "success": True,
                "data": {
                    "user": {
                        "user_id": self.test_user_id,
                        "email": self.test_email,
                        "display_name": "Updated Test User"
                    },
                    "subscription": {
                        "plan": "FREE",
                        "status": "active",
                        "daily_limit_seconds": 600,
                        "current_day_used_seconds": 0
                    },
                    "devices": {
                        "device_count": 1,
                        "active_session_count": 0
                    },
                    "usage": {
                        "daily": {
                            "can_use": True,
                            "remaining_seconds": 600
                        }
                    }
                }
            }
            
            self.assertTrue(dashboard_result["success"])
            print("✅ 儀表板狀態獲取成功")
            
            print("🎉 場景 2 完成: 現有用戶登入成功")
            
        except Exception as e:
            print(f"❌ 場景 2 失敗: {str(e)}")
            self.fail(f"現有用戶登入失敗: {str(e)}")
    
    def test_scenario_3_device_limit_exceeded_v23(self):
        """場景 3: 設備數量超限 (V2.3 統一處理)"""
        print("\n⚠️ 場景 3: 設備數量超限 (V2.3)")
        print("=" * 50)
        
        try:
            # 步驟 1: 嘗試註冊第二台設備
            print("步驟 1: 嘗試註冊第二台設備...")
            second_device_data = {
                "device_id": f"device_2_{self.timestamp}",
                "device_name": "Second Device",
                "platform": "macos",
                "app_version": "2.3.0"
            }
            
            # 模擬 validate_or_register_device 返回 code: -1 (設備限制)
            device_limit_result = {
                "success": False,
                "code": -1,
                "message": "已達到設備數量限制 (1/1)",
                "reason": "DEVICE_LIMIT_EXCEEDED",
                "action_required": "REMOVE_DEVICE",
                "data": {
                    "current_devices": [
                        {
                            "device_id": self.test_device_id,
                            "device_name": "Test Windows PC V2.3",
                            "platform": "windows",
                            "last_active_at": datetime.utcnow().isoformat()
                        }
                    ],
                    "current_count": 1,
                    "max_allowed": 1,
                    "current_plan": "FREE"
                }
            }
            
            self.assertEqual(device_limit_result["code"], -1)
            self.assertFalse(device_limit_result["success"])
            self.assertEqual(device_limit_result["reason"], "DEVICE_LIMIT_EXCEEDED")
            print("✅ 設備限制檢查正確 (code: -1)")
            
            # 步驟 2: 驗證設備管理選項
            print("步驟 2: 驗證設備管理選項...")
            current_devices = device_limit_result["data"]["current_devices"]
            self.assertEqual(len(current_devices), 1)
            self.assertEqual(current_devices[0]["device_id"], self.test_device_id)
            print("✅ 設備管理選項提供正確")
            
            # 步驟 3: 模擬升級計劃選項
            print("步驟 3: 模擬升級計劃選項...")
            upgrade_options = {
                "current_plan": "FREE",
                "suggested_plans": [
                    {
                        "plan": "PRO",
                        "max_devices": 2,
                        "daily_limit_seconds": 10800,
                        "price": "$19.99/月"
                    }
                ]
            }
            
            self.assertIn("suggested_plans", upgrade_options)
            self.assertEqual(upgrade_options["suggested_plans"][0]["max_devices"], 2)
            print("✅ 升級選項提供正確")
            
            print("🎉 場景 3 完成: 設備限制處理正確")
            
        except Exception as e:
            print(f"❌ 場景 3 失敗: {str(e)}")
            self.fail(f"設備限制處理失敗: {str(e)}")
    
    def test_scenario_4_usage_quota_management_v23(self):
        """場景 4: 使用量配額管理 (V2.3)"""
        print("\n🎙️ 場景 4: 使用量配額管理 (V2.3)")
        print("=" * 50)
        
        try:
            # 步驟 1: 使用前配額檢查
            print("步驟 1: 使用前配額檢查...")
            usage_check_request = {
                "device_id": self.test_device_id,
                "estimated_duration_seconds": 300
            }
            
            usage_check_result = {
                "success": True,
                "data": {
                    "can_use": True,
                    "remaining_seconds": 600,
                    "daily_limit": 600,
                    "estimated_sessions_remaining": 2,
                    "reset_time": "2025-01-28T00:00:00Z",
                    "subscription": {
                        "plan": "FREE",
                        "status": "active"
                    }
                }
            }
            
            self.assertTrue(usage_check_result["data"]["can_use"])
            self.assertEqual(usage_check_result["data"]["remaining_seconds"], 600)
            print("✅ 配額檢查通過")
            
            # 步驟 2: 提交使用記錄
            print("步驟 2: 提交使用記錄...")
            usage_submit_request = {
                "duration_seconds": 300,
                "feature_type": "ai-speech-to-text",
                "device_id": self.test_device_id,
                "platform": "windows",
                "session_metadata": {
                    "audio_quality": "high",
                    "language": "zh-TW"
                }
            }
            
            usage_submit_result = {
                "success": True,
                "data": {
                    "usage_log_id": f"log_{self.timestamp}",
                    "duration_recorded": 300,
                    "remaining_quota": {
                        "daily_remaining_seconds": 300,  # 600 - 300
                        "daily_limit_seconds": 600,
                        "usage_percentage": 50.0,
                        "can_use": True,
                        "reset_time": "2025-01-28T00:00:00Z"
                    },
                    "session_info": {
                        "session_id": f"session_{self.timestamp}",
                        "started_at": datetime.utcnow().isoformat(),
                        "ended_at": datetime.utcnow().isoformat()
                    }
                }
            }
            
            self.assertTrue(usage_submit_result["success"])
            self.assertEqual(usage_submit_result["data"]["duration_recorded"], 300)
            self.assertEqual(usage_submit_result["data"]["remaining_quota"]["daily_remaining_seconds"], 300)
            print("✅ 使用記錄提交成功")
            
            # 步驟 3: 使用後配額檢查
            print("步驟 3: 使用後配額檢查...")
            post_usage_check_result = {
                "success": True,
                "data": {
                    "can_use": True,
                    "remaining_seconds": 300,  # 更新後的剩餘配額
                    "daily_limit": 600,
                    "estimated_sessions_remaining": 1
                }
            }
            
            self.assertTrue(post_usage_check_result["data"]["can_use"])
            self.assertEqual(post_usage_check_result["data"]["remaining_seconds"], 300)
            print("✅ 使用後配額檢查正確")
            
            # 步驟 4: 驗證數據一致性
            print("步驟 4: 驗證數據一致性...")
            dashboard_after_usage = {
                "success": True,
                "data": {
                    "usage": {
                        "daily": {
                            "limit_seconds": 600,
                            "used_seconds": 300,
                            "remaining_seconds": 300,
                            "usage_percentage": 50.0,
                            "can_use": True
                        },
                        "recent_7_days": {
                            "total_seconds": 300,
                            "session_count": 1
                        }
                    }
                }
            }
            
            daily_usage = dashboard_after_usage["data"]["usage"]["daily"]
            self.assertEqual(daily_usage["used_seconds"], 300)
            self.assertEqual(daily_usage["remaining_seconds"], 300)
            self.assertEqual(daily_usage["usage_percentage"], 50.0)
            print("✅ 數據一致性驗證通過")
            
            print("🎉 場景 4 完成: 使用量配額管理正確")
            
        except Exception as e:
            print(f"❌ 場景 4 失敗: {str(e)}")
            self.fail(f"使用量配額管理失敗: {str(e)}")
    
    def test_scenario_5_subscription_upgrade_v23(self):
        """場景 5: 訂閱升級流程 (V2.3 新增)"""
        print("\n💳 場景 5: 訂閱升級流程 (V2.3)")
        print("=" * 50)
        
        try:
            # 步驟 1: 創建付費訂閱
            print("步驟 1: 創建付費訂閱...")
            subscription_data = {
                # 計劃資訊
                "plan": "PRO",
                "daily_limit_seconds": 10800,
                "max_devices": 2,
                "price_amount": 1999,
                "currency": "usd",
                "billing_cycle": "monthly",
                
                # Stripe 支付資訊
                "stripe_invoice_id": f"in_{self.timestamp}",
                "stripe_customer_id": f"cus_{self.timestamp}",
                "stripe_subscription_id": f"sub_{self.timestamp}",
                "amount_total": 1999,
                "amount_paid": 1999,
                "payment_status": "succeeded",
                "invoice_created": datetime.utcnow().isoformat(),
                "period_start": datetime.utcnow().isoformat(),
                "period_end": "2025-02-27T00:00:00Z"
            }
            
            subscription_result = {
                "success": True,
                "data": {
                    "subscription": {
                        "subscription_id": f"sub_{self.test_user_id}",
                        "user_id": self.test_user_id,
                        "plan": "PRO",
                        "status": "active",
                        "daily_limit_seconds": 10800,
                        "max_devices": 2,
                        "stripe_customer_id": f"cus_{self.timestamp}",
                        "billing_cycle": "monthly",
                        "auto_renew": True
                    }
                },
                "message": "PRO 訂閱創建成功"
            }
            
            self.assertTrue(subscription_result["success"])
            self.assertEqual(subscription_result["data"]["subscription"]["plan"], "PRO")
            print("✅ PRO 訂閱創建成功")
            
            # 步驟 2: 驗證訂閱更新
            print("步驟 2: 驗證訂閱更新...")
            updated_dashboard = {
                "success": True,
                "data": {
                    "subscription": {
                        "plan": "PRO",
                        "status": "active",
                        "daily_limit_seconds": 10800,
                        "max_devices": 2,
                        "auto_renew": True
                    },
                    "usage": {
                        "daily": {
                            "limit_seconds": 10800,
                            "used_seconds": 300,
                            "remaining_seconds": 10500,
                            "usage_percentage": 2.8,
                            "can_use": True
                        }
                    }
                }
            }
            
            subscription = updated_dashboard["data"]["subscription"]
            self.assertEqual(subscription["plan"], "PRO")
            self.assertEqual(subscription["daily_limit_seconds"], 10800)
            self.assertEqual(subscription["max_devices"], 2)
            print("✅ 訂閱更新驗證成功")
            
            # 步驟 3: 註冊第二台設備（現在應該成功）
            print("步驟 3: 註冊第二台設備...")
            second_device_result = {
                "success": True,
                "code": 2,  # 設備註冊成功
                "message": "設備註冊成功",
                "data": {
                    "devices_info": {
                        "current_count": 2,
                        "max_allowed": 2,
                        "can_add_more": False
                    }
                }
            }
            
            self.assertEqual(second_device_result["code"], 2)
            self.assertEqual(second_device_result["data"]["devices_info"]["current_count"], 2)
            print("✅ 第二台設備註冊成功")
            
            print("🎉 場景 5 完成: 訂閱升級流程正確")
            
        except Exception as e:
            print(f"❌ 場景 5 失敗: {str(e)}")
            self.fail(f"訂閱升級流程失敗: {str(e)}")

def run_business_workflows_v23_tests():
    """執行 V2.3 業務流程測試"""
    print("🧪 開始 SpeechPilot V2.3 業務流程測試...")
    print("=" * 60)
    
    # 創建測試套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestBusinessWorkflowsV23)
    
    # 執行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("=" * 60)
    
    if result.wasSuccessful():
        print("🎉 所有 V2.3 業務流程測試通過！")
        print("\n📋 測試場景總結:")
        print("✅ 場景 1: 新用戶 Onboarding (統一 API)")
        print("✅ 場景 2: 現有用戶登入 (統一 API)")
        print("✅ 場景 3: 設備數量超限處理")
        print("✅ 場景 4: 使用量配額管理")
        print("✅ 場景 5: 訂閱升級流程 (新增)")
        print("\n🚀 V2.3 統一 API 架構驗證完成！")
        return True
    else:
        print(f"❌ 測試失敗: {len(result.failures)} 個失敗, {len(result.errors)} 個錯誤")
        
        if result.failures:
            print("\n失敗的測試:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
        
        if result.errors:
            print("\n錯誤的測試:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
        
        return False

if __name__ == '__main__':
    success = run_business_workflows_v23_tests()
    sys.exit(0 if success else 1)
