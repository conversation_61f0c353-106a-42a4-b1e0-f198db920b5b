"""
配額管理模組 - 統一配額計算和同步
解決配額冗餘和同步問題
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, Tuple
from firebase_admin import firestore
from .constants import SUBSCRIPTION_PLANS
from .environment import get_db

logger = logging.getLogger(__name__)

class QuotaManager:
    """配額管理器 - 單一數據源管理配額"""
    
    def __init__(self):
        self.db = get_db()
    
    def get_subscription_quota(self, subscription_id: str) -> Dict[str, Any]:
        """
        從訂閱表獲取配額資訊（單一數據源）
        
        Args:
            subscription_id: 訂閱ID
            
        Returns:
            Dict[str, Any]: 配額資訊
        """
        try:
            subscription_ref = self.db.collection('subscriptions').document(subscription_id)
            subscription_doc = subscription_ref.get()
            
            if not subscription_doc.exists:
                raise ValueError(f"訂閱不存在: {subscription_id}")
            
            subscription_data = subscription_doc.to_dict()
            plan = subscription_data.get('plan', 'FREE')
            plan_config = SUBSCRIPTION_PLANS.get(plan, SUBSCRIPTION_PLANS['FREE'])
            
            return {
                'subscription_id': subscription_id,
                'plan': plan,
                'status': subscription_data.get('status', 'active'),
                'daily_limit_seconds': plan_config['daily_limit_seconds'],
                'current_day_used_seconds': subscription_data.get('current_day_used_seconds', 0),
                'max_devices': plan_config['max_devices'],
                'active_device_ids': subscription_data.get('active_device_ids', []),
                'last_daily_reset_date': subscription_data.get('last_daily_reset_date'),
                'last_daily_reset_at': subscription_data.get('last_daily_reset_at'),
                'register_region_timezone': subscription_data.get('register_region_timezone', 'UTC+8'),
                'usage_reset_hour': subscription_data.get('usage_reset_hour', 0)
            }
            
        except Exception as e:
            logger.error(f"獲取訂閱配額失敗: {subscription_id}, 錯誤: {str(e)}")
            raise
    
    def check_daily_quota(self, subscription_id: str, requested_seconds: int = 0) -> Dict[str, Any]:
        """
        檢查每日配額是否足夠
        
        Args:
            subscription_id: 訂閱ID
            requested_seconds: 請求使用的秒數
            
        Returns:
            Dict[str, Any]: 配額檢查結果
        """
        try:
            quota_info = self.get_subscription_quota(subscription_id)
            
            # 檢查是否需要重置配額
            reset_needed, reset_info = self._check_reset_needed(quota_info)
            if reset_needed:
                quota_info = self._reset_daily_quota(subscription_id, reset_info)
            
            daily_limit = quota_info['daily_limit_seconds']
            daily_used = quota_info['current_day_used_seconds']
            
            # 無限制計劃
            if daily_limit == -1:
                return {
                    'can_use': True,
                    'daily_remaining': -1,
                    'daily_limit': -1,
                    'daily_used': daily_used,
                    'reason': None
                }
            
            daily_remaining = max(0, daily_limit - daily_used)
            can_use = daily_remaining >= requested_seconds
            
            return {
                'can_use': can_use,
                'daily_remaining': daily_remaining,
                'daily_limit': daily_limit,
                'daily_used': daily_used,
                'reason': None if can_use else f"配額不足，剩餘 {daily_remaining} 秒，需要 {requested_seconds} 秒"
            }
            
        except Exception as e:
            logger.error(f"檢查每日配額失敗: {subscription_id}, 錯誤: {str(e)}")
            raise
    
    def update_usage(self, subscription_id: str, used_seconds: int) -> Dict[str, Any]:
        """
        更新使用量（原子操作）
        
        Args:
            subscription_id: 訂閱ID
            used_seconds: 使用的秒數
            
        Returns:
            Dict[str, Any]: 更新結果
        """
        try:
            subscription_ref = self.db.collection('subscriptions').document(subscription_id)
            
            # 使用事務確保原子性
            @firestore.transactional
            def update_in_transaction(transaction):
                subscription_doc = subscription_ref.get(transaction=transaction)
                if not subscription_doc.exists:
                    raise ValueError(f"訂閱不存在: {subscription_id}")
                
                current_data = subscription_doc.to_dict()
                current_used = current_data.get('current_day_used_seconds', 0)
                new_used = current_used + used_seconds
                
                transaction.update(subscription_ref, {
                    'current_day_used_seconds': new_used,
                    'updated_at': datetime.utcnow()
                })
                
                return new_used
            
            transaction = self.db.transaction()
            new_total_used = update_in_transaction(transaction)
            
            logger.info(f"配額更新成功: {subscription_id}, 使用 {used_seconds} 秒, 總計 {new_total_used} 秒")
            
            return {
                'success': True,
                'used_seconds': used_seconds,
                'total_used_seconds': new_total_used
            }
            
        except Exception as e:
            logger.error(f"更新使用量失敗: {subscription_id}, 錯誤: {str(e)}")
            raise
    
    def _check_reset_needed(self, quota_info: Dict[str, Any]) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        檢查是否需要重置每日配額
        
        Args:
            quota_info: 配額資訊
            
        Returns:
            Tuple[bool, Optional[Dict[str, Any]]]: (是否需要重置, 重置資訊)
        """
        try:
            last_reset_date = quota_info.get('last_daily_reset_date')
            timezone_str = quota_info.get('register_region_timezone', 'UTC+8')
            reset_hour = quota_info.get('usage_reset_hour', 0)
            
            # 解析時區
            if timezone_str.startswith('UTC'):
                offset_str = timezone_str[3:]
                if offset_str:
                    offset_hours = int(offset_str)
                else:
                    offset_hours = 0
            else:
                offset_hours = 8  # 預設為 UTC+8
            
            # 計算用戶時區的當前時間
            user_tz = timezone(timedelta(hours=offset_hours))
            user_now = datetime.now(user_tz)
            user_today = user_now.strftime('%Y-%m-%d')
            
            # 檢查是否需要重置
            if not last_reset_date or last_reset_date != user_today:
                # 計算重置時間
                reset_time = user_now.replace(hour=reset_hour, minute=0, second=0, microsecond=0)
                if user_now < reset_time:
                    # 如果當前時間早於重置時間，使用昨天的重置時間
                    reset_time = reset_time - timedelta(days=1)
                
                return True, {
                    'reset_date': user_today,
                    'reset_time_utc': reset_time.astimezone(timezone.utc),
                    'user_timezone': timezone_str
                }
            
            return False, None
            
        except Exception as e:
            logger.error(f"檢查重置需求失敗: {str(e)}")
            return False, None
    
    def _reset_daily_quota(self, subscription_id: str, reset_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        重置每日配額
        
        Args:
            subscription_id: 訂閱ID
            reset_info: 重置資訊
            
        Returns:
            Dict[str, Any]: 重置後的配額資訊
        """
        try:
            subscription_ref = self.db.collection('subscriptions').document(subscription_id)
            
            # 重置配額
            subscription_ref.update({
                'current_day_used_seconds': 0,
                'last_daily_reset_date': reset_info['reset_date'],
                'last_daily_reset_at': reset_info['reset_time_utc'],
                'updated_at': datetime.utcnow()
            })
            
            logger.info(f"每日配額重置成功: {subscription_id}, 重置日期: {reset_info['reset_date']}")
            
            # 返回重置後的配額資訊
            return self.get_subscription_quota(subscription_id)
            
        except Exception as e:
            logger.error(f"重置每日配額失敗: {subscription_id}, 錯誤: {str(e)}")
            raise

# 全域配額管理器實例
quota_manager = QuotaManager()
