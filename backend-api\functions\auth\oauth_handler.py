"""
OAuth authentication handler for SpeechPilot Firebase Functions
"""

import logging
import urllib.parse
from firebase_functions import https_fn
from firebase_admin import auth
from typing import Dict, Any

logger = logging.getLogger(__name__)

def google_oauth_handler(request: https_fn.Request) -> https_fn.Response:
    """
    處理 Google OAuth 認證流程
    
    Args:
        request: Firebase Functions HTTP 請求對象
        
    Returns:
        https_fn.Response: 重定向回應或錯誤頁面
    """
    try:
        # 獲取查詢參數
        state = request.args.get('state')
        platform = request.args.get('platform', 'desktop')
        code = request.args.get('code')
        error = request.args.get('error')
        
        logger.info(f"OAuth callback received - state: {state}, platform: {platform}")
        
        # 處理 OAuth 錯誤
        if error:
            logger.error(f"OAuth error: {error}")
            return create_error_response(error, platform)
        
        # 處理授權碼
        if code:
            try:
                # 這裡需要實作 Google OAuth 授權碼交換
                # 由於 Firebase Auth 已經處理了大部分 OAuth 邏輯，
                # 建議使用 Firebase Auth SDK 而不是自建
                
                # 創建成功回應
                return create_success_response(state, platform)
                
            except Exception as e:
                logger.error(f"Error processing OAuth code: {str(e)}")
                return create_error_response("token_exchange_failed", platform)
        
        # 如果沒有 code 或 error，返回錯誤
        return create_error_response("invalid_request", platform)
        
    except Exception as e:
        logger.error(f"Unexpected error in OAuth handler: {str(e)}")
        return create_error_response("internal_error", platform)

def create_success_response(state: str, platform: str) -> https_fn.Response:
    """創建成功回應"""
    
    if platform == 'desktop':
        # 桌面應用 - 重定向到自定義協議
        redirect_url = f"speechpilot://auth/success?state={state}"
    else:
        # 其他平台的處理
        redirect_url = f"speakoneai://auth/success?state={state}"
    
    # 創建重定向回應
    response = https_fn.Response(
        status=302,
        headers={'Location': redirect_url}
    )
    
    return response

def create_error_response(error: str, platform: str) -> https_fn.Response:
    """創建錯誤回應"""
    
    if platform == 'desktop':
        redirect_url = f"speechpilot://auth/error?error={error}"
    else:
        redirect_url = f"speakoneai://auth/error?error={error}"
    
    # 創建重定向回應
    response = https_fn.Response(
        status=302,
        headers={'Location': redirect_url}
    )
    
    return response

def google_oauth_initiate_handler(request: https_fn.Request) -> https_fn.Response:
    """
    啟動 Google OAuth 流程
    """
    try:
        # 獲取參數
        platform = request.args.get('platform', 'desktop')
        state = request.args.get('state')
        
        if not state:
            return https_fn.Response(
                "Missing state parameter",
                status=400
            )
        
        # Google OAuth 配置
        client_id = "your-google-client-id.googleusercontent.com"  # 需要配置
        
        if platform == 'desktop':
            redirect_uri = "https://asia-east1-speechpilot-f1495.cloudfunctions.net/auth/google"
        else:
            redirect_uri = "https://asia-east1-speechpilot-f1495.cloudfunctions.net/auth/google"
        
        # 構建 Google OAuth URL
        oauth_params = {
            'client_id': client_id,
            'redirect_uri': redirect_uri,
            'response_type': 'code',
            'scope': 'openid email profile',
            'state': f"{state}&platform={platform}",
            'access_type': 'offline',
            'prompt': 'consent'
        }
        
        oauth_url = "https://accounts.google.com/o/oauth2/v2/auth?" + urllib.parse.urlencode(oauth_params)
        
        # 重定向到 Google OAuth
        return https_fn.Response(
            status=302,
            headers={'Location': oauth_url}
        )
        
    except Exception as e:
        logger.error(f"Error initiating OAuth: {str(e)}")
        return https_fn.Response(
            "Internal server error",
            status=500
        )
