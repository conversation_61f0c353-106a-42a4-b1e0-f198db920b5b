"""
檢查用戶訂閱狀態
獲取用戶當前訂閱狀態和使用量資訊
"""

import logging
from datetime import datetime
from typing import Dict, Any
from firebase_functions import https_fn
from firebase_admin import firestore

logger = logging.getLogger(__name__)

@https_fn.on_request(cors=https_fn.CorsOptions(
    cors_origins=["*"],
    cors_methods=["GET", "POST", "OPTIONS"]
))
def check_user_subscription_status_handler(req: https_fn.Request, context=None) -> Dict[str, Any]:
    """檢查用戶訂閱狀態處理器"""
    try:
        # 獲取用戶 ID
        if context and hasattr(context, 'auth') and context.auth:
            user_id = context.auth.uid
        else:
            # 測試模式：從請求中獲取用戶 ID
            if req.method == 'POST':
                data = req.get_json()
                user_id = data.get('user_id') if data else None
            else:
                user_id = req.args.get('user_id')
        
        if not user_id:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.UNAUTHENTICATED,
                message='用戶未認證'
            )

        # 獲取 Firestore 客戶端（環境感知）
        from ..utils.environment import get_db
        db = get_db()
        
        # 獲取用戶資訊
        user_ref = db.collection('users').document(user_id)
        user_doc = user_ref.get()
        
        if not user_doc.exists:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.NOT_FOUND,
                message='用戶不存在'
            )
        
        user_data = user_doc.to_dict()
        
        # 獲取訂閱資訊
        subscription_id = user_data.get('current_subscription_id', f"sub_{user_id}")
        subscription_ref = db.collection('subscriptions').document(subscription_id)
        subscription_doc = subscription_ref.get()
        
        if not subscription_doc.exists:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.NOT_FOUND,
                message='訂閱不存在'
            )
        
        subscription_data = subscription_doc.to_dict()
        
        # 獲取設備資訊
        devices_query = db.collection('devices').where('user_id', '==', user_id).where('is_active', '==', True)
        devices_docs = devices_query.get()
        active_devices = [doc.to_dict() for doc in devices_docs]
        
        # 計算使用量狀態
        daily_limit = subscription_data.get('daily_limit_seconds', 0)
        monthly_limit = subscription_data.get('monthly_limit_seconds', 0)
        daily_used = subscription_data.get('daily_usage_seconds', 0)
        monthly_used = subscription_data.get('current_period_usage_seconds', 0)
        
        daily_remaining = max(0, daily_limit - daily_used) if daily_limit > 0 else float('inf')
        monthly_remaining = max(0, monthly_limit - monthly_used) if monthly_limit > 0 else float('inf')
        
        can_use = True
        if daily_limit > 0 and daily_used >= daily_limit:
            can_use = False
        elif monthly_limit > 0 and monthly_used >= monthly_limit:
            can_use = False
        
        # 計算百分比
        daily_percentage = (daily_used / daily_limit * 100) if daily_limit > 0 else 0
        monthly_percentage = (monthly_used / monthly_limit * 100) if monthly_limit > 0 else 0
        
        # 設備狀態
        max_devices = subscription_data.get('max_devices', 1)
        current_device_count = len(active_devices)
        can_add_device = current_device_count < max_devices if max_devices > 0 else True
        
        response = {
            'success': True,
            'data': {
                'subscription': {
                    'plan': subscription_data.get('plan'),
                    'status': subscription_data.get('status'),
                    'daily_limit_seconds': daily_limit,
                    'monthly_limit_seconds': monthly_limit,
                    'current_period_usage_seconds': monthly_used,
                    'daily_usage_seconds': daily_used,
                    'currentPeriodStart': subscription_data.get('currentPeriodStart'),
                    'currentPeriodEnd': subscription_data.get('currentPeriodEnd')
                },
                'usage_status': {
                    'can_use': can_use,
                    'daily_remaining_seconds': daily_remaining if daily_remaining != float('inf') else -1,
                    'monthly_remaining_seconds': monthly_remaining if monthly_remaining != float('inf') else -1,
                    'daily_remaining_percentage': max(0, 100 - daily_percentage),
                    'monthly_remaining_percentage': max(0, 100 - monthly_percentage)
                },
                'devices': {
                    'current_count': current_device_count,
                    'max_allowed': max_devices,
                    'can_add_device': can_add_device,
                    'devices': [
                        {
                            'device_id': device.get('device_id'),
                            'device_name': device.get('device_name'),
                            'platform': device.get('platform'),
                            'last_active_at': device.get('last_active_at')
                        } for device in active_devices
                    ]
                }
            }
        }
        
        logger.info(f"✅ 訂閱狀態檢查成功: {user_id} - {subscription_data.get('plan')}")
        return response
        
    except https_fn.HttpsError:
        raise
    except Exception as e:
        logger.error(f"❌ 檢查訂閱狀態失敗: {str(e)}")
        raise https_fn.HttpsError(
            code=https_fn.FunctionsErrorCode.INTERNAL,
            message='內部錯誤'
        )
