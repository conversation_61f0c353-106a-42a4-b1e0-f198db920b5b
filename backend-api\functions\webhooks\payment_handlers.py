"""
Stripe 支付事件處理函數
處理支付成功和失敗事件
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from firebase_admin import firestore

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端（環境感知）"""
    from ..utils.environment import get_db as get_env_db
    return get_env_db()

def get_user_id_from_customer(customer_id: str) -> Optional[str]:
    """
    根據 Stripe Customer ID 獲取用戶 ID
    
    Args:
        customer_id: Stripe Customer ID
        
    Returns:
        str: 用戶 ID 或 None
    """
    try:
        db = get_db()
        
        # 在 users 集合中查找 stripe_customer_id
        users_ref = db.collection('users')
        query = users_ref.where('stripe_customer_id', '==', customer_id).limit(1)
        docs = query.stream()
        
        for doc in docs:
            return doc.id
        
        # 如果在 users 中找不到，嘗試在 subscriptions 中查找
        subscriptions_ref = db.collection('subscriptions')
        query = subscriptions_ref.where('stripe_customer_id', '==', customer_id).limit(1)
        docs = query.stream()
        
        for doc in docs:
            subscription_data = doc.to_dict()
            return subscription_data.get('user_id')
        
        logger.error(f"找不到 Customer ID {customer_id} 對應的用戶")
        return None
        
    except Exception as e:
        logger.error(f"查找用戶錯誤: {str(e)}")
        return None

def get_subscription_plan_from_invoice(invoice: Dict[str, Any]) -> Optional[str]:
    """
    從發票中獲取訂閱計劃
    
    Args:
        invoice: Stripe Invoice 對象
        
    Returns:
        str: 計劃名稱或 None
    """
    try:
        # 從發票行項目中獲取價格信息
        if 'lines' in invoice and 'data' in invoice['lines']:
            for line in invoice['lines']['data']:
                if line.get('type') == 'subscription':
                    price_id = line.get('price', {}).get('id')
                    if price_id:
                        # 根據 Price ID 映射計劃
                        from ..utils.stripe_config import get_plan_from_stripe_price_id
                        return get_plan_from_stripe_price_id(price_id)
        
        return None
        
    except Exception as e:
        logger.error(f"從發票獲取計劃錯誤: {str(e)}")
        return None

def handle_payment_succeeded(invoice: Dict[str, Any]):
    """
    處理支付成功事件
    
    Args:
        invoice: Stripe Invoice 對象
    """
    try:
        customer_id = invoice['customer']
        invoice_id = invoice['id']
        subscription_id = invoice.get('subscription')
        
        # 獲取用戶 ID
        user_id = get_user_id_from_customer(customer_id)
        if not user_id:
            logger.error(f"找不到 Customer {customer_id} 對應的用戶")
            return
        
        # 獲取計劃信息
        plan = get_subscription_plan_from_invoice(invoice)
        if not plan:
            logger.warning(f"無法從發票 {invoice_id} 獲取計劃信息")
            plan = 'UNKNOWN'
        
        db = get_db()
        now = datetime.utcnow()
        
        # 創建購買記錄
        purchase_data = {
            # Stripe 相關資訊
            'stripe_invoice_id': invoice_id,
            'stripe_customer_id': customer_id,
            'stripe_subscription_id': subscription_id,
            'stripe_payment_intent_id': invoice.get('payment_intent'),
            
            # 金額資訊
            'amount_total': invoice.get('amount_paid', 0),
            'amount_subtotal': invoice.get('subtotal', 0),
            'amount_tax': invoice.get('tax', 0),
            'amount_paid': invoice.get('amount_paid', 0),
            'amount_due': invoice.get('amount_due', 0),
            'amount_remaining': invoice.get('amount_remaining', 0),
            
            # 貨幣和地區
            'currency': invoice.get('currency', 'usd').upper(),
            'account_country': invoice.get('account_country', 'US'),
            
            # 客戶資訊
            'customer_email': invoice.get('customer_email', ''),
            'customer_name': invoice.get('customer_name', ''),
            'customer_phone': invoice.get('customer_phone', ''),
            'customer_address': invoice.get('customer_address', {}),
            
            # 發票狀態
            'invoice_status': invoice.get('status', 'paid'),
            'collection_method': invoice.get('collection_method', 'charge_automatically'),
            'billing_reason': invoice.get('billing_reason', 'subscription_cycle'),
            
            # 時間資訊
            'invoice_created': datetime.fromtimestamp(invoice.get('created', 0), tz=timezone.utc) if invoice.get('created') else now,
            'period_start': datetime.fromtimestamp(invoice.get('period_start', 0), tz=timezone.utc) if invoice.get('period_start') else None,
            'period_end': datetime.fromtimestamp(invoice.get('period_end', 0), tz=timezone.utc) if invoice.get('period_end') else None,
            'due_date': datetime.fromtimestamp(invoice.get('due_date', 0), tz=timezone.utc) if invoice.get('due_date') else None,
            
            # 支付狀態
            'payment_status': 'succeeded',
            'payment_attempted': True,
            'attempt_count': invoice.get('attempt_count', 1),
            
            # 訂閱相關
            'subscription_plan': plan,
            'subscription_period': 'monthly',  # 預設值，可以從價格信息中獲取
            'unit_amount': 0,  # 可以從行項目中獲取
            
            # 元數據
            'stripe_metadata': invoice.get('metadata', {}),
            'webhook_event_type': 'invoice.payment_succeeded',
            
            # 內部狀態
            'processed': True,
            'processing_errors': [],
            
            # 應用相關
            'user_id': user_id,
            'source_app': 'webhook',
            
            # 時間戳
            'created_at': now,
            'updated_at': now
        }
        
        # 從發票行項目中獲取更詳細的信息
        if 'lines' in invoice and 'data' in invoice['lines']:
            for line in invoice['lines']['data']:
                if line.get('type') == 'subscription':
                    price = line.get('price', {})
                    purchase_data['unit_amount'] = price.get('unit_amount', 0)
                    
                    # 獲取計費週期
                    recurring = price.get('recurring', {})
                    interval = recurring.get('interval', 'month')
                    purchase_data['subscription_period'] = 'yearly' if interval == 'year' else 'monthly'
                    break
        
        # 保存購買記錄
        purchase_ref = db.collection('purchases').document()
        purchase_ref.set(purchase_data)
        
        # 重置每日配額（新的計費週期開始）
        billing_reason = invoice.get('billing_reason')
        if billing_reason in ['subscription_cycle', 'subscription_create']:
            subscription_ref = db.collection('subscriptions').document(f"sub_{user_id}")
            subscription_ref.update({
                'current_day_used_seconds': 0,
                'last_daily_reset_date': now.strftime('%Y-%m-%d'),
                'last_daily_reset_at': now,
                'webhook_events.invoice.payment_succeeded': now,
                'updated_at': now
            })
            
            logger.info(f"支付成功，用戶 {user_id} 每日配額已重置")
        
        logger.info(f"支付成功處理完成: 用戶 {user_id}, 發票 {invoice_id}, 計劃 {plan}")
        
    except Exception as e:
        logger.error(f"處理支付成功錯誤: {str(e)}")
        raise

def handle_payment_failed(invoice: Dict[str, Any]):
    """
    處理支付失敗事件
    
    Args:
        invoice: Stripe Invoice 對象
    """
    try:
        customer_id = invoice['customer']
        invoice_id = invoice['id']
        subscription_id = invoice.get('subscription')
        
        # 獲取用戶 ID
        user_id = get_user_id_from_customer(customer_id)
        if not user_id:
            logger.error(f"找不到 Customer {customer_id} 對應的用戶")
            return
        
        # 獲取計劃信息
        plan = get_subscription_plan_from_invoice(invoice)
        if not plan:
            logger.warning(f"無法從發票 {invoice_id} 獲取計劃信息")
            plan = 'UNKNOWN'
        
        db = get_db()
        now = datetime.utcnow()
        
        # 創建失敗的購買記錄
        purchase_data = {
            # Stripe 相關資訊
            'stripe_invoice_id': invoice_id,
            'stripe_customer_id': customer_id,
            'stripe_subscription_id': subscription_id,
            'stripe_payment_intent_id': invoice.get('payment_intent'),
            
            # 金額資訊
            'amount_total': invoice.get('amount_due', 0),
            'amount_subtotal': invoice.get('subtotal', 0),
            'amount_tax': invoice.get('tax', 0),
            'amount_paid': 0,  # 支付失敗
            'amount_due': invoice.get('amount_due', 0),
            'amount_remaining': invoice.get('amount_due', 0),
            
            # 貨幣和地區
            'currency': invoice.get('currency', 'usd').upper(),
            'account_country': invoice.get('account_country', 'US'),
            
            # 客戶資訊
            'customer_email': invoice.get('customer_email', ''),
            'customer_name': invoice.get('customer_name', ''),
            
            # 發票狀態
            'invoice_status': invoice.get('status', 'open'),
            'collection_method': invoice.get('collection_method', 'charge_automatically'),
            'billing_reason': invoice.get('billing_reason', 'subscription_cycle'),
            
            # 時間資訊
            'invoice_created': datetime.fromtimestamp(invoice.get('created', 0), tz=timezone.utc) if invoice.get('created') else now,
            'due_date': datetime.fromtimestamp(invoice.get('due_date', 0), tz=timezone.utc) if invoice.get('due_date') else None,
            
            # 支付狀態
            'payment_status': 'failed',
            'payment_attempted': True,
            'attempt_count': invoice.get('attempt_count', 1),
            
            # 訂閱相關
            'subscription_plan': plan,
            'subscription_period': 'monthly',
            
            # 元數據
            'stripe_metadata': invoice.get('metadata', {}),
            'webhook_event_type': 'invoice.payment_failed',
            
            # 內部狀態
            'processed': True,
            'processing_errors': [],
            
            # 應用相關
            'user_id': user_id,
            'source_app': 'webhook',
            
            # 時間戳
            'created_at': now,
            'updated_at': now
        }
        
        # 保存失敗的購買記錄
        purchase_ref = db.collection('purchases').document()
        purchase_ref.set(purchase_data)
        
        # 更新訂閱狀態（可能需要暫停服務）
        subscription_ref = db.collection('subscriptions').document(f"sub_{user_id}")
        subscription_ref.update({
            'webhook_events.invoice.payment_failed': now,
            'updated_at': now
        })
        
        logger.info(f"支付失敗處理完成: 用戶 {user_id}, 發票 {invoice_id}")
        
    except Exception as e:
        logger.error(f"處理支付失敗錯誤: {str(e)}")
        raise
