"""
SpeechPilot Firebase Functions - Python Implementation
Main entry point for all Firebase Functions
"""

from firebase_functions import https_fn
from firebase_admin import initialize_app, firestore
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Firebase Admin
try:
    import os
    # Check if we're in a deployment/production environment
    if (os.getenv('FUNCTIONS_EMULATOR') or
        os.getenv('FIREBASE_CONFIG') or
        os.getenv('GCP_PROJECT') or
        os.getenv('GOOGLE_CLOUD_PROJECT')):
        # Running in emulator, deployment, or production
        initialize_app()
        db = firestore.client()
        logger.info("Firebase Admin initialized successfully")
    else:
        # Local development without credentials - don't initialize
        logger.info("Running in local development mode without Firebase credentials")
        db = None
except Exception as e:
    logger.warning(f"Firebase Admin initialization failed: {e}")
    # This is expected in local development without credentials
    db = None

# Import function modules
from functions.usage.check_before_recording import check_usage_before_recording_handler
from functions.usage.calculate_after_recording import calculate_usage_after_recording_handler
from functions.usage.submit_usage import submit_usage_handler
from functions.devices.validate_or_register_device import validate_or_register_device_handler
from functions.devices.remove_device import remove_device_handler
from functions.users.create_or_update_user import create_or_update_user_handler
from functions.users.user_dashboard import get_user_dashboard_handler
from functions.subscriptions.create_subscription import create_subscription_handler
from functions.webhooks.stripe_webhook import stripe_webhook_handler
from functions.scheduled.daily_quota_reset import daily_quota_reset_handler
# 移除管理員 API 和購買 API - 不對外暴露

# Export Firebase Functions with Asia East region

# check_user_subscription_status 已移除 - 使用 get_user_dashboard

@https_fn.on_call(region="asia-east1")
def check_usage_before_recording(req: https_fn.CallableRequest) -> dict:
    """錄音前檢查可用使用時間"""
    return check_usage_before_recording_handler(req)

@https_fn.on_call(region="asia-east1")
def calculate_usage_after_recording(req: https_fn.CallableRequest) -> dict:
    """錄音完成後計算使用量和成本"""
    return calculate_usage_after_recording_handler(req)

@https_fn.on_call(region="asia-east1")
def submit_usage(req: https_fn.CallableRequest) -> dict:
    """提交使用量記錄"""
    return submit_usage_handler(req)

# register_device 已移除，統一使用 register_device_v2

@https_fn.on_call(region="asia-east1")
def get_user_dashboard(req: https_fn.CallableRequest) -> dict:
    """獲取用戶儀表板 - 三合一 API"""
    return get_user_dashboard_handler(req)

@https_fn.on_call(region="asia-east1")
def create_or_update_user(req: https_fn.CallableRequest) -> dict:
    """創建或更新用戶記錄"""
    return create_or_update_user_handler(req)

# createOrUpdateUser 已移除 - 統一使用 create_or_update_user

@https_fn.on_call(region="asia-east1")
def create_subscription(req: https_fn.CallableRequest) -> dict:
    """創建訂閱記錄（通用函數）"""
    return create_subscription_handler(req)

@https_fn.on_call(region="asia-east1")
def validate_or_register_device(req: https_fn.CallableRequest) -> dict:
    """統一設備驗證和註冊"""
    return validate_or_register_device_handler(req)

@https_fn.on_call(region="asia-east1")
def remove_device(req: https_fn.CallableRequest) -> dict:
    """移除設備"""
    return remove_device_handler(req)

# Stripe Webhook 處理函數
@https_fn.on_request(region="asia-east1", cors=https_fn.CorsOptions(cors_origins="*", cors_methods=["POST"]))
def stripe_webhook(req: https_fn.Request) -> https_fn.Response:
    """Stripe Webhook 處理端點"""
    return stripe_webhook_handler(req)

# 定時任務已在 daily_quota_reset.py 中使用 @scheduler_fn.on_schedule 裝飾器自動註冊

# 管理功能和購買功能已移除 - 不對外暴露

# Firestore triggers removed - user initialization handled by create_or_update_user function
