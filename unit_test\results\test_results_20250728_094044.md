# 🧪 SpeakOneAI Stripe Webhook 測試結果報告

**生成時間**: 2025-07-28 09:40:44

## 📊 測試總結

- **總場景數**: 4
- **通過場景**: 4 ✅
- **失敗場景**: 0 ❌
- **成功率**: 100.0%

## 📋 詳細測試結果

### 1. ✅ 訂閱創建 + 支付成功流程

**描述**: 測試用戶從 FREE 升級到 PRO 計劃的完整流程，包括訂閱創建和首次支付

**開始時間**: 09:40:40
**結束時間**: 09:40:41
**耗時**: 1.17 秒
**狀態**: passed

#### 🔔 Webhook 調用記錄

1. **customer.subscription.created** (09:40:40)
   - 描述: 用戶 create_pay_test_1753666840 創建 PRO 計劃訂閱

2. **invoice.payment_succeeded** (09:40:40)
   - 描述: 用戶 create_pay_test_1753666840 支付成功，金額 $19.99

#### 💾 數據庫變更記錄

1. **users** 表 - INSERT (09:40:40)
   - 用戶: create_pay_test_1753666840

2. **subscriptions** 表 - INSERT (09:40:40)
   - 用戶: create_pay_test_1753666840
   - 訂閱變更:
     - plan: N/A → FREE
     - status: N/A → active
     - daily_limit_seconds: N/A → 600
     - max_devices: N/A → 1
     - current_day_used_seconds: N/A → 0

3. **subscriptions** 表 - UPDATE (09:40:40)
   - 用戶: create_pay_test_1753666840
   - 訂閱變更:
     - plan: FREE → PRO
     - daily_limit_seconds: 600 → 10800
     - max_devices: 1 → 2

4. **purchases** 表 - INSERT (09:40:41)
   - 用戶: create_pay_test_1753666840
   - 新購買記錄:
     - 發票 ID: in_test_1753666840_create_pay_test_1753666840
     - 金額: $19.99
     - 計劃: PRO
     - 狀態: succeeded

5. **subscriptions** 表 - UPDATE (09:40:41)
   - 用戶: create_pay_test_1753666840
   - 訂閱變更:

#### 🔍 斷言結果

1. ✅ 初始計劃應該是 FREE
   - 期望: FREE
   - 實際: FREE

2. ✅ 訂閱創建後計劃應該是 PRO
   - 期望: PRO
   - 實際: PRO

3. ✅ 訂閱創建後配額應該是 10800s (3小時)
   - 期望: 10800
   - 實際: 10800

4. ✅ 訂閱創建後設備限制應該是 2
   - 期望: 2
   - 實際: 2

5. ✅ 應該創建 1 條購買記錄
   - 期望: 1
   - 實際: 1

6. ✅ 購買記錄的發票 ID 應該正確
   - 期望: in_test_1753666840_create_pay_test_1753666840
   - 實際: in_test_1753666840_create_pay_test_1753666840

7. ✅ 購買記錄的支付金額應該是 $19.99
   - 期望: 1999
   - 實際: 1999

8. ✅ 購買記錄的計劃應該是 PRO
   - 期望: PRO
   - 實際: PRO

9. ✅ 購買記錄的支付狀態應該是 succeeded
   - 期望: succeeded
   - 實際: succeeded

10. ✅ 支付成功後當日使用量應該重置為 0
   - 期望: 0
   - 實際: 0

---

### 2. ✅ 訂閱升級 + 支付成功流程

**描述**: 測試用戶從 PRO 升級到 PREMIUM 計劃，包括計劃變更和支付處理

**開始時間**: 09:40:41
**結束時間**: 09:40:41
**耗時**: 0.63 秒
**狀態**: passed

#### 🔔 Webhook 調用記錄

1. **customer.subscription.created** (09:40:41)
   - 描述: 用戶 upgrade_pay_test_1753666841 創建 PRO 計劃訂閱

2. **customer.subscription.updated** (09:40:41)
   - 描述: 用戶 upgrade_pay_test_1753666841 計劃變更: PRO → PREMIUM

3. **invoice.payment_succeeded** (09:40:41)
   - 描述: 用戶 upgrade_pay_test_1753666841 支付成功，金額 $59.99

#### 💾 數據庫變更記錄

1. **users** 表 - INSERT (09:40:41)
   - 用戶: upgrade_pay_test_1753666841

2. **subscriptions** 表 - INSERT (09:40:41)
   - 用戶: upgrade_pay_test_1753666841
   - 訂閱變更:
     - plan: N/A → FREE
     - status: N/A → active
     - daily_limit_seconds: N/A → 600
     - max_devices: N/A → 1
     - current_day_used_seconds: N/A → 0

3. **subscriptions** 表 - UPDATE (09:40:41)
   - 用戶: upgrade_pay_test_1753666841
   - 訂閱變更:
     - plan: FREE → PRO
     - daily_limit_seconds: 600 → 10800
     - max_devices: 1 → 2

4. **subscriptions** 表 - UPDATE (09:40:41)
   - 用戶: upgrade_pay_test_1753666841
   - 訂閱變更:
     - plan: PRO → PREMIUM
     - daily_limit_seconds: 10800 → 28800
     - max_devices: 2 → 5
     - current_day_used_seconds: 3600 → 0

5. **purchases** 表 - INSERT (09:40:41)
   - 用戶: upgrade_pay_test_1753666841
   - 新購買記錄:
     - 發票 ID: in_test_1753666840_upgrade_pay_test_1753666841
     - 金額: $59.99
     - 計劃: PREMIUM
     - 狀態: succeeded

6. **subscriptions** 表 - UPDATE (09:40:41)
   - 用戶: upgrade_pay_test_1753666841
   - 訂閱變更:

#### 🔍 斷言結果

1. ✅ 升級前計劃應該是 PRO
   - 期望: PRO
   - 實際: PRO

2. ✅ 升級後計劃應該是 PREMIUM
   - 期望: PREMIUM
   - 實際: PREMIUM

3. ✅ 升級後配額應該增加到 28800s (8小時)
   - 期望: 28800
   - 實際: 28800

4. ✅ 升級後設備限制應該增加到 5
   - 期望: 5
   - 實際: 5

5. ✅ 升級時使用量應該重置為 0
   - 期望: 0
   - 實際: 0

6. ✅ 應該有 PREMIUM 計劃的購買記錄
   - 期望: True
   - 實際: True

7. ✅ PREMIUM 購買記錄的金額應該是 $59.99
   - 期望: 5999
   - 實際: 5999

---

### 3. ✅ 訂閱取消流程

**描述**: 測試用戶取消 PREMIUM 訂閱，自動降級到 FREE 計劃

**開始時間**: 09:40:41
**結束時間**: 09:40:42
**耗時**: 0.22 秒
**狀態**: passed

#### 🔔 Webhook 調用記錄

1. **customer.subscription.created** (09:40:41)
   - 描述: 用戶 cancel_test_1753666841 創建 PREMIUM 計劃訂閱

2. **customer.subscription.deleted** (09:40:42)
   - 描述: 用戶 cancel_test_1753666841 訂閱取消，降級到 FREE 計劃

#### 💾 數據庫變更記錄

1. **users** 表 - INSERT (09:40:41)
   - 用戶: cancel_test_1753666841

2. **subscriptions** 表 - INSERT (09:40:41)
   - 用戶: cancel_test_1753666841
   - 訂閱變更:
     - plan: N/A → FREE
     - status: N/A → active
     - daily_limit_seconds: N/A → 600
     - max_devices: N/A → 1
     - current_day_used_seconds: N/A → 0

3. **subscriptions** 表 - UPDATE (09:40:41)
   - 用戶: cancel_test_1753666841
   - 訂閱變更:
     - plan: FREE → PREMIUM
     - daily_limit_seconds: 600 → 28800
     - max_devices: 1 → 5

4. **subscriptions** 表 - UPDATE (09:40:42)
   - 用戶: cancel_test_1753666841
   - 訂閱變更:
     - plan: PREMIUM → FREE
     - status: active → canceled
     - daily_limit_seconds: 28800 → 600
     - max_devices: 5 → 1
     - current_day_used_seconds: 7200 → 0

#### 🔍 斷言結果

1. ✅ 取消前計劃應該是 PREMIUM
   - 期望: PREMIUM
   - 實際: PREMIUM

2. ✅ 取消前應該有 3 個設備
   - 期望: 3
   - 實際: 3

3. ✅ 取消後計劃應該降級到 FREE
   - 期望: FREE
   - 實際: FREE

4. ✅ 取消後狀態應該是 canceled
   - 期望: canceled
   - 實際: canceled

5. ✅ 取消後配額應該降到 600s (10分鐘)
   - 期望: 600
   - 實際: 600

6. ✅ 取消後設備限制應該降到 1
   - 期望: 1
   - 實際: 1

7. ✅ 取消後使用量應該重置為 0
   - 期望: 0
   - 實際: 0

8. ✅ 取消後 Stripe 訂閱 ID 應該清空
   - 期望: None
   - 實際: None

---

### 4. ✅ 多次支付記錄追蹤

**描述**: 測試用戶多次支付的購買記錄正確創建和追蹤

**開始時間**: 09:40:42
**結束時間**: 09:40:44
**耗時**: 2.09 秒
**狀態**: passed

#### 🔔 Webhook 調用記錄

1. **customer.subscription.created** (09:40:42)
   - 描述: 用戶 multi_pay_test_1753666842 創建 PRO 計劃訂閱

2. **invoice.payment_succeeded** (09:40:42)
   - 描述: 用戶 multi_pay_test_1753666842 支付成功，金額 $19.99

3. **customer.subscription.updated** (09:40:42)
   - 描述: 用戶 multi_pay_test_1753666842 計劃變更: PRO → PREMIUM

4. **invoice.payment_succeeded** (09:40:42)
   - 描述: 用戶 multi_pay_test_1753666842 支付成功，金額 $59.99

5. **invoice.payment_succeeded** (09:40:44)
   - 描述: 用戶 multi_pay_test_1753666842 支付成功，金額 $59.99

#### 💾 數據庫變更記錄

1. **users** 表 - INSERT (09:40:42)
   - 用戶: multi_pay_test_1753666842

2. **subscriptions** 表 - INSERT (09:40:42)
   - 用戶: multi_pay_test_1753666842
   - 訂閱變更:
     - plan: N/A → FREE
     - status: N/A → active
     - daily_limit_seconds: N/A → 600
     - max_devices: N/A → 1
     - current_day_used_seconds: N/A → 0

3. **subscriptions** 表 - UPDATE (09:40:42)
   - 用戶: multi_pay_test_1753666842
   - 訂閱變更:
     - plan: FREE → PRO
     - daily_limit_seconds: 600 → 10800
     - max_devices: 1 → 2

4. **purchases** 表 - INSERT (09:40:42)
   - 用戶: multi_pay_test_1753666842
   - 新購買記錄:
     - 發票 ID: in_test_1753666840_multi_pay_test_1753666842
     - 金額: $19.99
     - 計劃: PRO
     - 狀態: succeeded

5. **subscriptions** 表 - UPDATE (09:40:42)
   - 用戶: multi_pay_test_1753666842
   - 訂閱變更:

6. **subscriptions** 表 - UPDATE (09:40:42)
   - 用戶: multi_pay_test_1753666842
   - 訂閱變更:
     - plan: PRO → PREMIUM
     - daily_limit_seconds: 10800 → 28800
     - max_devices: 2 → 5

7. **purchases** 表 - INSERT (09:40:43)
   - 用戶: multi_pay_test_1753666842
   - 新購買記錄:
     - 發票 ID: in_test_1753666840_multi_pay_test_1753666842
     - 金額: $59.99
     - 計劃: PREMIUM
     - 狀態: succeeded

8. **subscriptions** 表 - UPDATE (09:40:43)
   - 用戶: multi_pay_test_1753666842
   - 訂閱變更:

9. **purchases** 表 - INSERT (09:40:44)
   - 用戶: multi_pay_test_1753666842
   - 新購買記錄:
     - 發票 ID: in_test_1753666840_multi_pay_test_1753666842
     - 金額: $59.99
     - 計劃: PREMIUM
     - 狀態: succeeded

10. **subscriptions** 表 - UPDATE (09:40:44)
   - 用戶: multi_pay_test_1753666842
   - 訂閱變更:

#### 🔍 斷言結果

1. ✅ 應該有 3 條購買記錄
   - 期望: 3
   - 實際: 3

2. ✅ 應該有 1 條 PRO 計劃購買記錄
   - 期望: 1
   - 實際: 1

3. ✅ 應該有 2 條 PREMIUM 計劃購買記錄
   - 期望: 2
   - 實際: 2

4. ✅ 總支付金額應該是 $139.97
   - 期望: 13997
   - 實際: 13997

5. ✅ 所有購買記錄的用戶 ID 應該正確
   - 期望: True
   - 實際: True

6. ✅ 所有購買記錄的支付狀態應該是 succeeded
   - 期望: True
   - 實際: True

---

