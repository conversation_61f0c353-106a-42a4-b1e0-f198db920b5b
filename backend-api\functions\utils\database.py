"""
Database utilities for SpeechPilot Firebase Functions
"""

from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List, Tuple
from firebase_admin import firestore
from firebase_functions import https_fn
import logging

from .constants import SUBSCRIPTION_PLANS, ERROR_MESSAGES
from .error_handler import handle_error

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端"""
    return firestore.client()

def get_user_data(user_id: str) -> Dict[str, Any]:
    """
    獲取用戶資料
    
    Args:
        user_id: 用戶ID
        
    Returns:
        Dict[str, Any]: 用戶資料
        
    Raises:
        https_fn.HttpsError: 如果用戶不存在
    """
    try:
        db = get_db()
        user_doc = db.collection('users').document(user_id).get()
        if not user_doc.exists:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.NOT_FOUND,
                message=ERROR_MESSAGES["USER_NOT_FOUND"]
            )

        return user_doc.to_dict()
    except https_fn.HttpsError:
        raise
    except Exception as e:
        logger.error(f"Error getting user data for {user_id}: {str(e)}")
        raise https_fn.HttpsError(
            code=https_fn.FunctionsErrorCode.INTERNAL,
            message=ERROR_MESSAGES["INTERNAL_ERROR"]
        )



def check_device_exists(user_id: str, device_id: str) -> bool:
    """
    檢查設備是否存在且屬於用戶
    
    Args:
        user_id: 用戶ID
        device_id: 設備ID
        
    Returns:
        bool: 設備是否存在
    """
    try:
        db = get_db()
        # 使用簡單查詢避免複合索引需求
        device_query = db.collection('devices').where(
            'user_id', '==', user_id
        ).stream()

        # 在客戶端過濾
        for device_doc in device_query:
            device_data = device_doc.to_dict()
            if (device_data.get('device_id') == device_id and
                device_data.get('is_active', False)):
                return True

        return False

    except Exception as e:
        logger.error(f"Error checking device {device_id} for user {user_id}: {str(e)}")
        return False

def get_user_devices(user_id: str) -> List[Dict[str, Any]]:
    """
    獲取用戶的所有設備
    
    Args:
        user_id: 用戶ID
        
    Returns:
        List[Dict[str, Any]]: 設備列表
    """
    try:
        db = get_db()
        devices_query = db.collection('devices').where(
            'user_id', '==', user_id
        ).where(
            'is_active', '==', True
        ).stream()

        devices = []
        for doc in devices_query:
            device_data = doc.to_dict()
            device_data['id'] = doc.id
            devices.append(device_data)

        return devices

    except Exception as e:
        logger.error(f"Error getting devices for user {user_id}: {str(e)}")
        return []

def create_usage_log_and_update_counters(user_id: str, subscription_id: str, device_id: str, duration_seconds: int, feature_type: str = "ai-speech-to-text", platform: str = "windows", token_usage: Dict[str, int] = None, session_metadata: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Upsert usage_logs 記錄並更新訂閱計數器

    Args:
        user_id: 用戶ID
        subscription_id: 訂閱ID
        device_id: 設備ID
        duration_seconds: 使用時長（秒）
        feature_type: 功能類型 (ai-speech-to-text, direct-speech-to-text)
        platform: 平台
        token_usage: OpenAI token 使用量 {"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30}
        session_metadata: 會話元數據 {"audio_quality": "high", "language": "zh-TW", "file_size_mb": 15.2}

    Returns:
        Dict[str, Any]: 操作結果
    """
    try:
        from datetime import datetime, timezone

        db = get_db()
        now = datetime.now(timezone.utc)
        today = now.strftime('%Y-%m-%d')

        # 創建複合 document ID: user_device_feature_date
        usage_log_id = f"{user_id}_{device_id}_{feature_type}_{today}"
        usage_log_ref = db.collection('usage_logs').document(usage_log_id)

        # 檢查是否已存在記錄
        existing_doc = usage_log_ref.get()

        if existing_doc.exists:
            # 更新現有記錄 (累加)
            existing_data = existing_doc.to_dict()

            # 累加時長
            new_duration = existing_data.get('duration_seconds', 0) + duration_seconds

            # 累加 token 使用量 (頂層欄位)
            if token_usage:
                new_prompt_tokens = existing_data.get('prompt_tokens', 0) + token_usage.get('prompt_tokens', 0)
                new_completion_tokens = existing_data.get('completion_tokens', 0) + token_usage.get('completion_tokens', 0)
                new_input_tokens = existing_data.get('input_tokens', 0) + token_usage.get('input_tokens', 0)
                new_output_tokens = existing_data.get('output_tokens', 0) + token_usage.get('output_tokens', 0)
                # 計算 total_tokens：所有 token 類型的總和
                new_total_tokens = new_prompt_tokens + new_completion_tokens + new_input_tokens + new_output_tokens
            else:
                new_prompt_tokens = existing_data.get('prompt_tokens', 0)
                new_completion_tokens = existing_data.get('completion_tokens', 0)
                new_input_tokens = existing_data.get('input_tokens', 0)
                new_output_tokens = existing_data.get('output_tokens', 0)
                # 重新計算現有記錄的 total_tokens 以確保準確性
                new_total_tokens = new_prompt_tokens + new_completion_tokens + new_input_tokens + new_output_tokens

            # 更新記錄
            usage_log_ref.update({
                'duration_seconds': new_duration,
                'prompt_tokens': new_prompt_tokens,
                'completion_tokens': new_completion_tokens,
                'input_tokens': new_input_tokens,
                'output_tokens': new_output_tokens,
                'total_tokens': new_total_tokens,
                'session_count': firestore.Increment(1),
                'last_session_at': now,
                'updated_at': now
            })

            logger.info(f"Updated existing usage log: {usage_log_id}, total duration: {new_duration}s")

        else:
            # 創建新記錄
            prompt_tokens = token_usage.get('prompt_tokens', 0) if token_usage else 0
            completion_tokens = token_usage.get('completion_tokens', 0) if token_usage else 0
            input_tokens = token_usage.get('input_tokens', 0) if token_usage else 0
            output_tokens = token_usage.get('output_tokens', 0) if token_usage else 0
            # 計算 total_tokens：所有 token 類型的總和
            total_tokens = prompt_tokens + completion_tokens + input_tokens + output_tokens

            usage_log_data = {
                "user_id": user_id,
                "subscription_id": subscription_id,
                "device_id": device_id,
                "feature_type": feature_type,
                "platform": platform,
                "duration_seconds": duration_seconds,
                # Token 使用量 (頂層欄位)
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": total_tokens,
                "session_count": 1,
                "session_date": today,
                "first_session_at": now,
                "last_session_at": now,
                "created_at": now,
                "updated_at": now
            }

            # 添加會話元數據（如果提供）
            if session_metadata:
                usage_log_data["session_metadata"] = session_metadata

            usage_log_ref.set(usage_log_data)
            logger.info(f"Created new usage log: {usage_log_id}, duration: {duration_seconds}s")

        # 更新訂閱計數器
        subscription_ref = db.collection('subscriptions').document(subscription_id)

        # 添加調試日誌
        logger.info(f"Updating subscription {subscription_id} with duration: {duration_seconds}s")

        subscription_ref.update({
            'current_day_used_seconds': firestore.Increment(duration_seconds),
            'updated_at': now
        })

        logger.info(f"Successfully updated subscription {subscription_id}")

        # 驗證更新結果
        updated_subscription = subscription_ref.get()
        if updated_subscription.exists:
            updated_data = updated_subscription.to_dict()
            logger.info(f"Subscription after update: current_day_used_seconds = {updated_data.get('current_day_used_seconds', 'NOT_FOUND')}")
        else:
            logger.error(f"Subscription {subscription_id} not found after update!")

        # 計算返回的 token 數量（使用實際存儲的值）
        prompt_tokens = token_usage.get('prompt_tokens', 0) if token_usage else 0
        completion_tokens = token_usage.get('completion_tokens', 0) if token_usage else 0
        input_tokens = token_usage.get('input_tokens', 0) if token_usage else 0
        output_tokens = token_usage.get('output_tokens', 0) if token_usage else 0
        total_tokens = prompt_tokens + completion_tokens + input_tokens + output_tokens

        return {
            "success": True,
            "usage_log_id": usage_log_id,
            "duration_seconds": duration_seconds,
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "total_tokens": total_tokens
        }

    except Exception as e:
        raise handle_error(
            error=e,
            error_code="INTERNAL_ERROR",
            user_message="創建使用記錄時發生錯誤",
            context={"user_id": user_id, "subscription_id": subscription_id, "duration_seconds": duration_seconds}
        )

def check_subscription_usage_limits(subscription_id: str, duration_seconds: int) -> Dict[str, Any]:
    """
    檢查訂閱使用限制

    Args:
        subscription_id: 訂閱ID
        duration_seconds: 計劃使用的時長

    Returns:
        Dict[str, Any]: 檢查結果
    """
    try:
        db = get_db()
        subscription_doc = db.collection('subscriptions').document(subscription_id).get()

        if not subscription_doc.exists:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.NOT_FOUND,
                message="訂閱不存在"
            )

        subscription = subscription_doc.to_dict()

        # 只檢查每日限制
        daily_limit = subscription.get('daily_limit_seconds', 0)
        daily_used = subscription.get('daily_usage_seconds', 0)
        daily_remaining = daily_limit - daily_used if daily_limit != -1 else -1

        # 檢查是否可以使用（只檢查每日限制）
        can_use = daily_limit == -1 or daily_remaining >= duration_seconds

        return {
            "can_use": can_use,
            "daily_remaining": max(0, daily_remaining) if daily_remaining != -1 else -1,
            "daily_limit": daily_limit,
            "reason": None if can_use else "超過每日限制"
        }

    except Exception as e:
        logger.error(f"Error checking subscription usage limits: {str(e)}")
        raise

def update_user_usage(user_id: str, seconds_used: int) -> None:
    """
    更新用戶使用量統計
    
    Args:
        user_id: 用戶ID
        seconds_used: 使用秒數
    """
    try:
        db = get_db()
        user_ref = db.collection('users').document(user_id)

        # 使用事務確保數據一致性
        @firestore.transactional
        def update_in_transaction(transaction):
            user_doc = user_ref.get(transaction=transaction)
            if user_doc.exists:
                current_data = user_doc.to_dict()

                # 更新使用量
                new_daily_usage = current_data.get('daily_usage_seconds', 0) + seconds_used
                new_monthly_usage = current_data.get('monthly_usage_seconds', 0) + seconds_used

                transaction.update(user_ref, {
                    'daily_usage_seconds': new_daily_usage,
                    'monthly_usage_seconds': new_monthly_usage,
                    'updated_at': firestore.SERVER_TIMESTAMP
                })

        transaction = db.transaction()
        update_in_transaction(transaction)

        logger.info(f"Updated usage for user {user_id}: +{seconds_used}s")

    except Exception as e:
        logger.error(f"Error updating user usage for {user_id}: {str(e)}")
        # 不拋出異常，因為這不是關鍵操作

def check_daily_usage_limit(user_id: str, plan: str) -> Dict[str, Any]:
    """
    檢查每日使用限制
    
    Args:
        user_id: 用戶ID
        plan: 訂閱計劃
        
    Returns:
        Dict[str, Any]: 限制檢查結果
    """
    try:
        user_data = get_user_data(user_id)
        plan_config = SUBSCRIPTION_PLANS.get(plan, SUBSCRIPTION_PLANS["FREE"])
        
        daily_limit_seconds = plan_config["daily_limit_seconds"]
        
        # 檢查是否需要重置每日使用量
        now = datetime.now(timezone.utc)
        last_reset = user_data.get('last_usage_reset')
        
        if last_reset:
            if hasattr(last_reset, 'seconds'):
                last_reset_dt = datetime.fromtimestamp(last_reset.seconds, tz=timezone.utc)
            else:
                last_reset_dt = last_reset
            
            days_since_reset = (now - last_reset_dt).days
        else:
            days_since_reset = 1  # 強制重置
        
        daily_usage_seconds = user_data.get('daily_usage_seconds', 0)
        
        if days_since_reset >= 1:
            # 重置每日使用量
            daily_usage_seconds = 0
            db = get_db()
            user_ref = db.collection('users').document(user_id)
            user_ref.update({
                'daily_usage_seconds': 0,
                'last_usage_reset': firestore.SERVER_TIMESTAMP
            })
        
        remaining_seconds = daily_limit_seconds if daily_limit_seconds == -1 else max(0, daily_limit_seconds - daily_usage_seconds)
        can_use = daily_limit_seconds == -1 or daily_usage_seconds < daily_limit_seconds
        
        return {
            "can_use": can_use,
            "remaining_seconds": remaining_seconds,
            "daily_limit_seconds": daily_limit_seconds,
            "used_seconds": daily_usage_seconds
        }
        
    except Exception as e:
        logger.error(f"Error checking daily usage limit for {user_id}: {str(e)}")
        return {
            "can_use": False,
            "remaining_seconds": 0,
            "daily_limit_seconds": 0,
            "used_seconds": 0
        }
