# 🔄 SpeechPilot 資料庫架構優化 - 前端 API 變更通知

**版本**: V2.3  
**日期**: 2025-01-27  
**影響範圍**: 配額管理、設備管理、時區處理  

---

## 📋 **變更概覽**

我們已完成資料庫架構的重大優化，解決了以下關鍵問題：

1. **配額資料冗餘** - 移除用戶表中的冗餘配額欄位
2. **設備管理增強** - 新增設備狀態追蹤和操作記錄
3. **時區處理標準化** - 統一日期時間處理邏輯
4. **用戶訂閱關聯強化** - 確保雙向關聯一致性

---

## 🚨 **重要變更**

### 1. **用戶表 (users) 結構變更**

#### ❌ **已移除的欄位**
```javascript
// 這些欄位已從用戶表移除，現在由訂閱表統一管理
{
  "daily_limit_seconds": 10800,        // 已移除
  "current_day_used_seconds": 3600,    // 已移除
  "device_limit": 2                    // 已移除
}
```

#### ✅ **保留的欄位**
```javascript
{
  "current_subscription_id": "sub_xxx", // 訂閱關聯
  "device_ids": ["device_1", "device_2"], // 設備ID列表
  "last_device_check": "2025-01-27T10:00:00Z" // 最後設備檢查時間
}
```

### 2. **訂閱表 (subscriptions) 新增欄位**

#### ✅ **新增的時區管理欄位**
```javascript
{
  // 地區化配額重置
  "register_region": "Hong Kong",
  "register_region_timezone": "UTC+8",
  "usage_reset_hour": 0,
  
  // 統一的配額欄位名稱
  "current_day_used_seconds": 3600,    // 替代 daily_usage_seconds
  "last_daily_reset_date": "2025-01-27", // 本地日期
  "last_daily_reset_at": "2025-01-27T16:00:00Z" // UTC 時間戳
}
```

### 3. **設備表 (devices) 增強**

#### ✅ **新增的狀態管理欄位**
```javascript
{
  "status": "active",           // active, inactive, removed, unlinked
  "is_authorized": true,        // 設備授權狀態
  "device_fingerprint": "...",  // 設備指紋
  "user_agent": "...",         // 用戶代理
  
  // 操作時間記錄
  "removed_at": null,          // 移除時間
  "unlinked_at": null,         // 解綁時間
  "removal_reason": null       // 移除原因
}
```

---

## 🔧 **API 變更詳情**

### 1. **配額檢查 API**

#### 📍 **端點**: `check_user_subscription_status`

**變更前**:
```javascript
// 回應包含用戶表的冗餘配額資訊
{
  "data": {
    "daily_limit_seconds": 10800,
    "current_day_used_seconds": 3600
  }
}
```

**變更後**:
```javascript
// 配額資訊統一從訂閱表獲取
{
  "data": {
    "available_seconds_today": 7200,
    "total_used_today": 3600,
    "can_use": true
  }
}
```

### 2. **設備註冊 API**

#### 📍 **端點**: `register_device_v2`

**新增回應欄位**:
```javascript
{
  "data": {
    "device_id": "device_123",
    "status": "active",        // 新增：設備狀態
    "is_authorized": true,     // 新增：授權狀態
    "platform_supported": true
  }
}
```

### 3. **用戶創建 API**

#### 📍 **端點**: `create_or_update_user`

**新增請求參數**:
```javascript
{
  "register_region": "Hong Kong",     // 可選：註冊地區
  "register_region_timezone": "UTC+8" // 可選：時區
}
```

---

## 📱 **前端適配指南**

### 1. **配額顯示邏輯**

```javascript
// ❌ 舊的方式 - 不要再使用
const dailyLimit = userData.daily_limit_seconds;
const dailyUsed = userData.current_day_used_seconds;

// ✅ 新的方式 - 從訂閱狀態 API 獲取
const subscriptionStatus = await checkUserSubscriptionStatus();
const availableToday = subscriptionStatus.data.available_seconds_today;
const totalUsedToday = subscriptionStatus.data.total_used_today;
```

### 2. **設備狀態檢查**

```javascript
// ✅ 新增設備狀態檢查
const deviceInfo = await validateDevice(deviceId);
if (deviceInfo.data.device.status !== 'active') {
  // 處理設備非活躍狀態
  handleInactiveDevice(deviceInfo.data.device.status);
}
```

### 3. **時區處理**

```javascript
// ✅ 用戶註冊時提供地區資訊
const userData = {
  email: user.email,
  register_region: "Hong Kong",
  register_region_timezone: "UTC+8"
};

await createOrUpdateUser(userData);
```

---

## 🧪 **測試建議**

### 1. **配額測試**
- 驗證配額顯示是否正確
- 測試跨日配額重置功能
- 確認超額使用的處理邏輯

### 2. **設備管理測試**
- 測試設備註冊流程
- 驗證設備狀態變更
- 確認設備數量限制

### 3. **時區測試**
- 測試不同時區用戶的配額重置
- 驗證本地時間顯示
- 確認跨時區使用記錄

---

## 🔄 **遷移時程**

### 階段 1: 準備階段 (已完成)
- ✅ 新架構開發完成
- ✅ 向後兼容性確保
- ✅ 基本功能部署

### 階段 2: 測試階段 (進行中)
- 🔄 API 兼容性測試
- 🔄 前端適配驗證
- 🔄 資料一致性檢查

### 階段 3: 正式遷移 (待定)
- ⏳ 資料庫遷移腳本執行
- ⏳ 冗餘欄位清理
- ⏳ 監控和驗證

---

## 📞 **支援聯絡**

如有任何問題或需要協助，請聯絡：

- **技術支援**: 後端開發團隊
- **API 文檔**: 參考更新的 DATA_DICTIONARY.md
- **測試環境**: speakoneai-dev-9f995

---

## 📝 **檢查清單**

前端團隊請確認以下項目：

- [ ] 移除對用戶表配額欄位的直接引用
- [ ] 更新配額顯示邏輯使用訂閱狀態 API
- [ ] 適配新的設備狀態欄位
- [ ] 測試時區相關功能
- [ ] 驗證 API 回應格式變更
- [ ] 更新錯誤處理邏輯

---

**重要提醒**: 舊的 API 仍然可用，但建議盡快適配新的架構以獲得更好的性能和一致性。
