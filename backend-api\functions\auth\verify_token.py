"""
Token verification functionality for SpeechPilot Firebase Functions
"""

import logging
from firebase_functions import https_fn
from firebase_admin import firestore
from typing import Dict, Any

from ..utils.validation import validate_auth
from ..utils.database import get_user_data
from ..utils.constants import ERROR_MESSAGES
from ..utils.error_handler import handle_error, handle_validation_error, log_function_start, log_function_success

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端"""
    return firestore.client()

def verify_token_handler(request: https_fn.CallableRequest) -> Dict[str, Any]:
    """
    驗證 API Token
    
    Args:
        request: Firebase Functions 請求對象
        
    Returns:
        Dict[str, Any]: 驗證結果
    """
    try:
        # 驗證用戶認證
        user_id = validate_auth(request)
        
        logger.info(f"Verifying token for user: {user_id}")
        
        # 獲取用戶資料
        user_data = get_user_data(user_id)
        
        # 檢查用戶狀態
        if user_data.get('is_banned', False):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.PERMISSION_DENIED,
                message="用戶已被封禁"
            )
        
        if not user_data.get('is_active', True):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.PERMISSION_DENIED,
                message="用戶帳戶未啟用"
            )
        
        # 更新最後登入時間
        user_ref = db.collection('users').document(user_id)
        user_ref.update({
            'last_login_at': firestore.SERVER_TIMESTAMP,
            'updated_at': firestore.SERVER_TIMESTAMP
        })
        
        # 獲取用戶訂閱計劃
        subscription = user_data.get('subscription', {})
        plan = subscription.get('plan', 'FREE')
        
        logger.info(f"Token verified successfully for user {user_id}, plan: {plan}")
        
        return {
            "valid": True,
            "user_id": user_id,
            "plan": plan
        }
        
    except https_fn.HttpsError:
        # 重新拋出 Firebase Functions 錯誤
        raise
    except Exception as e:
        raise handle_error(
            error=e,
            error_code="INTERNAL_ERROR",
            user_message="驗證令牌時發生錯誤",
            context={"user_id": user_id, "function": "verify_token"}
        )
