"""
創建或更新用戶記錄
處理新用戶註冊和 Free 計劃初始化
"""

import logging
from datetime import datetime
from typing import Dict, Any
from firebase_functions import https_fn
from firebase_admin import firestore
from ..utils.constants import SUBSCRIPTION_PLANS
from ..utils.validation import normalize_platform
# from ..utils.user_subscription_sync import user_subscription_sync
# from ..utils.timezone_manager import timezone_manager
# 移除對已刪除函數的依賴

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端（環境感知）"""
    from ..utils.environment import get_db as get_env_db
    return get_env_db()

def check_subscription_exists(user_id: str) -> bool:
    """檢查用戶是否已有訂閱記錄"""
    try:
        db = get_db()
        subscription_doc = db.collection('subscriptions').document(f"sub_{user_id}").get()
        return subscription_doc.exists
    except Exception as e:
        logger.error(f"檢查訂閱存在性失敗: {str(e)}")
        return False

def create_free_subscription(user_id: str, source_app: str = 'desktop') -> dict:
    """為新用戶創建 FREE 訂閱"""
    try:
        from datetime import datetime

        db = get_db()
        now = datetime.utcnow()
        subscription_id = f"sub_{user_id}"

        # FREE 計劃配置
        free_plan = SUBSCRIPTION_PLANS.get('FREE', {
            'daily_limit_seconds': 600,
            'max_devices': 1,
            'supported_platforms': ['ios', 'android'],
            'features': ['basic_speech_to_text']
        })

        subscription_data = {
            'user_id': user_id,
            'plan': 'FREE',
            'status': 'active',
            'daily_limit_seconds': free_plan['daily_limit_seconds'],
            'max_devices': free_plan['max_devices'],
            'current_day_used_seconds': 0,
            'supported_platforms': free_plan['supported_platforms'],
            'features': free_plan['features'],
            'register_region_timezone': 'UTC+8',
            'usage_reset_hour': 0,
            'last_daily_reset_date': now.strftime('%Y-%m-%d'),
            'last_daily_reset_at': now,
            'active_device_ids': [],
            'last_device_limit_check': now,
            'created_at': now,
            'updated_at': now,
            'source_app': source_app,
            'stripe_customer_id': None,
            'stripe_subscription_id': None,
            'auto_renew': False
        }

        # 創建訂閱記錄
        subscription_ref = db.collection('subscriptions').document(subscription_id)
        subscription_ref.set(subscription_data)

        logger.info(f"✅ FREE 訂閱創建成功: {user_id}")
        return subscription_data

    except Exception as e:
        logger.error(f"❌ 創建 FREE 訂閱失敗: {str(e)}")
        raise e

def create_or_update_user_handler(req: https_fn.CallableRequest) -> Dict[str, Any]:
    """
    創建或更新用戶記錄
    
    Args:
        req: Firebase Functions 請求對象
        
    Returns:
        Dict[str, Any]: 用戶資料和訂閱資訊
    """
    try:
        # 驗證用戶認證
        if not req.auth:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.UNAUTHENTICATED,
                message='用戶未認證'
            )

        data = req.data
        
        # 驗證請求數據
        if not data or not data.get('uid') or not data.get('email'):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message='缺少必要的用戶資訊'
            )

        # 確保只能操作自己的資料
        if req.auth.uid != data['uid']:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.PERMISSION_DENIED,
                message='無權限操作其他用戶資料'
            )

        db = get_db()
        now = datetime.utcnow()
        user_id = data['uid']
        
        user_ref = db.collection('users').document(user_id)

        # 檢查用戶是否已存在（更安全的檢測方式）
        user_doc = user_ref.get()
        subscription_exists = check_subscription_exists(user_id)

        # 新用戶定義：用戶記錄不存在 AND 訂閱記錄不存在
        is_new_user = not user_doc.exists and not subscription_exists

        if is_new_user:
            # 為新用戶創建 Free 訂閱（先創建訂閱以獲取限制資訊）
            subscription_data = create_free_subscription(
                user_id,
                source_app=data.get('source_app', 'desktop')
            )

            # 新用戶 - 創建用戶記錄（移除冗餘配額欄位）
            subscription_id = f"sub_{user_id}"
            new_user = {
                'uid': user_id,
                'email': data['email'],
                'display_name': data.get('name', ''),  # 統一使用 display_name
                'profile_image': data.get('image', ''),  # 統一使用 profile_image
                'auth_provider': data.get('auth_provider', 'google'),
                'email_verified': data.get('email_verified', False),
                'is_active': True,
                'is_banned': False,
                'created_at': now,
                'updated_at': now,
                'last_login_at': now,
                'sign_up_platform': normalize_platform(data.get('platform', 'windows')),
                'app_version': data.get('app_version'),

                # 關聯欄位
                'current_subscription_id': subscription_id,

                # 設備管理（移除冗餘，由訂閱表管理）
                'device_ids': [],  # 初始為空

                # 業務流程狀態追蹤
                'onboarding_completed': False,
                'first_device_registered': False,
                'last_device_check': now,

                # 用戶偏好
                'preferences': {
                    'language': 'zh-TW',
                    'notifications': True,
                    'auto_upgrade_prompts': True
                }
            }

            user_ref.set(new_user)
            logger.info(f"✅ 新用戶創建成功: {data['email']}")
            
        else:
            # 現有用戶 - 更新資料
            existing_data = user_doc.to_dict()
            update_data = {
                'display_name': data.get('name', existing_data.get('display_name', existing_data.get('name', ''))),
                'profile_image': data.get('image', existing_data.get('profile_image', existing_data.get('image', ''))),
                'email_verified': data.get('email_verified', existing_data.get('email_verified', False)),
                'updated_at': now,
                'last_login_at': now,
                'platform': normalize_platform(data.get('platform', 'windows')),
                'app_version': data.get('app_version')
            }

            # 確保現有用戶有訂閱關聯
            if not existing_data.get('current_subscription_id'):
                subscription_id = f"sub_{user_id}"
                update_data['current_subscription_id'] = subscription_id

                # 如果沒有訂閱，創建一個
                subscription_ref = db.collection('subscriptions').document(subscription_id)
                if not subscription_ref.get().exists:
                    subscription_data = create_free_subscription(
                        user_id,
                        source_app=data.get('source_app', 'desktop')
                    )

                    # 為現有用戶添加新架構的欄位
                    update_data.update({
                        'device_ids': existing_data.get('device_ids', []),
                        'device_limit': subscription_data.get('max_devices', 1),
                        'current_period_used_seconds': existing_data.get('current_period_used_seconds', 0),
                        'currentPeriodStart': subscription_data.get('currentPeriodStart', now),
                        'currentPeriodEnd': subscription_data.get('currentPeriodEnd', now)
                    })

            # 確保現有用戶有新架構的欄位（向後兼容）
            if 'device_ids' not in existing_data:
                update_data['device_ids'] = []
            if 'device_limit' not in existing_data:
                # 從訂閱獲取設備限制
                subscription_ref = db.collection('subscriptions').document(f"sub_{user_id}")
                subscription_doc = subscription_ref.get()
                if subscription_doc.exists:
                    subscription_data = subscription_doc.to_dict()
                    update_data['device_limit'] = subscription_data.get('max_devices', 1)
                    if 'current_period_used_seconds' not in existing_data:
                        update_data['current_period_used_seconds'] = 0
                    if 'currentPeriodStart' not in existing_data:
                        update_data['currentPeriodStart'] = subscription_data.get('currentPeriodStart', now)
                    if 'currentPeriodEnd' not in existing_data:
                        update_data['currentPeriodEnd'] = subscription_data.get('currentPeriodEnd', now)

            user_ref.update(update_data)
            logger.info(f"✅ 用戶資料更新成功: {data['email']}")

        # 獲取最新的用戶和訂閱資料
        updated_user_doc = user_ref.get()
        subscription_ref = db.collection('subscriptions').document(f"sub_{user_id}")
        subscription_doc = subscription_ref.get()

        user_data = updated_user_doc.to_dict()
        subscription_data = subscription_doc.to_dict() if subscription_doc.exists else None

        response = {
            'success': True,
            'data': {
                'user': {
                    'id': user_data['uid'],
                    'email': user_data['email'],
                    'display_name': user_data.get('display_name', user_data.get('name', '')),
                    'current_subscription_id': user_data.get('current_subscription_id'),
                    'onboarding_completed': user_data.get('onboarding_completed', False),
                    'is_active': user_data['is_active'],
                    'is_new_user': is_new_user,
                    'device_ids': user_data.get('device_ids', []),
                    'device_limit': user_data.get('device_limit', 1),
                    'current_period_used_seconds': user_data.get('current_period_used_seconds', 0)
                },
                'subscription': {
                    'plan': subscription_data.get('plan', 'FREE'),
                    'status': subscription_data.get('status', 'active'),
                    'daily_limit_seconds': subscription_data.get('daily_limit_seconds', 600),
                    'max_devices': subscription_data.get('max_devices', 1),
                    'current_period_usage_seconds': subscription_data.get('current_period_usage_seconds', 0),
                    'daily_usage_seconds': subscription_data.get('daily_usage_seconds', 0),
                    'currentPeriodStart': subscription_data.get('currentPeriodStart'),
                    'currentPeriodEnd': subscription_data.get('currentPeriodEnd')
                } if subscription_data else None
            },
            'message': '新用戶創建成功' if is_new_user else '用戶資料更新成功'
        }

        return response

    except https_fn.HttpsError:
        raise
    except Exception as e:
        logger.error(f"❌ create_or_update_user 錯誤: {str(e)}")
        from ..utils.error_handler import handle_error
        raise handle_error(
            error=e,
            error_code="INTERNAL_ERROR",
            user_message="創建或更新用戶時發生錯誤",
            context={"user_id": data.get('uid'), "function": "create_or_update_user"}
        )

def create_free_subscription(user_id: str, created_at: datetime) -> None:
    """
    為新用戶創建 Free 訂閱
    
    Args:
        user_id: 用戶ID
        created_at: 創建時間
    """
    try:
        db = get_db()
        free_plan = SUBSCRIPTION_PLANS['FREE']
        
        subscription = {
            'user_id': user_id,
            'plan': 'FREE',
            'status': 'active',
            'daily_limit_seconds': free_plan['daily_limit_seconds'],
            'monthly_limit_seconds': free_plan['monthly_limit_seconds'],
            'max_devices': free_plan['max_devices'],
            'supported_platforms': free_plan['supported_platforms'],
            'created_at': created_at,
            'updated_at': created_at
        }

        db.collection('subscriptions').document(user_id).set(subscription)
        logger.info(f"✅ Free 訂閱創建成功: {user_id}")
        
    except Exception as e:
        logger.error(f"❌ 創建 Free 訂閱失敗: {str(e)}")
        raise e
