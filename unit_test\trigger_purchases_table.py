"""
觸發 purchases 表創建的實際測試
"""

import requests
import json
import time
from datetime import datetime

def trigger_purchases_table_creation():
    """觸發 purchases 表創建"""
    
    timestamp = str(int(time.time()))
    test_user_id = f"test_user_{timestamp}"
    
    # Firebase Functions URL
    api_url = "https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/create_subscription"
    
    # 測試數據
    test_data = {
        # 計劃資訊
        "plan": "PRO",
        "daily_limit_seconds": 10800,
        "max_devices": 2,
        "price_amount": 1999,
        "currency": "usd",
        "billing_cycle": "monthly",
        
        # Stripe 支付資訊
        "stripe_invoice_id": f"in_test_{timestamp}",
        "stripe_customer_id": f"cus_test_{timestamp}",
        "stripe_subscription_id": f"sub_test_{timestamp}",
        "stripe_payment_intent_id": f"pi_test_{timestamp}",
        "amount_total": 1999,
        "amount_paid": 1999,
        "payment_status": "succeeded",
        "invoice_created": datetime.utcnow().isoformat(),
        "period_start": datetime.utcnow().isoformat(),
        "period_end": "2025-02-27T00:00:00Z",
        
        # 客戶資訊
        "customer_email": f"test_{timestamp}@example.com",
        "customer_name": "Test User",
        "source_app": "web"
    }
    
    print("🚀 觸發 purchases 表創建...")
    print(f"API URL: {api_url}")
    print(f"測試用戶: {test_user_id}")
    print(f"發票 ID: {test_data['stripe_invoice_id']}")
    print(f"金額: ${test_data['amount_total']/100:.2f}")
    
    try:
        # 需要 Firebase Auth token，這裡只是示例
        headers = {
            'Content-Type': 'application/json',
            # 'Authorization': f'Bearer {firebase_token}'  # 需要實際的 token
        }
        
        print("\n⚠️  注意: 此測試需要有效的 Firebase Auth token")
        print("建議通過 Web 應用或實際的 Stripe webhook 來觸發")
        
        # 顯示請求數據
        print(f"\n📋 請求數據:")
        print(json.dumps(test_data, indent=2, ensure_ascii=False))
        
        print(f"\n💡 手動測試步驟:")
        print(f"1. 在 Web 應用中創建一個 PRO 訂閱")
        print(f"2. 確保 Stripe webhook 調用 create_subscription")
        print(f"3. 檢查 Firestore 中的 purchases 集合")
        print(f"4. 查找發票 ID: {test_data['stripe_invoice_id']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 錯誤: {str(e)}")
        return False

def trigger_usage_logs_creation():
    """觸發 usage_logs 表創建"""
    
    timestamp = str(int(time.time()))
    test_user_id = f"test_user_{timestamp}"
    test_device_id = f"device_{timestamp}"
    
    # Firebase Functions URL
    api_url = "https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/submit_usage"
    
    # 測試數據 - Chat Completion
    chat_completion_data = {
        "duration_seconds": 300,
        "feature_type": "ai-speech-to-text",
        "device_id": test_device_id,
        "platform": "windows",
        "token_usage": {
            "prompt_tokens": 10,
            "completion_tokens": 20,
            "total_tokens": 30
        }
    }
    
    # 測試數據 - Realtime API
    realtime_data = {
        "duration_seconds": 200,
        "feature_type": "direct-speech-to-text",
        "device_id": test_device_id,
        "platform": "windows",
        "token_usage": {
            "input_tokens": 15,
            "output_tokens": 25,
            "total_tokens": 40
        }
    }
    
    print("\n🎙️ 觸發 usage_logs 表創建...")
    print(f"API URL: {api_url}")
    print(f"測試用戶: {test_user_id}")
    print(f"測試設備: {test_device_id}")
    
    print(f"\n📋 Chat Completion 數據:")
    print(json.dumps(chat_completion_data, indent=2, ensure_ascii=False))
    
    print(f"\n📋 Realtime API 數據:")
    print(json.dumps(realtime_data, indent=2, ensure_ascii=False))
    
    print(f"\n💡 預期的 usage_logs 記錄 ID:")
    today = datetime.utcnow().strftime('%Y-%m-%d')
    chat_log_id = f"{test_user_id}_{test_device_id}_ai-speech-to-text_{today}"
    realtime_log_id = f"{test_user_id}_{test_device_id}_direct-speech-to-text_{today}"
    
    print(f"   Chat Completion: {chat_log_id}")
    print(f"   Realtime API: {realtime_log_id}")
    
    print(f"\n💡 手動測試步驟:")
    print(f"1. 在客戶端應用中調用 submit_usage API")
    print(f"2. 包含 OpenAI token_usage 資訊")
    print(f"3. 檢查 Firestore 中的 usage_logs 集合")
    print(f"4. 驗證 upsert 行為（相同用戶+設備+功能會累加）")
    
    return True

if __name__ == '__main__':
    print("🧪 觸發 Firestore 表創建測試")
    print("=" * 50)
    
    # 觸發 purchases 表創建
    trigger_purchases_table_creation()
    
    # 觸發 usage_logs 表創建
    trigger_usage_logs_creation()
    
    print("\n" + "=" * 50)
    print("✅ 觸發腳本執行完成")
    print("\n📊 檢查 Firestore 控制台:")
    print("   https://console.firebase.google.com/project/speakoneai-dev-9f995/firestore")
    print("\n🔍 查找以下集合:")
    print("   - purchases (付費訂閱創建後)")
    print("   - usage_logs (使用量提交後)")
    print("\n💡 提示:")
    print("   - purchases 表需要實際的付費訂閱才會出現")
    print("   - usage_logs 表需要實際的使用量提交才會出現")
    print("   - 建議通過 Web 應用或客戶端應用進行實際測試")
