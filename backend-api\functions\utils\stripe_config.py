"""
Stripe 配置管理
包含 Price ID 映射和相關配置
"""

import os
from typing import Dict, Optional

# Stripe Price ID 映射配置
# 請根據你的實際 Stripe Price ID 更新這些值
STRIPE_PRICE_MAPPING: Dict[str, str] = {
    # 開發環境 Price IDs (測試模式)
    'dev': {
        # 月費 Price IDs
        'price_1234567890_starter_monthly': 'STARTER',
        'price_1234567890_pro_monthly': 'PRO',
        'price_1234567890_premium_monthly': 'PREMIUM',
        'price_1234567890_max_monthly': 'MAX',

        # 年費 Price IDs
        'price_1234567890_starter_yearly': 'STARTER',
        'price_1234567890_pro_yearly': 'PRO',
        'price_1234567890_premium_yearly': 'PREMIUM',
        'price_1234567890_max_yearly': 'MAX',

        # 測試用 Price IDs
        'price_test_starter_monthly': 'STARTER',
        'price_test_pro_monthly': 'PRO',
        'price_test_premium_monthly': 'PREMIUM',
        'price_test_max_monthly': 'MAX',
        'price_test_starter_yearly': 'STARTER',
        'price_test_pro_yearly': 'PRO',
        'price_test_premium_yearly': 'PREMIUM',
        'price_test_max_yearly': 'MAX',
    },
    
    # 生產環境 Price IDs (正式模式)
    'prod': {
        # 月費 Price IDs
        'price_live_starter_monthly': 'STARTER',
        'price_live_pro_monthly': 'PRO',
        'price_live_premium_monthly': 'PREMIUM',
        'price_live_max_monthly': 'MAX',
        
        # 年費 Price IDs
        'price_live_starter_yearly': 'STARTER',
        'price_live_pro_yearly': 'PRO',
        'price_live_premium_yearly': 'PREMIUM',
        'price_live_max_yearly': 'MAX',
    }
}

def get_environment() -> str:
    """
    獲取當前環境
    
    Returns:
        str: 'dev' 或 'prod'
    """
    # 檢查環境變數
    env = os.getenv('ENVIRONMENT', 'dev').lower()
    
    # 也可以根據 Stripe 密鑰判斷
    stripe_key = os.getenv('STRIPE_SECRET_KEY', '')
    if stripe_key.startswith('sk_live_'):
        return 'prod'
    elif stripe_key.startswith('sk_test_'):
        return 'dev'
    
    return env

def get_plan_from_stripe_price_id(price_id: str) -> Optional[str]:
    """
    根據 Stripe Price ID 獲取計劃名稱
    
    Args:
        price_id: Stripe Price ID
        
    Returns:
        str: 計劃名稱 (STARTER, PRO, PREMIUM, MAX) 或 None
    """
    env = get_environment()
    price_mapping = STRIPE_PRICE_MAPPING.get(env, {})
    
    return price_mapping.get(price_id)

def get_stripe_price_id_for_plan(plan: str, billing_cycle: str = 'monthly') -> Optional[str]:
    """
    根據計劃名稱和計費週期獲取 Stripe Price ID
    
    Args:
        plan: 計劃名稱 (STARTER, PRO, PREMIUM, MAX)
        billing_cycle: 計費週期 ('monthly' 或 'yearly')
        
    Returns:
        str: Stripe Price ID 或 None
    """
    env = get_environment()
    price_mapping = STRIPE_PRICE_MAPPING.get(env, {})
    
    # 查找匹配的 Price ID
    suffix = f"_{plan.lower()}_{billing_cycle}"
    
    for price_id, mapped_plan in price_mapping.items():
        if mapped_plan == plan and price_id.endswith(suffix):
            return price_id
    
    return None

def validate_stripe_configuration() -> Dict[str, any]:
    """
    驗證 Stripe 配置是否完整
    
    Returns:
        Dict[str, any]: 驗證結果
    """
    env = get_environment()
    
    # 檢查必要的環境變數
    required_env_vars = [
        'STRIPE_SECRET_KEY',
        'STRIPE_WEBHOOK_SECRET'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    # 檢查 Price ID 映射
    price_mapping = STRIPE_PRICE_MAPPING.get(env, {})
    expected_plans = ['STARTER', 'PRO', 'PREMIUM', 'MAX']
    expected_cycles = ['monthly', 'yearly']
    
    missing_mappings = []
    for plan in expected_plans:
        for cycle in expected_cycles:
            found = False
            for price_id, mapped_plan in price_mapping.items():
                if mapped_plan == plan and cycle in price_id:
                    found = True
                    break
            
            if not found:
                missing_mappings.append(f"{plan}_{cycle}")
    
    return {
        'environment': env,
        'stripe_key_configured': bool(os.getenv('STRIPE_SECRET_KEY')),
        'webhook_secret_configured': bool(os.getenv('STRIPE_WEBHOOK_SECRET')),
        'missing_env_vars': missing_vars,
        'price_mappings_count': len(price_mapping),
        'missing_price_mappings': missing_mappings,
        'is_valid': len(missing_vars) == 0 and len(missing_mappings) == 0
    }

# Stripe Webhook 事件配置
SUPPORTED_WEBHOOK_EVENTS = [
    'customer.subscription.created',
    'customer.subscription.updated', 
    'customer.subscription.deleted',
    'invoice.payment_succeeded',
    'invoice.payment_failed'
]

# Webhook 端點配置
WEBHOOK_ENDPOINTS = {
    'dev': 'https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/backend-api/stripe_webhook',
    'prod': 'https://asia-east1-speakoneai-prod.cloudfunctions.net/backend-api/stripe_webhook'
}

def get_webhook_endpoint() -> str:
    """
    獲取當前環境的 Webhook 端點 URL
    
    Returns:
        str: Webhook 端點 URL
    """
    env = get_environment()
    return WEBHOOK_ENDPOINTS.get(env, WEBHOOK_ENDPOINTS['dev'])

def get_stripe_configuration_summary() -> Dict[str, any]:
    """
    獲取 Stripe 配置摘要
    
    Returns:
        Dict[str, any]: 配置摘要
    """
    validation = validate_stripe_configuration()
    
    return {
        'environment': validation['environment'],
        'webhook_endpoint': get_webhook_endpoint(),
        'supported_events': SUPPORTED_WEBHOOK_EVENTS,
        'configuration_valid': validation['is_valid'],
        'validation_details': validation
    }
