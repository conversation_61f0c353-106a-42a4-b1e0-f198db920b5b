"""
測試 usage_logs 聚合邏輯和頂層 token 欄位
"""

import unittest
import sys
import os
import time
from datetime import datetime

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend-api'))

class TestUsageLogsAggregation(unittest.TestCase):
    """測試 usage_logs 聚合邏輯"""
    
    def setUp(self):
        """測試設置"""
        self.timestamp = str(int(time.time()))
        self.test_user_id = f"test_user_{self.timestamp}"
        self.test_device_id = f"device_{self.timestamp}"
        
    def test_token_aggregation_logic(self):
        """測試 token 聚合邏輯"""
        print("\n🔢 測試 token 聚合邏輯")
        print("=" * 40)
        
        try:
            # 第一次提交 - Chat Completion
            first_submission = {
                "duration_seconds": 300,
                "token_usage": {
                    "prompt_tokens": 10,
                    "completion_tokens": 20,
                    "total_tokens": 30
                }
            }
            
            # 第二次提交 - Realtime API
            second_submission = {
                "duration_seconds": 200,
                "token_usage": {
                    "input_tokens": 15,
                    "output_tokens": 25,
                    "total_tokens": 40
                }
            }
            
            # 第三次提交 - 混合
            third_submission = {
                "duration_seconds": 150,
                "token_usage": {
                    "prompt_tokens": 5,
                    "completion_tokens": 10,
                    "input_tokens": 8,
                    "output_tokens": 12,
                    "total_tokens": 35
                }
            }
            
            # 模擬聚合邏輯
            aggregated_result = {
                "duration_seconds": 300 + 200 + 150,  # 650
                "prompt_tokens": 10 + 0 + 5,          # 15
                "completion_tokens": 20 + 0 + 10,     # 30
                "input_tokens": 0 + 15 + 8,           # 23
                "output_tokens": 0 + 25 + 12,         # 37
                "total_tokens": 30 + 40 + 35,         # 105
                "session_count": 3
            }
            
            # 驗證聚合結果
            self.assertEqual(aggregated_result["duration_seconds"], 650)
            self.assertEqual(aggregated_result["prompt_tokens"], 15)
            self.assertEqual(aggregated_result["completion_tokens"], 30)
            self.assertEqual(aggregated_result["input_tokens"], 23)
            self.assertEqual(aggregated_result["output_tokens"], 37)
            self.assertEqual(aggregated_result["total_tokens"], 105)
            self.assertEqual(aggregated_result["session_count"], 3)
            
            print("✅ token 聚合邏輯正確")
            print(f"   總時長: {aggregated_result['duration_seconds']} 秒")
            print(f"   Prompt tokens: {aggregated_result['prompt_tokens']}")
            print(f"   Completion tokens: {aggregated_result['completion_tokens']}")
            print(f"   Input tokens: {aggregated_result['input_tokens']}")
            print(f"   Output tokens: {aggregated_result['output_tokens']}")
            print(f"   Total tokens: {aggregated_result['total_tokens']}")
            print(f"   會話數: {aggregated_result['session_count']}")
            
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")
            self.fail(f"token 聚合邏輯測試失敗: {str(e)}")
    
    def test_usage_logs_document_id_format(self):
        """測試 usage_logs 文檔 ID 格式"""
        print("\n📋 測試文檔 ID 格式")
        print("=" * 40)
        
        try:
            today = datetime.utcnow().strftime('%Y-%m-%d')
            
            # AI Mode 文檔 ID
            ai_mode_id = f"{self.test_user_id}_{self.test_device_id}_ai-speech-to-text_{today}"
            
            # Direct Mode 文檔 ID
            direct_mode_id = f"{self.test_user_id}_{self.test_device_id}_direct-speech-to-text_{today}"
            
            # 驗證格式
            self.assertIn(self.test_user_id, ai_mode_id)
            self.assertIn(self.test_device_id, ai_mode_id)
            self.assertIn("ai-speech-to-text", ai_mode_id)
            self.assertIn(today, ai_mode_id)
            
            self.assertIn("direct-speech-to-text", direct_mode_id)
            
            # 驗證不同功能類型會產生不同文檔
            self.assertNotEqual(ai_mode_id, direct_mode_id)
            
            print("✅ 文檔 ID 格式正確")
            print(f"   AI Mode: {ai_mode_id}")
            print(f"   Direct Mode: {direct_mode_id}")
            print(f"   日期: {today}")
            
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")
            self.fail(f"文檔 ID 格式測試失敗: {str(e)}")
    
    def test_top_level_token_fields(self):
        """測試頂層 token 欄位結構"""
        print("\n📊 測試頂層 token 欄位")
        print("=" * 40)
        
        try:
            # 模擬 usage_logs 記錄結構
            usage_log_record = {
                "user_id": self.test_user_id,
                "device_id": self.test_device_id,
                "feature_type": "ai-speech-to-text",
                "platform": "windows",
                "session_date": "2025-01-27",
                
                # 聚合使用量
                "duration_seconds": 1500,
                "session_count": 5,
                
                # 頂層 token 欄位
                "prompt_tokens": 150,
                "completion_tokens": 300,
                "input_tokens": 75,
                "output_tokens": 125,
                "total_tokens": 650,
                
                # 時間追蹤
                "first_session_at": "2025-01-27T08:00:00Z",
                "last_session_at": "2025-01-27T18:30:00Z",
                "created_at": "2025-01-27T08:00:00Z",
                "updated_at": "2025-01-27T18:30:00Z"
            }
            
            # 驗證必要欄位存在
            required_fields = [
                "user_id", "device_id", "feature_type", "platform", "session_date",
                "duration_seconds", "session_count",
                "prompt_tokens", "completion_tokens", "input_tokens", "output_tokens", "total_tokens",
                "first_session_at", "last_session_at", "created_at", "updated_at"
            ]
            
            for field in required_fields:
                self.assertIn(field, usage_log_record)
            
            # 驗證 token 欄位都是數字
            token_fields = ["prompt_tokens", "completion_tokens", "input_tokens", "output_tokens", "total_tokens"]
            for field in token_fields:
                self.assertIsInstance(usage_log_record[field], int)
                self.assertGreaterEqual(usage_log_record[field], 0)
            
            # 驗證 total_tokens 邏輯 (不一定等於其他欄位總和，因為不同 API)
            self.assertGreater(usage_log_record["total_tokens"], 0)
            
            print("✅ 頂層 token 欄位結構正確")
            print(f"   必要欄位數: {len(required_fields)}")
            print(f"   Token 欄位: {token_fields}")
            print(f"   總 tokens: {usage_log_record['total_tokens']}")
            
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")
            self.fail(f"頂層 token 欄位測試失敗: {str(e)}")
    
    def test_submit_usage_response_format(self):
        """測試 submit_usage 回應格式"""
        print("\n📤 測試 submit_usage 回應格式")
        print("=" * 40)
        
        try:
            # 模擬 submit_usage 回應
            submit_usage_response = {
                "success": True,
                "data": {
                    "usage_log_id": f"{self.test_user_id}_{self.test_device_id}_ai-speech-to-text_2025-01-27",
                    "duration_recorded": 300,
                    # 頂層 token 欄位
                    "prompt_tokens": 25,
                    "completion_tokens": 45,
                    "input_tokens": 15,
                    "output_tokens": 30,
                    "total_tokens": 115,
                    "remaining_quota": {
                        "daily_remaining_seconds": 5100,
                        "daily_limit_seconds": 10800,
                        "can_use": True
                    }
                },
                "message": "使用量記錄成功：300 秒"
            }
            
            # 驗證回應結構
            self.assertTrue(submit_usage_response["success"])
            self.assertIn("data", submit_usage_response)
            
            data = submit_usage_response["data"]
            
            # 驗證必要欄位
            required_fields = [
                "usage_log_id", "duration_recorded",
                "prompt_tokens", "completion_tokens", "input_tokens", "output_tokens", "total_tokens",
                "remaining_quota"
            ]
            
            for field in required_fields:
                self.assertIn(field, data)
            
            # 驗證 token 欄位類型
            token_fields = ["prompt_tokens", "completion_tokens", "input_tokens", "output_tokens", "total_tokens"]
            for field in token_fields:
                self.assertIsInstance(data[field], int)
                self.assertGreaterEqual(data[field], 0)
            
            # 驗證配額資訊
            quota = data["remaining_quota"]
            self.assertIn("daily_remaining_seconds", quota)
            self.assertIn("daily_limit_seconds", quota)
            self.assertIn("can_use", quota)
            
            print("✅ submit_usage 回應格式正確")
            print(f"   Usage Log ID: {data['usage_log_id']}")
            print(f"   Duration: {data['duration_recorded']} 秒")
            print(f"   Total Tokens: {data['total_tokens']}")
            print(f"   Can Use: {quota['can_use']}")
            
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")
            self.fail(f"submit_usage 回應格式測試失敗: {str(e)}")

def run_usage_logs_aggregation_tests():
    """執行 usage_logs 聚合測試"""
    print("🧪 開始 usage_logs 聚合測試...")
    print("=" * 50)
    
    # 創建測試套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestUsageLogsAggregation)
    
    # 執行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("=" * 50)
    
    if result.wasSuccessful():
        print("🎉 所有 usage_logs 聚合測試通過！")
        print("\n📋 測試場景總結:")
        print("✅ Token 聚合邏輯")
        print("✅ 文檔 ID 格式")
        print("✅ 頂層 token 欄位結構")
        print("✅ submit_usage 回應格式")
        print("\n🚀 usage_logs 聚合功能驗證完成！")
        return True
    else:
        print(f"❌ 測試失敗: {len(result.failures)} 個失敗, {len(result.errors)} 個錯誤")
        
        if result.failures:
            print("\n失敗的測試:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
        
        if result.errors:
            print("\n錯誤的測試:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
        
        return False

if __name__ == '__main__':
    success = run_usage_logs_aggregation_tests()
    sys.exit(0 if success else 1)
