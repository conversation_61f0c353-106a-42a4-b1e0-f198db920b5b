#!/usr/bin/env python3
"""
資料庫重置腳本
"""

import sys
import os
import json
from datetime import datetime

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend-api'))

def reset_database():
    """執行資料庫重置"""
    try:
        # 初始化 Firebase
        import firebase_admin
        from firebase_admin import credentials, firestore
        
        # 檢查是否已經初始化
        try:
            firebase_admin.get_app()
        except ValueError:
            # 使用服務帳戶金鑰初始化
            cred_path = os.path.join(os.path.dirname(__file__), '..', 'serviceAccountKey.json')
            if os.path.exists(cred_path):
                cred = credentials.Certificate(cred_path)
                firebase_admin.initialize_app(cred)
            else:
                print("❌ 找不到服務帳戶金鑰文件")
                return False
        
        # 導入重置功能
        from functions.admin.database_reset import reset_users_collection, reset_subscriptions_collection, reset_devices_collection
        
        db = firestore.client()
        
        print("🔄 開始資料庫重置...")
        print("=" * 50)
        
        # 重置用戶集合
        print("📝 重置用戶集合...")
        users_result = reset_users_collection(db)
        print(f"   處理: {users_result['processed']} 個文檔")
        print(f"   更新: {users_result['updated']} 個文檔")
        if users_result['errors']:
            print(f"   錯誤: {len(users_result['errors'])} 個")
        
        # 重置訂閱集合
        print("📋 重置訂閱集合...")
        subscriptions_result = reset_subscriptions_collection(db)
        print(f"   處理: {subscriptions_result['processed']} 個文檔")
        print(f"   更新: {subscriptions_result['updated']} 個文檔")
        if subscriptions_result['errors']:
            print(f"   錯誤: {len(subscriptions_result['errors'])} 個")
        
        # 重置設備集合
        print("📱 重置設備集合...")
        devices_result = reset_devices_collection(db)
        print(f"   處理: {devices_result['processed']} 個文檔")
        print(f"   更新: {devices_result['updated']} 個文檔")
        if devices_result['errors']:
            print(f"   錯誤: {len(devices_result['errors'])} 個")
        
        print("=" * 50)
        print("✅ 資料庫重置完成！")
        
        # 總結
        total_processed = users_result['processed'] + subscriptions_result['processed'] + devices_result['processed']
        total_updated = users_result['updated'] + subscriptions_result['updated'] + devices_result['updated']
        total_errors = len(users_result['errors']) + len(subscriptions_result['errors']) + len(devices_result['errors'])
        
        print(f"📊 總結:")
        print(f"   總處理: {total_processed} 個文檔")
        print(f"   總更新: {total_updated} 個文檔")
        print(f"   總錯誤: {total_errors} 個")
        
        return True
        
    except Exception as e:
        print(f"❌ 資料庫重置失敗: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_reset():
    """驗證重置結果"""
    try:
        import firebase_admin
        from firebase_admin import firestore
        
        db = firestore.client()
        
        print("\n🔍 驗證重置結果...")
        print("=" * 50)
        
        # 檢查用戶表
        users_with_redundant = 0
        redundant_fields = ['daily_limit_seconds', 'current_day_used_seconds', 'device_limit']
        
        for user_doc in db.collection('users').limit(10).stream():
            user_data = user_doc.to_dict()
            for field in redundant_fields:
                if field in user_data:
                    users_with_redundant += 1
                    break
        
        print(f"👤 用戶表檢查:")
        print(f"   仍有冗餘欄位的用戶: {users_with_redundant}/10 (抽樣)")
        
        # 檢查訂閱表
        subscriptions_missing = 0
        required_fields = ['register_region_timezone', 'usage_reset_hour', 'last_daily_reset_date']
        
        for subscription_doc in db.collection('subscriptions').limit(10).stream():
            subscription_data = subscription_doc.to_dict()
            for field in required_fields:
                if field not in subscription_data:
                    subscriptions_missing += 1
                    break
        
        print(f"📋 訂閱表檢查:")
        print(f"   缺少必要欄位的訂閱: {subscriptions_missing}/10 (抽樣)")
        
        # 檢查設備表
        devices_missing_status = 0
        
        for device_doc in db.collection('devices').limit(10).stream():
            device_data = device_doc.to_dict()
            if 'status' not in device_data or 'is_authorized' not in device_data:
                devices_missing_status += 1
        
        print(f"📱 設備表檢查:")
        print(f"   缺少狀態欄位的設備: {devices_missing_status}/10 (抽樣)")
        
        print("=" * 50)
        
        if users_with_redundant == 0 and subscriptions_missing == 0 and devices_missing_status == 0:
            print("✅ 驗證通過！資料庫重置成功")
            return True
        else:
            print("⚠️  發現問題，可能需要重新執行重置")
            return False
        
    except Exception as e:
        print(f"❌ 驗證失敗: {str(e)}")
        return False

if __name__ == '__main__':
    print("🚀 SpeechPilot 資料庫重置工具")
    print("=" * 50)
    
    # 確認操作
    confirm = input("⚠️  這將修改資料庫結構，是否繼續？(y/N): ")
    if confirm.lower() != 'y':
        print("❌ 操作已取消")
        sys.exit(0)
    
    # 執行重置
    success = reset_database()
    
    if success:
        # 驗證結果
        verify_reset()
    else:
        print("❌ 重置失敗，請檢查錯誤訊息")
        sys.exit(1)
