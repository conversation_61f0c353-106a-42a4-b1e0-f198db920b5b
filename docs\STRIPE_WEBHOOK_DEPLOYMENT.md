# 🚀 Stripe Webhook 部署指南

## 📋 概覽

本指南將幫助你部署 SpeakOneAI 的 Stripe Webhook 處理系統，包括：
- Stripe 訂閱生命週期管理
- 每日配額重置（基於用戶時區）
- 支付事件處理
- 購買記錄管理

## 🏗️ 架構概覽

```
Stripe → Webhook → Cloud Functions → Firestore
                      ↓
                 定時任務 (每小時)
                      ↓
                 配額重置 (基於時區)
```

## 📁 新增檔案結構

```
backend-api/
├── functions/
│   ├── webhooks/
│   │   ├── __init__.py
│   │   ├── stripe_webhook.py          # 主 Webhook 處理器
│   │   ├── subscription_handlers.py   # 訂閱事件處理
│   │   └── payment_handlers.py        # 支付事件處理
│   ├── scheduled/
│   │   ├── __init__.py
│   │   └── daily_quota_reset.py       # 每日配額重置定時任務
│   └── utils/
│       └── stripe_config.py           # Stripe 配置管理
└── main.py                            # 更新的主入口點
```

## ⚙️ 環境配置

### 1. 環境變數設置

**開發環境 (.env.dev)**:
```bash
STRIPE_SECRET_KEY=sk_test_your_test_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
ENVIRONMENT=dev
```

**生產環境 (.env.prod)**:
```bash
STRIPE_SECRET_KEY=sk_live_your_live_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
ENVIRONMENT=prod
```

### 2. 更新 Stripe Price ID 映射

編輯 `backend-api/functions/utils/stripe_config.py`：

```python
STRIPE_PRICE_MAPPING: Dict[str, str] = {
    'dev': {
        # 替換為你的實際測試 Price IDs
        'price_test_starter_monthly': 'STARTER',
        'price_test_pro_monthly': 'PRO',
        'price_test_premium_monthly': 'PREMIUM',
        'price_test_max_monthly': 'MAX',
        
        'price_test_starter_yearly': 'STARTER',
        'price_test_pro_yearly': 'PRO',
        'price_test_premium_yearly': 'PREMIUM',
        'price_test_max_yearly': 'MAX',
    },
    'prod': {
        # 替換為你的實際生產 Price IDs
        'price_live_starter_monthly': 'STARTER',
        'price_live_pro_monthly': 'PRO',
        'price_live_premium_monthly': 'PREMIUM',
        'price_live_max_monthly': 'MAX',
        
        'price_live_starter_yearly': 'STARTER',
        'price_live_pro_yearly': 'PRO',
        'price_live_premium_yearly': 'PREMIUM',
        'price_live_max_yearly': 'MAX',
    }
}
```

## 🚀 部署步驟

### 1. 安裝依賴

```bash
cd backend-api
pip install -r requirements.txt
```

### 2. 測試配置

```bash
# 執行測試套件
cd unit_test
python run_webhook_tests.py
```

### 3. 部署到 Firebase

```bash
# 開發環境
firebase use speakoneai-dev-9f995
firebase deploy --only functions

# 生產環境
firebase use speakoneai-prod
firebase deploy --only functions
```

### 4. 配置 Stripe Webhook

#### 開發環境 Webhook 設置

1. 登入 [Stripe Dashboard](https://dashboard.stripe.com/test/webhooks)
2. 點擊 "Add endpoint"
3. 設置端點 URL：
   ```
   https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/backend-api/stripe_webhook
   ```
4. 選擇事件：
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

#### 生產環境 Webhook 設置

1. 切換到 [Live mode](https://dashboard.stripe.com/webhooks)
2. 重複上述步驟，使用生產端點：
   ```
   https://asia-east1-speakoneai-prod.cloudfunctions.net/backend-api/stripe_webhook
   ```

### 5. 獲取 Webhook Secret

1. 在 Stripe Dashboard 中點擊創建的 Webhook
2. 在 "Signing secret" 部分點擊 "Reveal"
3. 複製 `whsec_...` 開頭的密鑰
4. 設置為環境變數 `STRIPE_WEBHOOK_SECRET`

## 🧪 測試驗證

### 1. 使用 Stripe CLI 測試

```bash
# 安裝 Stripe CLI
# 登入
stripe login

# 轉發事件到開發環境
stripe listen --forward-to https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/backend-api/stripe_webhook

# 觸發測試事件
stripe trigger customer.subscription.created
stripe trigger customer.subscription.updated
stripe trigger invoice.payment_succeeded
```

### 2. 檢查 Firestore 數據

驗證以下集合中的數據：
- `subscriptions` - 訂閱記錄
- `purchases` - 購買記錄
- `processed_events` - 已處理的 Webhook 事件
- `quota_reset_logs` - 配額重置日誌

### 3. 監控日誌

```bash
# 查看 Cloud Functions 日誌
firebase functions:log --only stripe_webhook
firebase functions:log --only daily_quota_reset_handler
```

## 📊 功能特性

### 1. 訂閱生命週期管理

- **創建**: 用戶訂閱時自動更新配額限制
- **升級**: 立即生效新的配額限制
- **降級**: 保持當前配額直到週期結束
- **取消**: 自動降級到 FREE 計劃

### 2. 每日配額重置

- **時區支援**: 根據用戶註冊時區重置
- **執行頻率**: 每小時檢查一次
- **重置時間**: 用戶當地時間 00:00
- **全球覆蓋**: 支援 UTC-12 到 UTC+14

### 3. 支付處理

- **成功**: 創建購買記錄，重置配額
- **失敗**: 記錄失敗原因，保持服務狀態
- **冪等性**: 防止重複處理同一事件

### 4. 數據一致性

- **雙表同步**: users 和 subscriptions 表同步更新
- **事件追蹤**: 記錄所有 Webhook 事件
- **錯誤處理**: 完整的錯誤日誌和重試機制

## 🔧 故障排除

### 常見問題

1. **Webhook 簽名驗證失敗**
   - 檢查 `STRIPE_WEBHOOK_SECRET` 是否正確
   - 確認端點 URL 是否匹配

2. **Price ID 映射錯誤**
   - 更新 `stripe_config.py` 中的映射
   - 確認 Price ID 格式正確

3. **配額重置不工作**
   - 檢查用戶時區設置
   - 驗證定時任務是否正常執行

4. **數據不同步**
   - 檢查 Firestore 權限
   - 驗證數據庫連接

### 調試工具

```bash
# 檢查配置
python -c "
from functions.utils.stripe_config import get_stripe_configuration_summary
import json
print(json.dumps(get_stripe_configuration_summary(), indent=2))
"

# 手動重置用戶配額
python -c "
from functions.scheduled.daily_quota_reset import manual_reset_user_quota
result = manual_reset_user_quota('user_id_here')
print(result)
"

# 查看配額重置狀態
python -c "
from functions.scheduled.daily_quota_reset import get_quota_reset_status
result = get_quota_reset_status()
print(result)
"
```

## 📈 監控和維護

### 1. 關鍵指標

- Webhook 處理成功率
- 配額重置執行次數
- 支付事件處理延遲
- 數據同步一致性

### 2. 告警設置

建議設置以下告警：
- Webhook 處理失敗率 > 5%
- 配額重置失敗 > 10 個用戶
- 支付事件處理延遲 > 5 分鐘

### 3. 定期維護

- 清理過期的 `processed_events` 記錄
- 檢查 `quota_reset_logs` 統計
- 驗證 Stripe 配置更新

## 🎯 總結

通過本部署指南，你已經成功建立了：

✅ **完整的 Stripe Webhook 處理系統**
✅ **基於時區的每日配額重置**
✅ **訂閱生命週期自動管理**
✅ **支付事件完整處理**
✅ **購買記錄自動創建**
✅ **數據一致性保證**

系統現在可以：
- 自動處理用戶訂閱變更
- 根據用戶時區重置每日配額
- 記錄所有支付交易
- 在訂閱取消時降級到 FREE 計劃
- 在計劃升級時立即更新配額

🚀 **你的 SpeakOneAI 訂閱系統已準備就緒！**
