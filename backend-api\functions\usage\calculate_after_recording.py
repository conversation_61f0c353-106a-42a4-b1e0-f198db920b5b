"""
Usage calculation after recording functionality for SpeechPilot Firebase Functions
"""

import logging
import math
from datetime import datetime, timezone
from firebase_functions import https_fn
from firebase_admin import firestore
from typing import Dict, Any

from ..utils.validation import validate_auth, validate_device_id, validate_features, validate_timestamp
from ..utils.database import get_user_data, check_device_exists, create_usage_log_and_update_counters
from ..utils.constants import ERROR_MESSAGES, TOKEN_CALCULATION

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端"""
    return firestore.client()

def get_user_subscription_status_simple(user_id: str) -> Dict[str, Any]:
    """簡化的用戶訂閱狀態檢查"""
    try:
        db = get_db()

        # 獲取用戶資訊
        user_doc = db.collection('users').document(user_id).get()
        if not user_doc.exists:
            return {"can_use": False, "available_seconds_today": 0}

        user_data = user_doc.to_dict()
        subscription_id = user_data.get('current_subscription_id')

        if not subscription_id:
            return {"can_use": False, "available_seconds_today": 0}

        # 獲取訂閱資訊
        subscription_doc = db.collection('subscriptions').document(subscription_id).get()
        if not subscription_doc.exists:
            return {"can_use": False, "available_seconds_today": 0}

        subscription_data = subscription_doc.to_dict()

        # 檢查訂閱狀態
        if subscription_data.get('status') != 'active':
            return {"can_use": False, "available_seconds_today": 0}

        # 計算可用時間
        daily_limit = subscription_data.get('daily_limit_seconds', 600)
        daily_used = subscription_data.get('current_day_used_seconds', 0)

        if daily_limit == -1:
            available_seconds = -1
        else:
            available_seconds = max(0, daily_limit - daily_used)

        return {
            "can_use": available_seconds > 0 or daily_limit == -1,
            "available_seconds_today": available_seconds
        }

    except Exception as e:
        logger.error(f"獲取用戶訂閱狀態失敗: {user_id}, 錯誤: {str(e)}")
        return {"can_use": False, "available_seconds_today": 0}

def calculate_usage_after_recording_handler(request: https_fn.CallableRequest) -> Dict[str, Any]:
    """
    錄音完成後計算使用量和成本
    
    Args:
        request: Firebase Functions 請求對象
        
    Returns:
        Dict[str, Any]: 計算結果
    """
    try:
        # 驗證用戶認證
        user_id = validate_auth(request)
        
        # 獲取請求數據
        data = request.data or {}
        device_id = data.get('device_id')
        session_start = data.get('session_start')
        session_end = data.get('session_end')
        seconds_used = data.get('seconds_used', 0)
        features = data.get('features', [])
        platform = data.get('platform', 'unknown')
        app_version = data.get('app_version', 'unknown')
        
        logger.info(f"Calculating usage after recording for user {user_id}, device {device_id}")
        
        # 驗證必要參數
        if not device_id:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="device_id is required"
            )
        
        if not validate_device_id(device_id):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="Invalid device_id format"
            )
        
        if not session_start or not session_end:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="session_start and session_end are required"
            )
        
        if not validate_timestamp(session_start) or not validate_timestamp(session_end):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="Invalid timestamp format"
            )
        
        if not isinstance(seconds_used, (int, float)) or seconds_used < 0:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="Invalid seconds_used"
            )
        
        if not validate_features(features):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="Invalid features"
            )
        
        # 檢查設備是否存在且屬於用戶
        if not check_device_exists(user_id, device_id):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.NOT_FOUND,
                message=ERROR_MESSAGES["DEVICE_NOT_FOUND"]
            )
        
        # 轉換時間戳為 datetime 對象
        if hasattr(session_start, 'seconds'):
            start_time = datetime.fromtimestamp(session_start.seconds, tz=timezone.utc)
        else:
            start_time = session_start
            
        if hasattr(session_end, 'seconds'):
            end_time = datetime.fromtimestamp(session_end.seconds, tz=timezone.utc)
        else:
            end_time = session_end
        
        # 驗證時間邏輯
        if start_time >= end_time:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="session_start must be before session_end"
            )
        
        # 檢查用戶訂閱狀態
        subscription_status = get_user_subscription_status_simple(user_id)
        
        if not subscription_status["can_use"]:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.PERMISSION_DENIED,
                message="用戶無使用權限"
            )
        
        # 檢查是否超出可用時間並調整（只檢查每日限制）
        available_seconds = subscription_status["available_seconds_today"]
        
        final_seconds_used = seconds_used
        if available_seconds != -1 and seconds_used > available_seconds:
            # 調整為可用時間
            final_seconds_used = max(0, available_seconds)
            logger.warning(f"User {user_id} usage time exceeded limit, adjusted to: {final_seconds_used}s")
        
        # 計算 tokens 使用量（基於時間和功能）
        tokens_used = 0
        if 'ai-speech-to-text' in features:
            # AI 模式消耗更多 tokens
            tokens_used = math.ceil(final_seconds_used * TOKEN_CALCULATION["ai_speech_to_text"])
        elif 'direct-speech-to-text' in features:
            # 直接模式消耗較少 tokens
            tokens_used = math.ceil(final_seconds_used * TOKEN_CALCULATION["direct_speech_to_text"])
        
        # 獲取用戶訂閱ID
        user_data = get_user_data(user_id)
        subscription_id = user_data.get('current_subscription_id')

        if not subscription_id:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.FAILED_PRECONDITION,
                message="用戶沒有有效的訂閱"
            )

        # 創建使用記錄並更新計數器（原子操作）
        result = create_usage_log_and_update_counters(
            user_id=user_id,
            subscription_id=subscription_id,
            device_id=device_id,
            duration_seconds=final_seconds_used,
            feature_type='ai-speech-to-text' if 'ai-speech-to-text' in features else 'direct-speech-to-text',
            platform=platform
        )

        usage_log_id = result["usage_log_id"]
        
        # 更新設備活動時間
        device_ref = db.collection('devices').where(
            'user_id', '==', user_id
        ).where(
            'device_id', '==', device_id
        ).limit(1).stream()
        
        for doc in device_ref:
            doc.reference.update({
                'last_active_at': firestore.SERVER_TIMESTAMP
            })
            break
        
        result = {
            "seconds_used": final_seconds_used,
            "tokens_used": tokens_used,
            "cost": 0,  # 可以在這裡添加成本計算
            "success": True,
            "usage_log_id": usage_log_id,
            "adjusted": final_seconds_used != seconds_used
        }
        
        logger.info(f"Usage calculated for user {user_id}: {final_seconds_used}s, {tokens_used} tokens")
        
        return result
        
    except https_fn.HttpsError:
        # 重新拋出 Firebase Functions 錯誤
        raise
    except Exception as e:
        logger.error(f"Error calculating usage after recording: {str(e)}")
        raise https_fn.HttpsError(
            code=https_fn.FunctionsErrorCode.INTERNAL,
            message=ERROR_MESSAGES["INTERNAL_ERROR"]
        )
