"""
環境配置工具
支援開發和生產環境的動態切換
"""

import os
import json
from typing import Dict, Any, Optional

class EnvironmentConfig:
    """環境配置管理類"""
    
    def __init__(self):
        self._config = None
        self._current_env = None
        self.load_config()
    
    def load_config(self):
        """載入環境配置"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '../../../config/environments.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
            else:
                # 預設配置
                self._config = {
                    "environments": {
                        "dev": {
                            "name": "開發環境",
                            "firebase_project": "speakoneai-dev-9f995",
                            "firestore_database": "(default)",
                            "functions_name": "backend-api",
                            "functions_url": "https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/backend-api",
                            "region": "asia-east1"
                        },
                        "prod": {
                            "name": "生產環境",
                            "firebase_project": "speakoneai-prod",
                            "firestore_database": "(default)",
                            "functions_name": "backend-api",
                            "functions_url": "https://asia-east1-speakoneai-prod.cloudfunctions.net/backend-api",
                            "region": "asia-east1"
                        }
                    },
                    "current_environment": "dev"
                }
        except Exception as e:
            print(f"載入環境配置失敗: {e}")
            self._config = None
    
    def get_current_environment(self) -> str:
        """獲取當前環境"""
        # 總是檢查環境變數以支持動態切換
        env_from_var = os.getenv('ENVIRONMENT')
        if env_from_var in ['dev', 'prod']:
            self._current_env = env_from_var
            return self._current_env

        # 如果有緩存的環境設置
        if self._current_env:
            return self._current_env

        # 從配置文件獲取
        if self._config:
            self._current_env = self._config.get('current_environment', 'dev')
            return self._current_env

        # 預設為開發環境
        self._current_env = 'dev'
        return self._current_env
    
    def get_environment_config(self, env: Optional[str] = None) -> Dict[str, Any]:
        """獲取環境配置"""
        if not env:
            env = self.get_current_environment()
        
        if self._config and env in self._config.get('environments', {}):
            return self._config['environments'][env]
        
        # 預設配置
        if env == 'prod':
            return {
                "name": "生產環境",
                "firebase_project": "speakoneai-prod",
                "firestore_database": "(default)",
                "functions_name": "backend-api",
                "functions_url": "https://asia-east1-speakoneai-prod.cloudfunctions.net/backend-api",
                "region": "asia-east1"
            }
        else:
            return {
                "name": "開發環境",
                "firebase_project": "speakoneai-dev-9f995",
                "firestore_database": "(default)",
                "functions_name": "backend-api",
                "functions_url": "https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/backend-api",
                "region": "asia-east1"
            }
    
    def get_firestore_database_id(self) -> str:
        """獲取 Firestore 數據庫 ID"""
        # 優先從環境變數獲取
        db_id = os.getenv('FIRESTORE_DATABASE_ID')
        if db_id:
            return db_id
        
        # 從配置獲取
        config = self.get_environment_config()
        return config.get('firestore_database', '(default)')
    
    def get_functions_name(self) -> str:
        """獲取 Functions 名稱"""
        # 優先從環境變數獲取
        func_name = os.getenv('FUNCTIONS_NAME')
        if func_name:
            return func_name
        
        # 從配置獲取
        config = self.get_environment_config()
        return config.get('functions_name', 'backend-api')

    def get_functions_url(self) -> str:
        """獲取 Functions URL"""
        config = self.get_environment_config()
        return config.get('functions_url', 'https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/backend-api')
    
    def is_production(self) -> bool:
        """是否為生產環境"""
        return self.get_current_environment() == 'prod'
    
    def is_development(self) -> bool:
        """是否為開發環境"""
        return self.get_current_environment() == 'dev'
    
    def set_environment(self, env: str):
        """設置當前環境"""
        if env in ['dev', 'prod']:
            self._current_env = env
            os.environ['ENVIRONMENT'] = env
            
            # 同時設置相關環境變數
            config = self.get_environment_config(env)
            os.environ['FIRESTORE_DATABASE_ID'] = config['firestore_database']
            os.environ['FUNCTIONS_NAME'] = config['functions_name']
    
    def get_environment_info(self) -> Dict[str, Any]:
        """獲取完整環境資訊"""
        current_env = self.get_current_environment()
        config = self.get_environment_config(current_env)
        
        return {
            'current_environment': current_env,
            'environment_name': config.get('name'),
            'firebase_project': config.get('firebase_project'),
            'firestore_database': config.get('firestore_database'),
            'functions_name': config.get('functions_name'),
            'functions_url': config.get('functions_url'),
            'region': config.get('region'),
            'is_production': self.is_production(),
            'is_development': self.is_development()
        }

# 全局環境配置實例
env_config = EnvironmentConfig()

def get_db():
    """獲取 Firestore 客戶端（環境感知）"""
    from firebase_admin import firestore
    database_id = env_config.get_firestore_database_id()
    return firestore.client(database_id=database_id)

def get_environment_info():
    """獲取環境資訊的便捷函數"""
    return env_config.get_environment_info()

def is_production():
    """是否為生產環境的便捷函數"""
    return env_config.is_production()

def is_development():
    """是否為開發環境的便捷函數"""
    return env_config.is_development()
