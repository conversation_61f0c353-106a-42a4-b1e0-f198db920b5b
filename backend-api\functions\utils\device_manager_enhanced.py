"""
增強設備管理模組 - 支援設備狀態追蹤和操作記錄
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from firebase_admin import firestore
from .constants import DeviceStatus, DeviceAction, SUBSCRIPTION_PLANS
from .environment import get_db

logger = logging.getLogger(__name__)

class EnhancedDeviceManager:
    """增強設備管理器"""
    
    def __init__(self):
        self.db = get_db()
    
    def register_device(self, user_id: str, subscription_id: str, device_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        註冊設備（增強版）
        
        Args:
            user_id: 用戶ID
            subscription_id: 訂閱ID
            device_data: 設備資料
            
        Returns:
            Dict[str, Any]: 註冊結果
        """
        try:
            device_id = device_data['device_id']
            now = datetime.utcnow()
            
            # 檢查設備是否已存在
            existing_device = self._get_device_by_id(user_id, device_id)
            
            if existing_device:
                # 更新現有設備
                result = self._update_existing_device(existing_device, device_data, now)
                self._log_device_action(user_id, device_id, DeviceAction.VALIDATE, now)
            else:
                # 檢查設備數量限制
                self._check_device_limit(subscription_id)
                
                # 創建新設備
                result = self._create_new_device(user_id, subscription_id, device_data, now)
                self._log_device_action(user_id, device_id, DeviceAction.REGISTER, now)
            
            # 同步設備列表到用戶和訂閱表
            self._sync_device_lists(user_id, subscription_id)
            
            return result
            
        except Exception as e:
            logger.error(f"設備註冊失敗: {user_id}/{device_id}, 錯誤: {str(e)}")
            raise
    
    def remove_device(self, user_id: str, device_id: str, reason: str = "user_request") -> Dict[str, Any]:
        """
        移除設備（軟刪除）
        
        Args:
            user_id: 用戶ID
            device_id: 設備ID
            reason: 移除原因
            
        Returns:
            Dict[str, Any]: 移除結果
        """
        try:
            device = self._get_device_by_id(user_id, device_id)
            if not device:
                raise ValueError(f"設備不存在: {device_id}")
            
            now = datetime.utcnow()
            device_ref = device['ref']
            
            # 軟刪除設備
            device_ref.update({
                'is_active': False,
                'status': DeviceStatus.REMOVED.value,
                'removed_at': now,
                'removal_reason': reason,
                'updated_at': now
            })
            
            # 設備操作記錄已移除
            
            # 同步設備列表
            subscription_id = device['data'].get('subscription_id')
            if subscription_id:
                self._sync_device_lists(user_id, subscription_id)
            
            logger.info(f"設備移除成功: {user_id}/{device_id}, 原因: {reason}")
            
            return {
                'success': True,
                'device_id': device_id,
                'status': DeviceStatus.REMOVED.value,
                'removed_at': now.isoformat() + 'Z'
            }
            
        except Exception as e:
            logger.error(f"設備移除失敗: {user_id}/{device_id}, 錯誤: {str(e)}")
            raise
    
    def unlink_device(self, user_id: str, device_id: str) -> Dict[str, Any]:
        """
        解除設備綁定
        
        Args:
            user_id: 用戶ID
            device_id: 設備ID
            
        Returns:
            Dict[str, Any]: 解綁結果
        """
        try:
            device = self._get_device_by_id(user_id, device_id)
            if not device:
                raise ValueError(f"設備不存在: {device_id}")
            
            now = datetime.utcnow()
            device_ref = device['ref']
            
            # 解除綁定
            device_ref.update({
                'is_active': False,
                'status': DeviceStatus.UNLINKED.value,
                'unlinked_at': now,
                'updated_at': now
            })
            
            # 記錄操作
            self._log_device_action(user_id, device_id, DeviceAction.UNLINK, now)
            
            # 同步設備列表
            subscription_id = device['data'].get('subscription_id')
            if subscription_id:
                self._sync_device_lists(user_id, subscription_id)
            
            logger.info(f"設備解綁成功: {user_id}/{device_id}")
            
            return {
                'success': True,
                'device_id': device_id,
                'status': DeviceStatus.UNLINKED.value,
                'unlinked_at': now.isoformat() + 'Z'
            }
            
        except Exception as e:
            logger.error(f"設備解綁失敗: {user_id}/{device_id}, 錯誤: {str(e)}")
            raise
    
    def get_user_devices(self, user_id: str, include_inactive: bool = False) -> List[Dict[str, Any]]:
        """
        獲取用戶設備列表
        
        Args:
            user_id: 用戶ID
            include_inactive: 是否包含非活躍設備
            
        Returns:
            List[Dict[str, Any]]: 設備列表
        """
        try:
            query = self.db.collection('devices').where('user_id', '==', user_id)
            
            if not include_inactive:
                query = query.where('is_active', '==', True)
            
            devices = []
            for doc in query.stream():
                device_data = doc.to_dict()
                device_data['id'] = doc.id
                devices.append(device_data)
            
            return devices
            
        except Exception as e:
            logger.error(f"獲取用戶設備失敗: {user_id}, 錯誤: {str(e)}")
            raise
    
    def _get_device_by_id(self, user_id: str, device_id: str) -> Optional[Dict[str, Any]]:
        """獲取設備資料"""
        query = self.db.collection('devices').where('user_id', '==', user_id).where('device_id', '==', device_id).limit(1)
        
        for doc in query.stream():
            return {
                'ref': doc.reference,
                'data': doc.to_dict(),
                'id': doc.id
            }
        
        return None
    
    def _check_device_limit(self, subscription_id: str):
        """檢查設備數量限制"""
        subscription_ref = self.db.collection('subscriptions').document(subscription_id)
        subscription_doc = subscription_ref.get()
        
        if not subscription_doc.exists:
            raise ValueError(f"訂閱不存在: {subscription_id}")
        
        subscription_data = subscription_doc.to_dict()
        plan = subscription_data.get('plan', 'FREE')
        plan_config = SUBSCRIPTION_PLANS.get(plan, SUBSCRIPTION_PLANS['FREE'])
        max_devices = plan_config['max_devices']
        
        if max_devices == -1:
            return  # 無限制
        
        active_devices = subscription_data.get('active_device_ids', [])
        if len(active_devices) >= max_devices:
            raise ValueError(f"設備數量超過限制: {len(active_devices)}/{max_devices}")
    
    def _create_new_device(self, user_id: str, subscription_id: str, device_data: Dict[str, Any], now: datetime) -> Dict[str, Any]:
        """創建新設備"""
        device_record = {
            'user_id': user_id,
            'subscription_id': subscription_id,
            'device_id': device_data['device_id'],
            'device_name': device_data.get('device_name', 'Unknown Device'),
            'platform': device_data.get('platform', 'unknown'),
            'app_version': device_data.get('app_version', '1.0.0'),
            'os_version': device_data.get('device_info', {}).get('os_version', 'unknown'),
            'device_fingerprint': device_data.get('device_fingerprint', ''),
            'is_active': True,
            'is_authorized': True,
            'status': DeviceStatus.ACTIVE.value,
            'created_at': now,
            'updated_at': now,
            'last_active_at': now,
            'registration_ip': device_data.get('registration_ip', 'unknown'),
            'user_agent': device_data.get('user_agent', 'unknown')
        }
        
        device_ref = self.db.collection('devices').document()
        device_ref.set(device_record)
        
        return {
            'success': True,
            'device_id': device_data['device_id'],
            'is_new': True,
            'status': DeviceStatus.ACTIVE.value
        }
    
    def _update_existing_device(self, existing_device: Dict[str, Any], device_data: Dict[str, Any], now: datetime) -> Dict[str, Any]:
        """更新現有設備"""
        device_ref = existing_device['ref']
        
        update_data = {
            'device_name': device_data.get('device_name') or existing_device['data']['device_name'],
            'app_version': device_data.get('app_version', existing_device['data'].get('app_version')),
            'os_version': device_data.get('device_info', {}).get('os_version', existing_device['data'].get('os_version')),
            'is_active': True,
            'is_authorized': True,
            'status': DeviceStatus.ACTIVE.value,
            'last_active_at': now,
            'updated_at': now
        }
        
        device_ref.update(update_data)
        
        return {
            'success': True,
            'device_id': device_data['device_id'],
            'is_new': False,
            'status': DeviceStatus.ACTIVE.value
        }
    
    def _sync_device_lists(self, user_id: str, subscription_id: str):
        """同步設備列表到用戶和訂閱表"""
        try:
            # 獲取活躍設備列表
            active_devices = self.get_user_devices(user_id, include_inactive=False)
            device_ids = [device['device_id'] for device in active_devices]
            
            now = datetime.utcnow()
            
            # 更新用戶表
            user_ref = self.db.collection('users').document(user_id)
            user_ref.update({
                'device_ids': device_ids,
                'last_device_check': now,
                'updated_at': now
            })
            
            # 更新訂閱表
            subscription_ref = self.db.collection('subscriptions').document(subscription_id)
            subscription_ref.update({
                'active_device_ids': device_ids,
                'last_device_limit_check': now,
                'updated_at': now
            })
            
            logger.info(f"設備列表同步成功: {user_id} -> {device_ids}")
            
        except Exception as e:
            logger.error(f"設備列表同步失敗: {user_id}, 錯誤: {str(e)}")
            raise
    
    # device_action_logs 功能已移除

# 全域設備管理器實例
enhanced_device_manager = EnhancedDeviceManager()
