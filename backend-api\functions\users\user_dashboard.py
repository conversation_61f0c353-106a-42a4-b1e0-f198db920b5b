"""
用戶儀表板 API - 三合一用戶資訊
"""

import logging
from datetime import datetime
from typing import Dict, Any, List
from firebase_functions import https_fn
from firebase_admin import firestore

from ..utils.validation import validate_auth
from ..utils.constants import SUBSCRIPTION_PLANS
from ..utils.environment import get_db

logger = logging.getLogger(__name__)

def get_user_dashboard_handler(req: https_fn.CallableRequest) -> Dict[str, Any]:
    """
    獲取用戶儀表板資訊 - 三合一 API
    
    一次返回用戶資訊、訂閱狀態、設備列表和使用量統計
    
    Args:
        req: Firebase Functions 請求對象

    Returns:
        Dict[str, Any]: 完整的用戶儀表板資訊
        {
            "success": true,
            "data": {
                "user": {...},
                "subscription": {...},  // 包含 current_day_used_seconds
                "devices": [...]
            }
        }
    """
    try:
        # 驗證用戶認證
        if not req.auth:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.UNAUTHENTICATED,
                message='用戶未認證'
            )

        user_id = req.auth.uid
        
        logger.info(f"獲取用戶儀表板: {user_id}")
        
        db = get_db()
        
        # 獲取用戶基本資訊
        user_info = get_user_basic_info(db, user_id)
        
        # 獲取訂閱資訊
        subscription_info = get_subscription_info(db, user_id)
        
        # 獲取設備資訊
        devices_info = get_devices_summary(db, user_id)
        
        # 移除 usage 物件，只信任 subscription 表的 current_day_used_seconds

        response = {
            'success': True,
            'data': {
                'user': user_info,
                'subscription': subscription_info,
                'devices': devices_info
            },
            'message': '用戶儀表板資訊獲取成功'
        }
        
        logger.info(f"用戶儀表板獲取成功: {user_id}")
        
        return response
        
    except https_fn.HttpsError:
        raise
    except Exception as e:
        logger.error(f"獲取用戶儀表板失敗: {str(e)}")
        raise https_fn.HttpsError(
            code=https_fn.FunctionsErrorCode.INTERNAL,
            message=f'獲取用戶儀表板失敗: {str(e)}'
        )

def get_user_basic_info(db, user_id: str) -> Dict[str, Any]:
    """獲取用戶基本資訊"""
    try:
        user_doc = db.collection('users').document(user_id).get()
        
        if not user_doc.exists:
            return {
                'user_id': user_id,
                'email': None,
                'display_name': None,
                'created_at': None,
                'preferences': {
                    'language': 'zh-TW',
                    'notifications': True,
                    'auto_upgrade_prompts': True
                },
                'onboarding_completed': False,
                'first_device_registered': False
            }
        
        user_data = user_doc.to_dict()
        
        return {
            'user_id': user_id,
            'email': user_data.get('email'),
            'display_name': user_data.get('display_name'),
            'created_at': user_data.get('created_at'),
            'updated_at': user_data.get('updated_at'),
            'preferences': user_data.get('preferences', {
                'language': 'zh-TW',
                'notifications': True,
                'auto_upgrade_prompts': True
            }),
            'onboarding_completed': user_data.get('onboarding_completed', False),
            'first_device_registered': user_data.get('first_device_registered', False),
            'last_device_check': user_data.get('last_device_check')
        }
        
    except Exception as e:
        logger.error(f"獲取用戶基本資訊失敗: {user_id}, 錯誤: {str(e)}")
        return {
            'user_id': user_id,
            'email': None,
            'error': str(e)
        }

def get_subscription_info(db, user_id: str) -> Dict[str, Any]:
    """獲取訂閱資訊"""
    try:
        # 從用戶表獲取訂閱ID
        user_doc = db.collection('users').document(user_id).get()
        if not user_doc.exists:
            return create_default_subscription_info()
        
        user_data = user_doc.to_dict()
        subscription_id = user_data.get('current_subscription_id')
        
        if not subscription_id:
            return create_default_subscription_info()
        
        # 從訂閱表獲取詳細資訊
        subscription_doc = db.collection('subscriptions').document(subscription_id).get()
        if not subscription_doc.exists:
            return create_default_subscription_info()
        
        subscription_data = subscription_doc.to_dict()
        plan = subscription_data.get('plan', 'FREE')
        plan_config = SUBSCRIPTION_PLANS.get(plan, SUBSCRIPTION_PLANS['FREE'])
        
        return {
            'subscription_id': subscription_id,
            'plan': plan,
            'status': subscription_data.get('status', 'active'),
            'daily_limit_seconds': subscription_data.get('daily_limit_seconds', plan_config['daily_limit_seconds']),
            'current_day_used_seconds': subscription_data.get('current_day_used_seconds', 0),
            'max_devices': subscription_data.get('max_devices', plan_config['max_devices']),
            'supported_platforms': plan_config.get('supported_platforms', []),
            'features': plan_config.get('features', []),
            'register_region_timezone': subscription_data.get('register_region_timezone', 'UTC+8'),
            'usage_reset_hour': subscription_data.get('usage_reset_hour', 0),
            'last_daily_reset_date': subscription_data.get('last_daily_reset_date'),
            'last_daily_reset_at': subscription_data.get('last_daily_reset_at'),
            'created_at': subscription_data.get('created_at'),
            'updated_at': subscription_data.get('updated_at'),
            # Stripe 資訊（如果有）
            'stripe_customer_id': subscription_data.get('stripe_customer_id'),
            'stripe_subscription_id': subscription_data.get('stripe_subscription_id'),
            'next_billing_date': subscription_data.get('next_billing_date'),
            'auto_renew': subscription_data.get('auto_renew', True)
        }
        
    except Exception as e:
        logger.error(f"獲取訂閱資訊失敗: {user_id}, 錯誤: {str(e)}")
        return create_default_subscription_info()

def create_default_subscription_info() -> Dict[str, Any]:
    """創建預設訂閱資訊"""
    free_plan = SUBSCRIPTION_PLANS['FREE']
    return {
        'subscription_id': None,
        'plan': 'FREE',
        'status': 'active',
        'daily_limit_seconds': free_plan['daily_limit_seconds'],
        'current_day_used_seconds': 0,
        'max_devices': free_plan['max_devices'],
        'supported_platforms': free_plan['supported_platforms'],
        'features': free_plan['features'],
        'register_region_timezone': 'UTC+8',
        'usage_reset_hour': 0,
        'last_daily_reset_date': None,
        'last_daily_reset_at': None,
        'created_at': None,
        'updated_at': None,
        'stripe_customer_id': None,
        'stripe_subscription_id': None,
        'next_billing_date': None,
        'auto_renew': False
    }

def get_devices_summary(db, user_id: str) -> Dict[str, Any]:
    """獲取設備摘要資訊"""
    try:
        # 獲取活躍設備
        active_devices_query = db.collection('devices').where('user_id', '==', user_id).where('is_active', '==', True)
        
        active_devices = []
        for doc in active_devices_query.stream():
            device_data = doc.to_dict()
            active_devices.append({
                'device_id': device_data.get('device_id'),
                'device_name': device_data.get('device_name'),
                'platform': device_data.get('platform'),
                'status': device_data.get('status', 'active'),
                'is_authorized': device_data.get('is_authorized', True),
                'last_active_at': device_data.get('last_active_at'),
                'created_at': device_data.get('created_at'),
                'app_version': device_data.get('app_version')
            })
        
        # 移除活躍會話查詢（簡化架構）

        return {
            'active_devices': active_devices,
            'device_count': len(active_devices)
        }
        
    except Exception as e:
        logger.error(f"獲取設備摘要失敗: {user_id}, 錯誤: {str(e)}")
        return {
            'active_devices': [],
            'device_count': 0,
            'error': str(e)
        }

# get_usage_summary 函數已移除 - 只信任 subscription 表的 current_day_used_seconds
