# 🧪 SpeechPilot 業務流程測試案例

## 📋 **測試概覽**

本文檔定義了 SpeechPilot V2.2 的核心業務流程測試案例，確保系統在各種業務場景下的正確性和穩定性。

**測試環境**: `speakoneai-dev-9f995` (default 數據庫)
**Functions**: `backend-api` (開發 Functions)
**測試框架**: Python + Firebase Admin SDK
**測試類型**: 端到端業務流程測試

---

## 🎯 **測試場景總覽**

| 場景 | 名稱 | 目的 | 關鍵驗證點 |
|------|------|------|------------|
| **場景 1** | 新用戶首次 SSO 登入 | 驗證完整的用戶 Onboarding 流程 | 用戶創建、FREE 訂閱、設備註冊、狀態更新 |
| **場景 2** | 現有用戶登入（正常） | 驗證現有用戶的標準登入流程 | 用戶更新、設備驗證、狀態檢查、配額查詢 |
| **場景 3** | 現有用戶登入（設備超限） | 驗證設備數量限制機制 | 設備限制檢查、錯誤處理、升級提示 |
| **場景 3B** | 設備超限且不移除舊設備 | 驗證安全控制機制 | 未授權設備拒絕、現有設備正常工作 |
| **場景 4** | 錄音功能配額檢查 | 驗證使用量追蹤和配額管理 | 配額檢查、使用記錄、數據一致性 |

---

## 📝 **詳細測試案例**

### 🆕 **場景 1: 新用戶首次 SSO 登入**

**業務目標**: 確保新用戶能夠順利完成 Onboarding 流程並開始使用服務

**前置條件**:
- 用戶通過 Google SSO 首次登入
- 系統中不存在該用戶記錄

**測試步驟**:

#### 步驟 1: 創建新用戶
- **操作**: 調用 `create_or_update_user` API
- **輸入數據**:
  ```javascript
  {
    "uid": "new-user-{timestamp}",
    "email": "new-user-{timestamp}@example.com", 
    "name": "New Test User",
    "auth_provider": "google",
    "email_verified": true,
    "platform": "windows",
    "app_version": "1.0.0",
    "source_app": "desktop"
  }
  ```
- **預期結果**: 用戶創建成功，返回成功訊息

#### 步驟 2: 驗證 FREE 訂閱自動創建
- **操作**: 查詢 Firestore 中的 subscriptions 集合
- **預期結果**: 
  - 自動創建 FREE 計劃訂閱
  - 訂閱 ID 格式為 `sub_{user_id}`
  - 計劃限制正確設置（每日 10 分鐘，月度 5 小時，1 台設備）

#### 步驟 3: 檢查設備數量限制
- **操作**: 驗證訂閱中的設備限制設置
- **預期結果**: 
  - `max_devices = 1`
  - `current_devices = 0`

#### 步驟 4: 註冊第一台設備
- **操作**: 調用 `register_device_v2` API
- **輸入數據**:
  ```javascript
  {
    "device_id": "device_{user_id}",
    "device_name": "Test Windows PC",
    "platform": "windows", 
    "app_version": "1.0.0",
    "device_info": {
      "os_version": "10.0.26100",
      "fingerprint": "test_fingerprint_{timestamp}"
    }
  }
  ```
- **預期結果**: 設備註冊成功

#### 步驟 5: 驗證 Onboarding 完成狀態
- **操作**: 檢查用戶狀態更新
- **預期結果**:
  - `onboarding_completed = true`
  - `first_device_registered = true`
  - 用戶、訂閱、設備三表數據一致

**成功標準**: 所有 5 個步驟都通過

---

### 👤 **場景 2: 現有用戶登入（正常）**

**業務目標**: 確保現有用戶能夠正常登入並使用服務

**前置條件**:
- 用戶已存在於系統中
- 用戶已有註冊設備
- 訂閱狀態正常

**測試步驟**:

#### 準備階段: 創建測試用戶和設備
- 創建用戶記錄
- 註冊一台設備

#### 步驟 1: 用戶重新登入
- **操作**: 調用 `create_or_update_user` API（更新模式）
- **輸入數據**: 包含更新的 app_version
- **預期結果**: 用戶資訊更新成功

#### 步驟 2: 驗證設備
- **操作**: 調用 `validate_device` API
- **預期結果**: 設備驗證通過，更新最後活躍時間

#### 步驟 3: 檢查訂閱狀態
- **操作**: 調用 `check_user_subscription_status` API
- **預期結果**: 返回完整的訂閱狀態和使用量資訊

#### 步驟 4: 檢查使用配額
- **操作**: 調用 `check_usage_before_recording` API
- **預期結果**: 配額檢查通過，可以使用服務

**成功標準**: 所有 4 個步驟都通過

---

### ⚠️ **場景 3: 現有用戶登入（設備數量超限）**

**業務目標**: 驗證設備數量限制機制的正確性

**前置條件**:
- 用戶使用 FREE 計劃（限制 1 台設備）
- 用戶已註冊 1 台設備

**測試步驟**:

#### 準備階段: 創建用戶並註冊設備到達限制
- 創建 FREE 計劃用戶
- 註冊第一台設備

#### 步驟 1: 嘗試註冊第二台設備（預期失敗）
- **操作**: 調用 `register_device_v2` API 註冊第二台設備
- **預期結果**: 
  - 註冊失敗
  - 返回設備數量限制錯誤
  - 錯誤代碼包含 "RESOURCE_EXHAUSTED" 或 "設備數量限制"

#### 步驟 2: 檢查當前設備狀態
- **操作**: 查詢 Firestore 驗證設備狀態
- **預期結果**: 
  - 活躍設備數量 = 1
  - 第二台設備未被註冊

#### 步驟 3: 模擬設備管理選項
- **操作**: 模擬用戶選擇移除舊設備的操作
- **預期結果**: 提供設備管理選項

**成功標準**: 所有 3 個步驟都通過

---

### 🔒 **場景 3B: 設備超限且不移除舊設備**

**業務目標**: 驗證當用戶不移除舊設備時的安全控制機制

**前置條件**:
- 用戶使用 FREE 計劃（限制 1 台設備）
- 用戶已註冊 1 台設備
- 嘗試註冊第二台設備失敗

**測試步驟**:

#### 準備階段: 創建用戶並註冊設備到達限制
- 創建 FREE 計劃用戶
- 註冊第一台設備

#### 步驟 1: 嘗試註冊第二台設備（預期失敗）
- **操作**: 調用 `register_device_v2` API
- **預期結果**: 設備註冊失敗

#### 步驟 2: 驗證第二台設備確實沒有被註冊
- **操作**: 查詢 Firestore 驗證設備列表
- **預期結果**: 
  - 活躍設備數量 = 1
  - 第二台設備不在設備列表中

#### 步驟 3: 嘗試從未註冊設備使用服務（預期失敗）
- **操作**: 使用第二台設備 ID 調用 `check_usage_before_recording` API
- **預期結果**: 
  - 服務調用失敗
  - 返回設備未授權錯誤

#### 步驟 4: 驗證第一台設備仍可正常使用
- **操作**: 使用第一台設備 ID 調用 `check_usage_before_recording` API
- **預期結果**: 服務調用成功，可以正常使用

**成功標準**: 所有 4 個步驟都通過

---

### 🎙️ **場景 4: 錄音功能配額檢查**

**業務目標**: 驗證使用量追蹤和配額管理的準確性

**前置條件**:
- 用戶已註冊並有活躍設備
- 用戶有可用配額

**測試步驟**:

#### 準備階段: 創建用戶和設備
- 創建測試用戶
- 註冊測試設備

#### 步驟 1: 使用前配額檢查
- **操作**: 調用 `check_usage_before_recording` API
- **輸入**: 預估使用時長 300 秒
- **預期結果**: 配額檢查通過，返回剩餘配額

#### 步驟 2: 提交使用記錄
- **操作**: 調用 `submit_usage` API
- **輸入數據**:
  ```javascript
  {
    "duration_seconds": 300,
    "feature_type": "ai-speech-to-text", 
    "device_id": "device_{user_id}",
    "platform": "windows"
  }
  ```
- **預期結果**: 使用記錄提交成功

#### 步驟 3: 使用後配額檢查
- **操作**: 再次調用 `check_usage_before_recording` API
- **預期結果**: 
  - 剩餘配額正確減少
  - 剩餘時間 < 原始限制（FREE 計劃 600 秒）

#### 步驟 4: 驗證數據一致性
- **操作**: 檢查 Firestore 中的數據一致性
- **預期結果**:
  - subscriptions 表中的使用量計數器更新
  - usage_logs 表中有對應的使用記錄
  - 兩者數據一致

**成功標準**: 所有 4 個步驟都通過

---

## 📊 **測試成功標準**

### 整體成功標準
- **通過率**: 100% (5/5 場景通過)
- **數據一致性**: 所有表間數據保持同步
- **錯誤處理**: 異常情況得到正確處理
- **性能要求**: 每個 API 調用響應時間 < 2 秒

### 關鍵驗證點
1. **用戶生命週期**: 從註冊到使用的完整流程
2. **設備管理**: 設備註冊、驗證、限制控制
3. **訂閱管理**: 計劃限制、狀態檢查
4. **使用量追蹤**: 配額檢查、使用記錄、數據同步
5. **安全控制**: 未授權訪問的正確拒絕

---

## 🔧 **測試執行**

### 執行命令
```bash
python test_business_workflows.py
```

### 測試報告
測試完成後會生成詳細報告，包括：
- 每個場景的執行結果
- 失敗步驟的詳細錯誤信息
- 整體成功率統計
- 數據一致性驗證結果

### 故障排除
如果測試失敗，檢查：
1. Firebase 連接配置
2. 數據庫權限設置
3. 函數實現邏輯
4. 數據架構一致性

這些測試案例確保 SpeechPilot 的核心業務流程在各種情況下都能正確運行！
