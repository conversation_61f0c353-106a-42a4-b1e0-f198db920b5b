"""
增強的測試日誌記錄器
提供詳細的測試執行記錄和數據庫變更追蹤
"""

import os
import sys
import time
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
import json

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend-api'))

# 初始化 Firebase
try:
    from load_env import load_env_file
    load_env_file()
    
    import firebase_admin
    from firebase_admin import credentials, firestore
    
    if not firebase_admin._apps:
        # 使用預設憑證
        cred = credentials.ApplicationDefault()
        firebase_admin.initialize_app(cred, {
            'projectId': 'speakoneai-dev-9f995',
            'databaseURL': 'https://speakoneai-dev-9f995-default-rtdb.firebaseio.com'
        })
    
    db = firestore.client()
    print("✅ Firebase Admin SDK 初始化成功")
    
except Exception as e:
    print(f"⚠️  Firebase Admin SDK 初始化失敗: {str(e)}")
    db = None

class TestLogger:
    """增強的測試日誌記錄器"""
    
    def __init__(self):
        self.scenarios = []
        self.current_scenario = None
        self.webhook_calls = []
        self.database_changes = []
        self.assertions = []
        self.start_time = datetime.now(timezone.utc)
        
    def start_scenario(self, name: str, description: str):
        """開始新的測試場景"""
        self.current_scenario = {
            'name': name,
            'description': description,
            'start_time': datetime.now(timezone.utc),
            'end_time': None,
            'duration': None,
            'status': 'running',
            'error': None
        }
        self.scenarios.append(self.current_scenario)
        print(f"\n🧪 開始測試場景: {name}")
        print(f"📝 描述: {description}")
        
    def end_scenario(self, success: bool, error: str = None):
        """結束當前測試場景"""
        if self.current_scenario:
            self.current_scenario['end_time'] = datetime.now(timezone.utc)
            self.current_scenario['duration'] = (
                self.current_scenario['end_time'] - self.current_scenario['start_time']
            ).total_seconds()
            self.current_scenario['status'] = 'passed' if success else 'failed'
            self.current_scenario['error'] = error
            
            status_emoji = "✅" if success else "❌"
            print(f"{status_emoji} 測試場景完成: {self.current_scenario['name']}")
            print(f"⏱️  耗時: {self.current_scenario['duration']:.2f} 秒")
            
            if error:
                print(f"❌ 錯誤: {error}")
                
    def log_webhook_call(self, event_type: str, data: Dict[str, Any], description: str):
        """記錄 Webhook 調用"""
        webhook_call = {
            'timestamp': datetime.now(timezone.utc),
            'event_type': event_type,
            'data': data,
            'description': description
        }
        self.webhook_calls.append(webhook_call)
        
        # 格式化時間戳顯示
        time_str = webhook_call['timestamp'].strftime('%H:%M:%S')
        print(f"🔔 Webhook 調用 ({time_str}): {event_type}")
        print(f"   {description}")
        
    def log_database_change(self, table: str, operation: str, before: Dict[str, Any], 
                          after: Dict[str, Any], user_id: str):
        """記錄數據庫變更"""
        change = {
            'timestamp': datetime.now(timezone.utc),
            'table': table,
            'operation': operation,
            'before': before,
            'after': after,
            'user_id': user_id
        }
        self.database_changes.append(change)
        
        # 格式化時間戳顯示
        time_str = change['timestamp'].strftime('%H:%M:%S')
        print(f"💾 數據庫變更 ({time_str}): {table} - {operation}")
        print(f"   用戶: {user_id}")
        
        # 顯示變更詳情
        if operation == 'INSERT':
            if table == 'subscriptions':
                print(f"   訂閱變更:")
                print(f"   plan: N/A → {after.get('plan', 'N/A')}")
                print(f"   status: N/A → {after.get('status', 'N/A')}")
                print(f"   daily_limit_seconds: N/A → {after.get('daily_limit_seconds', 'N/A')}")
                print(f"   max_devices: N/A → {after.get('max_devices', 'N/A')}")
                print(f"   current_day_used_seconds: N/A → {after.get('current_day_used_seconds', 'N/A')}")
        elif operation == 'UPDATE':
            if table == 'subscriptions':
                print(f"   訂閱變更:")
                for key in ['plan', 'status', 'daily_limit_seconds', 'max_devices', 'current_day_used_seconds']:
                    old_val = before.get(key, 'N/A') if before else 'N/A'
                    new_val = after.get(key, 'N/A') if after else 'N/A'
                    if old_val != new_val:
                        print(f"   {key}: {old_val} → {new_val}")
                        
    def log_assertion(self, description: str, expected: Any, actual: Any, passed: bool):
        """記錄斷言結果"""
        assertion = {
            'timestamp': datetime.now(timezone.utc),
            'description': description,
            'expected': expected,
            'actual': actual,
            'passed': passed
        }
        self.assertions.append(assertion)
        
        status_emoji = "✅" if passed else "❌"
        print(f"{status_emoji} 斷言: {description}")
        if not passed:
            print(f"   期望: {expected}")
            print(f"   實際: {actual}")
            
    def save_results_to_markdown(self):
        """保存測試結果到 Markdown 文件"""
        try:
            # 確保結果目錄存在
            results_dir = os.path.join(os.path.dirname(__file__), 'results')
            os.makedirs(results_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"test_results_{timestamp}.md"
            filepath = os.path.join(results_dir, filename)
            
            # 生成 Markdown 內容
            content = self._generate_markdown_report()
            
            # 寫入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
                
            print(f"📄 測試報告已保存: {filepath}")
            
        except Exception as e:
            print(f"❌ 保存測試報告失敗: {str(e)}")
            
    def _generate_markdown_report(self) -> str:
        """生成 Markdown 測試報告"""
        lines = []
        lines.append("# Webhook 測試報告")
        lines.append("")
        lines.append(f"**測試時間**: {self.start_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        lines.append("")
        
        # 測試場景總結
        lines.append("## 測試場景總結")
        lines.append("")
        for scenario in self.scenarios:
            status_emoji = "✅" if scenario['status'] == 'passed' else "❌"
            lines.append(f"### {status_emoji} {scenario['name']}")
            lines.append(f"**描述**: {scenario['description']}")
            lines.append("")
            lines.append(f"**開始時間**: {scenario['start_time'].strftime('%H:%M:%S')}")
            lines.append(f"**結束時間**: {scenario['end_time'].strftime('%H:%M:%S') if scenario['end_time'] else 'N/A'}")
            lines.append(f"**耗時**: {scenario['duration']:.2f} 秒" if scenario['duration'] else "N/A")
            lines.append(f"**狀態**: {scenario['status']}")
            if scenario['error']:
                lines.append(f"**錯誤**: {scenario['error']}")
            lines.append("")
            
        # Webhook 調用記錄
        if self.webhook_calls:
            lines.append("## 🔔 Webhook 調用記錄")
            for call in self.webhook_calls:
                time_str = call['timestamp'].strftime('%H:%M:%S')
                lines.append(f"**{call['event_type']}** ({time_str})")
                lines.append("")
                lines.append(f"**描述**: {call['description']}")
                lines.append("")
                
        # 數據庫變更記錄
        if self.database_changes:
            lines.append("## 💾 數據庫變更記錄")
            for change in self.database_changes:
                time_str = change['timestamp'].strftime('%H:%M:%S')
                lines.append(f"**{change['table']} 表 - {change['operation']}** ({time_str})")
                lines.append("")
                lines.append(f"**用戶**: {change['user_id']}")
                
                if change['operation'] == 'INSERT' and change['table'] == 'subscriptions':
                    lines.append("**訂閱變更**:")
                    after = change['after']
                    lines.append(f"- plan: N/A → {after.get('plan', 'N/A')}")
                    lines.append(f"- status: N/A → {after.get('status', 'N/A')}")
                    lines.append(f"- daily_limit_seconds: N/A → {after.get('daily_limit_seconds', 'N/A')}")
                    lines.append(f"- max_devices: N/A → {after.get('max_devices', 'N/A')}")
                    lines.append(f"- current_day_used_seconds: N/A → {after.get('current_day_used_seconds', 'N/A')}")
                lines.append("")
                
        return "\n".join(lines)

# 創建全局測試日誌記錄器實例
test_logger = TestLogger()
