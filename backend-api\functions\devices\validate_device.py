"""
驗證設備
檢查設備是否有權限使用服務
"""

import logging
from datetime import datetime
from typing import Dict, Any
from firebase_functions import https_fn
from firebase_admin import firestore

logger = logging.getLogger(__name__)

@https_fn.on_request(cors=https_fn.CorsOptions(
    cors_origins=["*"],
    cors_methods=["GET", "POST", "OPTIONS"]
))
def validate_device_handler(req: https_fn.Request, context=None) -> Dict[str, Any]:
    """驗證設備處理器"""
    try:
        # 獲取用戶 ID
        if context and hasattr(context, 'auth') and context.auth:
            user_id = context.auth.uid
        else:
            # 測試模式：從請求中獲取用戶 ID
            if req.method == 'POST':
                data = req.get_json()
                user_id = data.get('user_id') if data else None
            else:
                user_id = req.args.get('user_id')
        
        if not user_id:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.UNAUTHENTICATED,
                message='用戶未認證'
            )

        # 獲取請求數據
        if req.method == 'POST':
            data = req.get_json()
        else:
            data = dict(req.args)
        
        device_id = data.get('device_id')
        platform = data.get('platform')
        
        if not device_id:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message='缺少設備 ID'
            )

        # 獲取 Firestore 客戶端（環境感知）
        from ..utils.environment import get_db
        db = get_db()
        
        # 查找設備
        device_query = db.collection('devices').where('device_id', '==', device_id).where('user_id', '==', user_id)
        device_docs = device_query.get()
        
        if not device_docs:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.NOT_FOUND,
                message='設備不存在或未授權'
            )
        
        device_doc = device_docs[0]
        device_data = device_doc.to_dict()
        
        # 檢查設備狀態
        if not device_data.get('is_active'):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.PERMISSION_DENIED,
                message='設備已停用'
            )
        
        if not device_data.get('is_authorized'):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.PERMISSION_DENIED,
                message='設備未授權'
            )
        
        # 獲取用戶訂閱資訊
        user_ref = db.collection('users').document(user_id)
        user_doc = user_ref.get()
        
        if not user_doc.exists:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.NOT_FOUND,
                message='用戶不存在'
            )
        
        user_data = user_doc.to_dict()
        subscription_id = user_data.get('current_subscription_id', f"sub_{user_id}")
        
        # 獲取訂閱資訊
        subscription_ref = db.collection('subscriptions').document(subscription_id)
        subscription_doc = subscription_ref.get()
        
        if not subscription_doc.exists:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.NOT_FOUND,
                message='訂閱不存在'
            )
        
        subscription_data = subscription_doc.to_dict()
        
        # 檢查訂閱狀態
        if subscription_data.get('status') != 'active':
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.PERMISSION_DENIED,
                message='訂閱未激活'
            )
        
        # 更新設備最後活躍時間
        now = datetime.utcnow()
        device_doc.reference.update({
            'last_active_at': now,
            'updated_at': now
        })
        
        response = {
            'success': True,
            'data': {
                'device': {
                    'device_id': device_data.get('device_id'),
                    'is_authorized': True,
                    'is_active': device_data.get('is_active'),
                    'last_active_at': now.isoformat() + 'Z',
                    'device_name': device_data.get('device_name'),
                    'platform': device_data.get('platform')
                },
                'subscription': {
                    'plan': subscription_data.get('plan'),
                    'status': subscription_data.get('status'),
                    'daily_limit_seconds': subscription_data.get('daily_limit_seconds'),
                    'monthly_limit_seconds': subscription_data.get('monthly_limit_seconds')
                }
            },
            'message': '設備驗證成功'
        }
        
        logger.info(f"✅ 設備驗證成功: {device_id} ({user_id})")
        return response
        
    except https_fn.HttpsError:
        raise
    except Exception as e:
        logger.error(f"❌ 設備驗證失敗: {str(e)}")
        raise https_fn.HttpsError(
            code=https_fn.FunctionsErrorCode.INTERNAL,
            message='內部錯誤'
        )
