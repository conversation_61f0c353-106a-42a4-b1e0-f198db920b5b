# 🧪 SpeakOneAI Stripe Webhook 測試結果報告

**生成時間**: 2025-07-28 09:39:20

## 📊 測試總結

- **總場景數**: 4
- **通過場景**: 1 ✅
- **失敗場景**: 3 ❌
- **成功率**: 25.0%

## 📋 詳細測試結果

### 1. ❌ 訂閱創建 + 支付成功流程

**描述**: 測試用戶從 FREE 升級到 PRO 計劃的完整流程，包括訂閱創建和首次支付

**開始時間**: 09:39:18
**結束時間**: 09:39:19
**耗時**: 0.84 秒
**狀態**: failed

**錯誤信息**: 400 The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/speakoneai-dev-9f995/firestore/indexes?create_composite=ClZwcm9qZWN0cy9zcGVha29uZWFpLWRldi05Zjk5NS9kYXRhYmFzZXMvKGRlZmF1bHQpL2NvbGxlY3Rpb25Hcm91cHMvcHVyY2hhc2VzL2luZGV4ZXMvXxABGgsKB3VzZXJfaWQQARoOCgpjcmVhdGVkX2F0EAEaDAoIX19uYW1lX18QAQ

#### 🔔 Webhook 調用記錄

1. **customer.subscription.created** (09:39:18)
   - 描述: 用戶 create_pay_test_1753666758 創建 PRO 計劃訂閱

2. **invoice.payment_succeeded** (09:39:19)
   - 描述: 用戶 create_pay_test_1753666758 支付成功，金額 $19.99

#### 💾 數據庫變更記錄

1. **users** 表 - INSERT (09:39:18)
   - 用戶: create_pay_test_1753666758

2. **subscriptions** 表 - INSERT (09:39:18)
   - 用戶: create_pay_test_1753666758
   - 訂閱變更:
     - plan: N/A → FREE
     - status: N/A → active
     - daily_limit_seconds: N/A → 600
     - max_devices: N/A → 1
     - current_day_used_seconds: N/A → 0

3. **subscriptions** 表 - UPDATE (09:39:18)
   - 用戶: create_pay_test_1753666758
   - 訂閱變更:
     - plan: FREE → PRO
     - daily_limit_seconds: 600 → 10800
     - max_devices: 1 → 2

#### 🔍 斷言結果

1. ✅ 初始計劃應該是 FREE
   - 期望: FREE
   - 實際: FREE

2. ✅ 訂閱創建後計劃應該是 PRO
   - 期望: PRO
   - 實際: PRO

3. ✅ 訂閱創建後配額應該是 10800s (3小時)
   - 期望: 10800
   - 實際: 10800

4. ✅ 訂閱創建後設備限制應該是 2
   - 期望: 2
   - 實際: 2

---

### 2. ❌ 訂閱升級 + 支付成功流程

**描述**: 測試用戶從 PRO 升級到 PREMIUM 計劃，包括計劃變更和支付處理

**開始時間**: 09:39:19
**結束時間**: 09:39:19
**耗時**: 0.32 秒
**狀態**: failed

**錯誤信息**: 400 The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/speakoneai-dev-9f995/firestore/indexes?create_composite=ClZwcm9qZWN0cy9zcGVha29uZWFpLWRldi05Zjk5NS9kYXRhYmFzZXMvKGRlZmF1bHQpL2NvbGxlY3Rpb25Hcm91cHMvcHVyY2hhc2VzL2luZGV4ZXMvXxABGgsKB3VzZXJfaWQQARoOCgpjcmVhdGVkX2F0EAEaDAoIX19uYW1lX18QAQ

#### 🔔 Webhook 調用記錄

1. **customer.subscription.created** (09:39:19)
   - 描述: 用戶 upgrade_pay_test_1753666759 創建 PRO 計劃訂閱

2. **customer.subscription.updated** (09:39:19)
   - 描述: 用戶 upgrade_pay_test_1753666759 計劃變更: PRO → PREMIUM

3. **invoice.payment_succeeded** (09:39:19)
   - 描述: 用戶 upgrade_pay_test_1753666759 支付成功，金額 $59.99

#### 💾 數據庫變更記錄

1. **users** 表 - INSERT (09:39:19)
   - 用戶: upgrade_pay_test_1753666759

2. **subscriptions** 表 - INSERT (09:39:19)
   - 用戶: upgrade_pay_test_1753666759
   - 訂閱變更:
     - plan: N/A → FREE
     - status: N/A → active
     - daily_limit_seconds: N/A → 600
     - max_devices: N/A → 1
     - current_day_used_seconds: N/A → 0

3. **subscriptions** 表 - UPDATE (09:39:19)
   - 用戶: upgrade_pay_test_1753666759
   - 訂閱變更:
     - plan: FREE → PRO
     - daily_limit_seconds: 600 → 10800
     - max_devices: 1 → 2

4. **subscriptions** 表 - UPDATE (09:39:19)
   - 用戶: upgrade_pay_test_1753666759
   - 訂閱變更:
     - plan: PRO → PREMIUM
     - daily_limit_seconds: 10800 → 28800
     - max_devices: 2 → 5
     - current_day_used_seconds: 3600 → 0

#### 🔍 斷言結果

1. ✅ 升級前計劃應該是 PRO
   - 期望: PRO
   - 實際: PRO

2. ✅ 升級後計劃應該是 PREMIUM
   - 期望: PREMIUM
   - 實際: PREMIUM

3. ✅ 升級後配額應該增加到 28800s (8小時)
   - 期望: 28800
   - 實際: 28800

4. ✅ 升級後設備限制應該增加到 5
   - 期望: 5
   - 實際: 5

5. ✅ 升級時使用量應該重置為 0
   - 期望: 0
   - 實際: 0

---

### 3. ✅ 訂閱取消流程

**描述**: 測試用戶取消 PREMIUM 訂閱，自動降級到 FREE 計劃

**開始時間**: 09:39:19
**結束時間**: 09:39:19
**耗時**: 0.33 秒
**狀態**: passed

#### 🔔 Webhook 調用記錄

1. **customer.subscription.created** (09:39:19)
   - 描述: 用戶 cancel_test_1753666759 創建 PREMIUM 計劃訂閱

2. **customer.subscription.deleted** (09:39:19)
   - 描述: 用戶 cancel_test_1753666759 訂閱取消，降級到 FREE 計劃

#### 💾 數據庫變更記錄

1. **users** 表 - INSERT (09:39:19)
   - 用戶: cancel_test_1753666759

2. **subscriptions** 表 - INSERT (09:39:19)
   - 用戶: cancel_test_1753666759
   - 訂閱變更:
     - plan: N/A → FREE
     - status: N/A → active
     - daily_limit_seconds: N/A → 600
     - max_devices: N/A → 1
     - current_day_used_seconds: N/A → 0

3. **subscriptions** 表 - UPDATE (09:39:19)
   - 用戶: cancel_test_1753666759
   - 訂閱變更:
     - plan: FREE → PREMIUM
     - daily_limit_seconds: 600 → 28800
     - max_devices: 1 → 5

4. **subscriptions** 表 - UPDATE (09:39:19)
   - 用戶: cancel_test_1753666759
   - 訂閱變更:
     - plan: PREMIUM → FREE
     - status: active → canceled
     - daily_limit_seconds: 28800 → 600
     - max_devices: 5 → 1
     - current_day_used_seconds: 7200 → 0

#### 🔍 斷言結果

1. ✅ 取消前計劃應該是 PREMIUM
   - 期望: PREMIUM
   - 實際: PREMIUM

2. ✅ 取消前應該有 3 個設備
   - 期望: 3
   - 實際: 3

3. ✅ 取消後計劃應該降級到 FREE
   - 期望: FREE
   - 實際: FREE

4. ✅ 取消後狀態應該是 canceled
   - 期望: canceled
   - 實際: canceled

5. ✅ 取消後配額應該降到 600s (10分鐘)
   - 期望: 600
   - 實際: 600

6. ✅ 取消後設備限制應該降到 1
   - 期望: 1
   - 實際: 1

7. ✅ 取消後使用量應該重置為 0
   - 期望: 0
   - 實際: 0

8. ✅ 取消後 Stripe 訂閱 ID 應該清空
   - 期望: None
   - 實際: None

---

### 4. ❌ 多次支付記錄追蹤

**描述**: 測試用戶多次支付的購買記錄正確創建和追蹤

**開始時間**: 09:39:19
**結束時間**: 09:39:20
**耗時**: 0.34 秒
**狀態**: failed

**錯誤信息**: 400 The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/speakoneai-dev-9f995/firestore/indexes?create_composite=ClZwcm9qZWN0cy9zcGVha29uZWFpLWRldi05Zjk5NS9kYXRhYmFzZXMvKGRlZmF1bHQpL2NvbGxlY3Rpb25Hcm91cHMvcHVyY2hhc2VzL2luZGV4ZXMvXxABGgsKB3VzZXJfaWQQARoOCgpjcmVhdGVkX2F0EAEaDAoIX19uYW1lX18QAQ

#### 🔔 Webhook 調用記錄

1. **customer.subscription.created** (09:39:19)
   - 描述: 用戶 multi_pay_test_1753666759 創建 PRO 計劃訂閱

2. **invoice.payment_succeeded** (09:39:20)
   - 描述: 用戶 multi_pay_test_1753666759 支付成功，金額 $19.99

#### 💾 數據庫變更記錄

1. **users** 表 - INSERT (09:39:19)
   - 用戶: multi_pay_test_1753666759

2. **subscriptions** 表 - INSERT (09:39:19)
   - 用戶: multi_pay_test_1753666759
   - 訂閱變更:
     - plan: N/A → FREE
     - status: N/A → active
     - daily_limit_seconds: N/A → 600
     - max_devices: N/A → 1
     - current_day_used_seconds: N/A → 0

3. **subscriptions** 表 - UPDATE (09:39:20)
   - 用戶: multi_pay_test_1753666759
   - 訂閱變更:
     - plan: FREE → PRO
     - daily_limit_seconds: 600 → 10800
     - max_devices: 1 → 2

---

