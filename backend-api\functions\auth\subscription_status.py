"""
檢查用戶訂閱狀態
返回用戶計劃和使用限制
"""

import logging
from datetime import datetime
from typing import Dict, Any
from firebase_functions import https_fn
from firebase_admin import firestore
from ..utils.constants import SUBSCRIPTION_PLANS
from ..utils.error_handler import handle_error, handle_validation_error, log_function_start, log_function_success

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端"""
    return firestore.client()

def check_user_subscription_status_handler(req: https_fn.CallableRequest) -> Dict[str, Any]:
    """
    檢查用戶訂閱狀態
    返回用戶計劃和使用限制

    Args:
        req: Firebase Functions 請求對象

    Returns:
        Dict[str, Any]: 訂閱狀態資訊
    """
    try:
        if not req.auth:
            raise handle_validation_error("UNAUTHENTICATED")

        user_id = req.auth.uid
        log_function_start("check_user_subscription_status", user_id)
        db = get_db()
        
        # 獲取用戶資料以找到訂閱ID
        user_doc = db.collection('users').document(user_id).get()
        if not user_doc.exists:
            raise handle_validation_error("USER_NOT_FOUND", details={"user_id": user_id})

        user_data = user_doc.to_dict()
        subscription_id = user_data.get('current_subscription_id')

        if not subscription_id:
            raise handle_validation_error(
                "FAILED_PRECONDITION",
                "用戶沒有有效的訂閱",
                details={"user_id": user_id, "user_data": user_data}
            )

        # 獲取訂閱資訊
        subscription_doc = db.collection('subscriptions').document(subscription_id).get()

        if not subscription_doc.exists:
            raise handle_validation_error(
                "NOT_FOUND",
                "用戶訂閱不存在",
                details={"user_id": user_id, "subscription_id": subscription_id}
            )

        subscription = subscription_doc.to_dict()
        plan_config = SUBSCRIPTION_PLANS[subscription['plan']]

        # 獲取今日使用量
        today = datetime.utcnow().strftime('%Y-%m-%d')
        today_usage_doc = db.collection('usage').document(f"{user_id}_{today}").get()

        today_used = today_usage_doc.to_dict().get('seconds_used', 0) if today_usage_doc.exists else 0

        # 獲取本月使用量（簡化查詢避免複合索引）
        current_month = datetime.utcnow().strftime('%Y-%m')
        monthly_usage_query = db.collection('usage').where(
            'user_id', '==', user_id
        ).stream()

        monthly_used = 0
        for doc in monthly_usage_query:
            doc_data = doc.to_dict()
            doc_date = doc_data.get('date', '')
            # 在客戶端過濾月份
            if doc_date.startswith(current_month):
                monthly_used += doc_data.get('seconds_used', 0)

        # 計算剩餘配額
        daily_limit = subscription['daily_limit_seconds']
        monthly_limit = subscription['monthly_limit_seconds']
        
        daily_remaining = max(0, daily_limit - today_used) if daily_limit > 0 else -1
        monthly_remaining = max(0, monthly_limit - monthly_used) if monthly_limit > 0 else -1

        # 判斷是否可以使用
        can_use_today = daily_remaining > 0 or daily_limit == -1
        can_use_monthly = monthly_remaining > 0 or monthly_limit == -1
        can_use = can_use_today and can_use_monthly

        # 獲取設備資訊（簡化查詢避免複合索引）
        devices_query = db.collection('devices').where(
            'user_id', '==', user_id
        ).stream()

        device_count = 0
        for device_doc in devices_query:
            device_data = device_doc.to_dict()
            # 在客戶端過濾活躍設備
            if device_data.get('is_active', False):
                device_count += 1

        response = {
            'success': True,
            'data': {
                'subscription': {
                    'plan': subscription['plan'],
                    'status': subscription['status'],
                    'daily_limit_seconds': daily_limit,
                    'monthly_limit_seconds': monthly_limit,
                    'max_devices': subscription['max_devices'],
                    'supported_platforms': subscription['supported_platforms']
                },
                'usage': {
                    'today_used': today_used,
                    'monthly_used': monthly_used,
                    'daily_remaining': daily_remaining,
                    'monthly_remaining': monthly_remaining
                },
                'limits': {
                    'can_use': can_use,
                    'can_use_today': can_use_today,
                    'can_use_monthly': can_use_monthly,
                    'needs_upgrade': not can_use and subscription['plan'] != 'MAX'
                },
                'devices': {
                    'current_count': device_count,
                    'max_devices': subscription['max_devices'],
                    'can_add_device': device_count < subscription['max_devices'] or subscription['max_devices'] == -1
                },
                'plan_features': plan_config.get('features', [])
            }
        }

        log_function_success("check_user_subscription_status", user_id, plan=subscription['plan'])
        return response

    except https_fn.HttpsError:
        raise
    except Exception as e:
        raise handle_error(
            error=e,
            error_code="INTERNAL_ERROR",
            user_message="檢查訂閱狀態時發生錯誤",
            context={"user_id": user_id, "function": "check_user_subscription_status"}
        )

def get_user_info_enhanced_handler(req: https_fn.CallableRequest) -> Dict[str, Any]:
    """
    獲取用戶資訊 - 增強版

    Args:
        req: Firebase Functions 請求對象

    Returns:
        Dict[str, Any]: 用戶資訊
    """
    try:
        if not req.auth:
            raise handle_validation_error("UNAUTHENTICATED")

        user_id = req.auth.uid
        log_function_start("get_user_info_enhanced", user_id)
        db = get_db()

        # 獲取用戶和訂閱資訊
        user_doc = db.collection('users').document(user_id).get()
        subscription_doc = db.collection('subscriptions').document(user_id).get()

        if not user_doc.exists:
            raise handle_validation_error("USER_NOT_FOUND", details={"user_id": user_id})

        user_data = user_doc.to_dict()
        subscription_data = subscription_doc.to_dict() if subscription_doc.exists else None

        # 獲取今日使用量
        today = datetime.utcnow().strftime('%Y-%m-%d')
        usage_doc = db.collection('usage').document(f"{user_id}_{today}").get()

        today_usage = usage_doc.to_dict().get('seconds_used', 0) if usage_doc.exists else 0

        response = {
            'success': True,
            'data': {
                'user': user_data,
                'subscription': subscription_data,
                'usage': {
                    'today_used': today_usage,
                    'remaining_today': max(0, subscription_data['daily_limit_seconds'] - today_usage) if subscription_data and subscription_data['daily_limit_seconds'] > 0 else -1
                }
            }
        }

        log_function_success("get_user_info_enhanced", user_id)
        return response

    except https_fn.HttpsError:
        raise
    except Exception as e:
        raise handle_error(
            error=e,
            error_code="INTERNAL_ERROR",
            user_message="獲取用戶資訊時發生錯誤",
            context={"user_id": user_id, "function": "get_user_info_enhanced"}
        )
