"""
Validation utilities for SpeechPilot Firebase Functions
"""

import re
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from firebase_functions import https_fn
from firebase_admin import firestore

from .constants import Platform, ERROR_MESSAGES

def validate_auth(request: https_fn.CallableRequest) -> str:
    """
    驗證用戶認證並返回用戶ID
    
    Args:
        request: Firebase Functions 請求對象
        
    Returns:
        str: 用戶ID
        
    Raises:
        https_fn.HttpsError: 如果用戶未認證
    """
    if not request.auth:
        raise https_fn.HttpsError(
            code=https_fn.FunctionsErrorCode.UNAUTHENTICATED,
            message=ERROR_MESSAGES["UNAUTHENTICATED"]
        )
    
    return request.auth.uid

def validate_device_id(device_id: str) -> bool:
    """
    驗證設備ID格式
    
    Args:
        device_id: 設備ID
        
    Returns:
        bool: 是否有效
    """
    if not device_id or not isinstance(device_id, str):
        return False
    
    # 設備ID應該是UUID格式或其他有效格式
    uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    return bool(re.match(uuid_pattern, device_id, re.IGNORECASE)) or len(device_id) >= 10

def validate_platform(platform: str) -> bool:
    """
    驗證平台名稱

    Args:
        platform: 平台名稱

    Returns:
        bool: 是否有效
    """
    # 標準化平台名稱
    platform_mapping = {
        'win32': 'windows',
        'darwin': 'macos',
        'android': 'android',
        'ios': 'ios',
        'linux': 'linux'
    }

    # 如果是映射的平台名稱，轉換為標準名稱
    normalized_platform = platform_mapping.get(platform.lower(), platform.lower())

    try:
        Platform(normalized_platform)
        return True
    except ValueError:
        return False

def normalize_platform(platform: str) -> str:
    """
    標準化平台名稱

    Args:
        platform: 原始平台名稱

    Returns:
        str: 標準化的平台名稱
    """
    platform_mapping = {
        'win32': 'windows',
        'darwin': 'macos',
        'linux': 'linux'
    }

    return platform_mapping.get(platform.lower(), platform.lower())

def validate_timestamp(timestamp: Any) -> bool:
    """
    驗證時間戳
    
    Args:
        timestamp: 時間戳對象
        
    Returns:
        bool: 是否有效
    """
    if isinstance(timestamp, firestore.SERVER_TIMESTAMP.__class__):
        return True
    
    if hasattr(timestamp, 'seconds') and hasattr(timestamp, 'nanoseconds'):
        return True
    
    if isinstance(timestamp, datetime):
        return True
    
    return False

def validate_usage_submission(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    驗證使用量提交數據
    
    Args:
        data: 提交的數據
        
    Returns:
        Dict[str, Any]: 驗證結果
    """
    errors = []
    
    # 檢查必要欄位
    required_fields = ['device_id', 'duration_seconds']
    for field in required_fields:
        if field not in data:
            errors.append(f"Missing required field: {field}")
    
    # 驗證設備ID
    if 'device_id' in data and not validate_device_id(data['device_id']):
        errors.append("Invalid device_id format")
    
    # 驗證平台
    if 'platform' in data and not validate_platform(data['platform']):
        errors.append("Invalid platform")
    
    # 驗證使用時間
    if 'duration_seconds' in data:
        duration_seconds = data['duration_seconds']
        if not isinstance(duration_seconds, (int, float)) or duration_seconds < 0:
            errors.append("Invalid duration_seconds: must be a non-negative number")
        elif duration_seconds > 24 * 60 * 60:  # 不能超過24小時
            errors.append("Invalid duration_seconds: cannot exceed 24 hours")
    
    # 驗證時間戳（可選）
    if 'session_start' in data and not validate_timestamp(data['session_start']):
        errors.append("Invalid session_start timestamp")

    if 'session_end' in data and not validate_timestamp(data['session_end']):
        errors.append("Invalid session_end timestamp")

    # 驗證時間邏輯（可選）
    if ('session_start' in data and 'session_end' in data and
        'seconds_used' in data):
        try:
            start = data['session_start']
            end = data['session_end']
            
            # 轉換為 datetime 對象進行比較
            if hasattr(start, 'seconds'):
                start_dt = datetime.fromtimestamp(start.seconds, tz=timezone.utc)
            else:
                start_dt = start
                
            if hasattr(end, 'seconds'):
                end_dt = datetime.fromtimestamp(end.seconds, tz=timezone.utc)
            else:
                end_dt = end
            
            if start_dt >= end_dt:
                errors.append("session_start must be before session_end")
            
            # 檢查計算的時間差是否合理
            calculated_seconds = (end_dt - start_dt).total_seconds()
            reported_seconds = data['seconds_used']
            
            # 允許一定的誤差（10%或最少5秒）
            tolerance = max(5, calculated_seconds * 0.1)
            if abs(calculated_seconds - reported_seconds) > tolerance:
                errors.append(f"Time mismatch: calculated {calculated_seconds}s, reported {reported_seconds}s")
                
        except Exception as e:
            errors.append(f"Error validating timestamps: {str(e)}")
    
    return {
        "is_valid": len(errors) == 0,
        "errors": errors,
        "adjusted_seconds": None  # 可以在這裡添加調整後的秒數
    }

def validate_features(features: Optional[List[str]]) -> bool:
    """
    驗證功能列表
    
    Args:
        features: 功能列表
        
    Returns:
        bool: 是否有效
    """
    if features is None:
        return True
    
    if not isinstance(features, list):
        return False
    
    valid_features = [
        'ai-speech-to-text',
        'direct-speech-to-text',
        'real-time-transcription',
        'voice-commands'
    ]
    
    return all(feature in valid_features for feature in features)
