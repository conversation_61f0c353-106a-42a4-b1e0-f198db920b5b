"""
Usage submission functionality for SpeechPilot Firebase Functions
"""

import logging
from firebase_functions import https_fn
from firebase_admin import firestore
from typing import Dict, Any

from ..utils.validation import validate_auth, validate_usage_submission
from ..utils.database import get_user_data, check_device_exists, create_usage_log_and_update_counters, check_subscription_usage_limits
from ..utils.constants import ERROR_MESSAGES
from ..utils.error_handler import handle_error, handle_validation_error, log_function_start, log_function_success

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端（環境感知）"""
    from ..utils.environment import get_db as get_env_db
    return get_env_db()

def submit_usage_handler(request: https_fn.CallableRequest) -> Dict[str, Any]:
    """
    提交使用量記錄（保留原有函數以向後兼容）

    Args:
        request: Firebase Functions 請求對象

    Returns:
        Dict[str, Any]: 提交結果
    """
    user_id = None  # 初始化 user_id 變數
    try:
        # 驗證用戶認證
        user_id = validate_auth(request)

        # 獲取請求數據
        submission = request.data or {}

        logger.info(f"Submitting usage for user {user_id}")

        # 驗證提交數據
        validation_result = validate_usage_submission(submission)

        if not validation_result["is_valid"]:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message=f"Invalid submission: {', '.join(validation_result['errors'])}"
            )

        device_id = submission.get('device_id')

        # 檢查設備是否存在且屬於用戶
        if not check_device_exists(user_id, device_id):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.NOT_FOUND,
                message=ERROR_MESSAGES["DEVICE_NOT_FOUND"]
            )

        # 獲取用戶資料
        user_data = get_user_data(user_id)
        subscription_id = user_data.get('current_subscription_id')

        if not subscription_id:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.FAILED_PRECONDITION,
                message="用戶沒有有效的訂閱"
            )

        duration_seconds = submission.get('duration_seconds', 0)

        # 檢查使用限制
        usage_check = check_subscription_usage_limits(subscription_id, duration_seconds)

        if not usage_check['can_use']:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.RESOURCE_EXHAUSTED,
                message=usage_check['reason'],
                details={
                    'daily_remaining': usage_check['daily_remaining'],
                    'daily_limit': usage_check['daily_limit']
                }
            )

        # 創建使用記錄並更新計數器（原子操作）
        feature_type = submission.get('feature_type', 'ai-speech-to-text')
        platform = submission.get('platform', 'windows')
        token_usage = submission.get('token_usage', {})  # OpenAI token 使用量
        session_metadata = submission.get('session_metadata', {})  # 會話元數據

        result = create_usage_log_and_update_counters(
            user_id=user_id,
            subscription_id=subscription_id,
            device_id=device_id,
            duration_seconds=duration_seconds,
            feature_type=feature_type,
            platform=platform,
            token_usage=token_usage,
            session_metadata=session_metadata
        )

        # 更新設備活動時間
        db = get_db()
        device_query = db.collection('devices').where(
            'user_id', '==', user_id
        ).where(
            'device_id', '==', device_id
        ).limit(1).stream()

        for doc in device_query:
            doc.reference.update({
                'last_active_at': firestore.SERVER_TIMESTAMP
            })
            break

        # 獲取更新後的訂閱資訊（只信任 subscription 表）
        db = get_db()
        subscription_doc = db.collection('subscriptions').document(subscription_id).get()
        subscription_data = subscription_doc.to_dict() if subscription_doc.exists else {}

        response = {
            "success": True,
            "data": {
                "usage_log_id": result["usage_log_id"],
                "duration_recorded": duration_seconds,
                # Token 使用量 (頂層欄位)
                "prompt_tokens": result.get("prompt_tokens", 0),
                "completion_tokens": result.get("completion_tokens", 0),
                "input_tokens": result.get("input_tokens", 0),
                "output_tokens": result.get("output_tokens", 0),
                "total_tokens": result.get("total_tokens", 0),
                # 只信任 subscription 表的欄位
                "current_day_used_seconds": subscription_data.get("current_day_used_seconds", 0),
                "daily_limit_seconds": subscription_data.get("daily_limit_seconds", 600)
            },
            "message": f"使用量記錄成功：{duration_seconds} 秒"
        }

        logger.info(f"Usage submitted successfully for user {user_id}: {duration_seconds}s")

        return response

    except https_fn.HttpsError:
        # 重新拋出 Firebase Functions 錯誤
        raise
    except Exception as e:
        # 安全的錯誤處理，避免 user_id 未定義的問題
        context = {"function": "submit_usage"}
        if user_id:
            context["user_id"] = user_id

        raise handle_error(
            error=e,
            error_code="INTERNAL_ERROR",
            user_message="提交使用量時發生錯誤",
            context=context
        )
