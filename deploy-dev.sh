#!/bin/bash
# Deploy to Development Environment
# Usage: ./deploy-dev.sh

set -e

echo "🚀 Deploying to Development Environment..."

# Configuration
ENVFILE=".env.dev"
RUNTIME="python311"
REGION="asia-east1"

# Check if env file exists
if [ ! -f "$ENVFILE" ]; then
    echo "❌ Error: $ENVFILE not found!"
    echo "Please create $ENVFILE with your development configuration."
    exit 1
fi

# Extract environment variables (skip comments and empty lines)
VARS=$(grep -v '^#' $ENVFILE | grep -v '^$' | xargs | tr ' ' ',')

if [ -z "$VARS" ]; then
    echo "❌ Error: No environment variables found in $ENVFILE"
    exit 1
fi

echo "📋 Environment variables: $VARS"

# Deploy Firebase Functions
echo "🔧 Deploying Firebase Functions..."
gcloud functions deploy backend-api \
  --runtime $RUNTIME \
  --trigger-http \
  --allow-unauthenticated \
  --region $REGION \
  --set-env-vars "$VARS" \
  --memory 256MB \
  --timeout 60s

echo "✅ Development deployment completed!"
echo "🌐 Function URL: https://$REGION-speakoneai-dev-9f995.cloudfunctions.net/backend-api"
