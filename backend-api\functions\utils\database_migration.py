"""
資料庫遷移腳本 - 安全遷移到新架構
"""

import logging
from datetime import datetime
from typing import Dict, Any, List
from firebase_admin import firestore
from .environment import get_db
from .constants import SUBSCRIPTION_PLANS, DeviceStatus
from .timezone_manager import TimezoneManager

logger = logging.getLogger(__name__)

class DatabaseMigration:
    """資料庫遷移管理器"""
    
    def __init__(self):
        self.db = get_db()
        self.timezone_manager = TimezoneManager()
    
    def migrate_to_new_schema(self, dry_run: bool = True) -> Dict[str, Any]:
        """
        遷移到新架構
        
        Args:
            dry_run: 是否為試運行（不實際修改資料）
            
        Returns:
            Dict[str, Any]: 遷移結果
        """
        try:
            migration_log = {
                'started_at': datetime.utcnow(),
                'dry_run': dry_run,
                'users_processed': 0,
                'subscriptions_processed': 0,
                'devices_processed': 0,
                'errors': [],
                'warnings': []
            }
            
            logger.info(f"開始資料庫遷移 (dry_run={dry_run})")
            
            # 1. 遷移用戶表
            self._migrate_users(migration_log, dry_run)
            
            # 2. 遷移訂閱表
            self._migrate_subscriptions(migration_log, dry_run)
            
            # 3. 遷移設備表
            self._migrate_devices(migration_log, dry_run)
            
            # 4. 驗證遷移結果
            if not dry_run:
                self._verify_migration(migration_log)
            
            migration_log['completed_at'] = datetime.utcnow()
            migration_log['duration_seconds'] = (migration_log['completed_at'] - migration_log['started_at']).total_seconds()
            
            logger.info(f"資料庫遷移完成: {migration_log}")
            
            return migration_log
            
        except Exception as e:
            logger.error(f"資料庫遷移失敗: {str(e)}")
            raise
    
    def _migrate_users(self, migration_log: Dict[str, Any], dry_run: bool):
        """遷移用戶表"""
        try:
            users_collection = self.db.collection('users')
            
            for user_doc in users_collection.stream():
                try:
                    user_id = user_doc.id
                    user_data = user_doc.to_dict()
                    
                    # 檢查是否需要遷移
                    needs_migration = self._check_user_migration_needed(user_data)
                    
                    if needs_migration:
                        migrated_data = self._migrate_user_data(user_data)
                        
                        if not dry_run:
                            user_doc.reference.update(migrated_data)
                        
                        logger.info(f"用戶遷移: {user_id} -> {list(migrated_data.keys())}")
                    
                    migration_log['users_processed'] += 1
                    
                except Exception as e:
                    error_msg = f"用戶遷移失敗: {user_doc.id}, 錯誤: {str(e)}"
                    migration_log['errors'].append(error_msg)
                    logger.error(error_msg)
            
        except Exception as e:
            logger.error(f"用戶表遷移失敗: {str(e)}")
            raise
    
    def _migrate_subscriptions(self, migration_log: Dict[str, Any], dry_run: bool):
        """遷移訂閱表"""
        try:
            subscriptions_collection = self.db.collection('subscriptions')
            
            for subscription_doc in subscriptions_collection.stream():
                try:
                    subscription_id = subscription_doc.id
                    subscription_data = subscription_doc.to_dict()
                    
                    # 檢查是否需要遷移
                    needs_migration = self._check_subscription_migration_needed(subscription_data)
                    
                    if needs_migration:
                        migrated_data = self._migrate_subscription_data(subscription_data)
                        
                        if not dry_run:
                            subscription_doc.reference.update(migrated_data)
                        
                        logger.info(f"訂閱遷移: {subscription_id} -> {list(migrated_data.keys())}")
                    
                    migration_log['subscriptions_processed'] += 1
                    
                except Exception as e:
                    error_msg = f"訂閱遷移失敗: {subscription_doc.id}, 錯誤: {str(e)}"
                    migration_log['errors'].append(error_msg)
                    logger.error(error_msg)
            
        except Exception as e:
            logger.error(f"訂閱表遷移失敗: {str(e)}")
            raise
    
    def _migrate_devices(self, migration_log: Dict[str, Any], dry_run: bool):
        """遷移設備表"""
        try:
            devices_collection = self.db.collection('devices')
            
            for device_doc in devices_collection.stream():
                try:
                    device_id = device_doc.id
                    device_data = device_doc.to_dict()
                    
                    # 檢查是否需要遷移
                    needs_migration = self._check_device_migration_needed(device_data)
                    
                    if needs_migration:
                        migrated_data = self._migrate_device_data(device_data)
                        
                        if not dry_run:
                            device_doc.reference.update(migrated_data)
                        
                        logger.info(f"設備遷移: {device_id} -> {list(migrated_data.keys())}")
                    
                    migration_log['devices_processed'] += 1
                    
                except Exception as e:
                    error_msg = f"設備遷移失敗: {device_doc.id}, 錯誤: {str(e)}"
                    migration_log['errors'].append(error_msg)
                    logger.error(error_msg)
            
        except Exception as e:
            logger.error(f"設備表遷移失敗: {str(e)}")
            raise
    
    def _check_user_migration_needed(self, user_data: Dict[str, Any]) -> bool:
        """檢查用戶是否需要遷移"""
        # 檢查是否有舊的冗餘欄位
        redundant_fields = ['daily_limit_seconds', 'current_day_used_seconds', 'device_limit']
        return any(field in user_data for field in redundant_fields)
    
    def _check_subscription_migration_needed(self, subscription_data: Dict[str, Any]) -> bool:
        """檢查訂閱是否需要遷移"""
        # 檢查是否缺少新架構的必要欄位
        required_fields = ['register_region_timezone', 'usage_reset_hour', 'last_daily_reset_date']
        return any(field not in subscription_data for field in required_fields)
    
    def _check_device_migration_needed(self, device_data: Dict[str, Any]) -> bool:
        """檢查設備是否需要遷移"""
        # 檢查是否缺少新的狀態欄位
        return 'status' not in device_data or 'is_authorized' not in device_data
    
    def _migrate_user_data(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """遷移用戶資料"""
        migrated_data = {}
        
        # 移除冗餘的配額欄位（這些現在由訂閱表管理）
        redundant_fields = ['daily_limit_seconds', 'current_day_used_seconds', 'device_limit']
        for field in redundant_fields:
            if field in user_data:
                migrated_data[field] = firestore.DELETE_FIELD
        
        # 確保必要欄位存在
        if 'device_ids' not in user_data:
            migrated_data['device_ids'] = []
        
        if 'last_device_check' not in user_data:
            migrated_data['last_device_check'] = datetime.utcnow()
        
        if 'preferences' not in user_data:
            migrated_data['preferences'] = {
                'language': 'zh-TW',
                'notifications': True,
                'auto_upgrade_prompts': True
            }
        
        migrated_data['updated_at'] = datetime.utcnow()
        
        return migrated_data
    
    def _migrate_subscription_data(self, subscription_data: Dict[str, Any]) -> Dict[str, Any]:
        """遷移訂閱資料"""
        migrated_data = {}
        
        # 添加地區化設定
        if 'register_region_timezone' not in subscription_data:
            migrated_data['register_region_timezone'] = 'UTC+8'
        
        if 'usage_reset_hour' not in subscription_data:
            migrated_data['usage_reset_hour'] = 0
        
        # 統一配額欄位名稱
        if 'daily_usage_seconds' in subscription_data:
            migrated_data['current_day_used_seconds'] = subscription_data['daily_usage_seconds']
            migrated_data['daily_usage_seconds'] = firestore.DELETE_FIELD
        
        # 確保重置日期存在
        if 'last_daily_reset_date' not in subscription_data:
            timezone_str = migrated_data.get('register_region_timezone', 'UTC+8')
            migrated_data['last_daily_reset_date'] = self.timezone_manager.get_local_date(timezone_str)
        
        if 'last_daily_reset_at' not in subscription_data:
            migrated_data['last_daily_reset_at'] = datetime.utcnow()
        
        # 確保設備列表存在
        if 'active_device_ids' not in subscription_data:
            migrated_data['active_device_ids'] = []
        
        if 'last_device_limit_check' not in subscription_data:
            migrated_data['last_device_limit_check'] = datetime.utcnow()
        
        migrated_data['updated_at'] = datetime.utcnow()
        
        return migrated_data
    
    def _migrate_device_data(self, device_data: Dict[str, Any]) -> Dict[str, Any]:
        """遷移設備資料"""
        migrated_data = {}
        
        # 添加新的狀態欄位
        if 'status' not in device_data:
            if device_data.get('is_active', True):
                migrated_data['status'] = DeviceStatus.ACTIVE.value
            else:
                migrated_data['status'] = DeviceStatus.INACTIVE.value
        
        if 'is_authorized' not in device_data:
            migrated_data['is_authorized'] = device_data.get('is_active', True)
        
        # 確保必要欄位存在
        if 'device_fingerprint' not in device_data:
            migrated_data['device_fingerprint'] = ''
        
        if 'user_agent' not in device_data:
            migrated_data['user_agent'] = 'unknown'
        
        migrated_data['updated_at'] = datetime.utcnow()
        
        return migrated_data
    
    def _verify_migration(self, migration_log: Dict[str, Any]):
        """驗證遷移結果"""
        try:
            verification_errors = []
            
            # 檢查用戶表
            users_collection = self.db.collection('users')
            for user_doc in users_collection.limit(10).stream():  # 抽樣檢查
                user_data = user_doc.to_dict()
                
                # 檢查是否還有冗餘欄位
                redundant_fields = ['daily_limit_seconds', 'current_day_used_seconds', 'device_limit']
                for field in redundant_fields:
                    if field in user_data:
                        verification_errors.append(f"用戶 {user_doc.id} 仍有冗餘欄位: {field}")
            
            # 檢查訂閱表
            subscriptions_collection = self.db.collection('subscriptions')
            for subscription_doc in subscriptions_collection.limit(10).stream():  # 抽樣檢查
                subscription_data = subscription_doc.to_dict()
                
                # 檢查必要欄位
                required_fields = ['register_region_timezone', 'usage_reset_hour', 'last_daily_reset_date']
                for field in required_fields:
                    if field not in subscription_data:
                        verification_errors.append(f"訂閱 {subscription_doc.id} 缺少欄位: {field}")
            
            migration_log['verification_errors'] = verification_errors
            
            if verification_errors:
                logger.warning(f"遷移驗證發現問題: {verification_errors}")
            else:
                logger.info("遷移驗證通過")
            
        except Exception as e:
            logger.error(f"遷移驗證失敗: {str(e)}")
            migration_log['verification_errors'] = [f"驗證失敗: {str(e)}"]

# 全域遷移管理器實例
database_migration = DatabaseMigration()
