"""
執行所有訂閱相關測試
包括場景測試、Webhook 整合測試和數據庫驗證
"""

import sys
import os
import time
from datetime import datetime

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend-api'))

def print_header(title: str):
    """打印測試標題"""
    print("\n" + "=" * 80)
    print(f"🧪 {title}")
    print("=" * 80)

def print_section(title: str):
    """打印測試段落"""
    print("\n" + "-" * 60)
    print(f"📋 {title}")
    print("-" * 60)

def main():
    """主測試函數"""
    start_time = time.time()
    
    print_header("SpeakOneAI 訂閱系統全面測試套件")
    print(f"測試開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n🎯 測試範圍:")
    print("  1. 基礎 Webhook 功能測試")
    print("  2. 配額重置邏輯測試")
    print("  3. 訂閱場景測試 (升級、降級、取消)")
    print("  4. Webhook 整合測試 (Stripe 事件處理)")
    print("  5. 統一詳細 Webhook 事件測試 (逐步記錄 + Markdown 報告)")
    print("  6. 數據庫狀態驗證 (Firestore 數據一致性)")
    print("  7. 設備限制管理測試")
    
    all_tests_passed = True
    test_results = {}
    
    # 測試 1: 基礎 Webhook 功能測試
    print_section("1. 基礎 Webhook 功能測試")
    try:
        from test_stripe_webhook import run_stripe_webhook_tests
        webhook_basic_result = run_stripe_webhook_tests()
        test_results['webhook_basic'] = webhook_basic_result
        if not webhook_basic_result:
            all_tests_passed = False
    except Exception as e:
        print(f"❌ 基礎 Webhook 測試執行失敗: {str(e)}")
        test_results['webhook_basic'] = False
        all_tests_passed = False
    
    # 測試 2: 配額重置功能測試
    print_section("2. 配額重置功能測試")
    try:
        from test_daily_quota_reset import run_daily_quota_reset_tests
        quota_reset_result = run_daily_quota_reset_tests()
        test_results['quota_reset'] = quota_reset_result
        if not quota_reset_result:
            all_tests_passed = False
    except Exception as e:
        print(f"❌ 配額重置測試執行失敗: {str(e)}")
        test_results['quota_reset'] = False
        all_tests_passed = False
    
    # 測試 3: 訂閱場景測試（需要 Firebase）
    print_section("3. 訂閱場景測試 (需要 Firebase Admin SDK)")
    try:
        from test_subscription_scenarios import run_subscription_scenario_tests
        scenario_result = run_subscription_scenario_tests()
        test_results['subscription_scenarios'] = scenario_result
        if not scenario_result:
            all_tests_passed = False
    except Exception as e:
        print(f"❌ 訂閱場景測試執行失敗: {str(e)}")
        test_results['subscription_scenarios'] = False
        all_tests_passed = False
    
    # 測試 4: Webhook 整合測試（需要 Firebase）
    print_section("4. Webhook 整合測試 (需要 Firebase Admin SDK)")
    try:
        from test_webhook_integration import run_webhook_integration_tests
        webhook_integration_result = run_webhook_integration_tests()
        test_results['webhook_integration'] = webhook_integration_result
        if not webhook_integration_result:
            all_tests_passed = False
    except Exception as e:
        print(f"❌ Webhook 整合測試執行失敗: {str(e)}")
        test_results['webhook_integration'] = False
        all_tests_passed = False

    # 測試 5: 統一詳細 Webhook 事件測試
    print_section("5. 統一詳細 Webhook 事件測試 (逐步記錄 + Markdown 報告)")
    try:
        import unittest
        from test_unified_detailed_webhook import TestUnifiedDetailedWebhook

        # 創建測試套件
        test_suite = unittest.TestLoader().loadTestsFromTestCase(TestUnifiedDetailedWebhook)

        # 執行測試
        runner = unittest.TextTestRunner(verbosity=1, stream=open(os.devnull, 'w'))
        result = runner.run(test_suite)

        unified_detailed_result = result.wasSuccessful()
        test_results['unified_detailed_webhook'] = unified_detailed_result

        if unified_detailed_result:
            print("✅ 統一詳細 Webhook 事件測試通過")
            print("   - 創建共享測試用戶")
            print("   - 逐步記錄訂閱創建 + 支付流程")
            print("   - 逐步記錄訂閱升級 + 支付流程")
            print("   - 詳細的表查詢和變化比較")
            print("   - 完整的斷言驗證")
            print("   - 自動生成 Markdown 測試報告")
            print("   - 測試數據自動清理")
        else:
            print("❌ 統一詳細 Webhook 事件測試失敗")
            if result.failures:
                print("   失敗的測試:")
                for test, _ in result.failures:
                    print(f"   - {test}")
            if result.errors:
                print("   錯誤的測試:")
                for test, _ in result.errors:
                    print(f"   - {test}")
            all_tests_passed = False

    except Exception as e:
        print(f"❌ 統一詳細 Webhook 事件測試執行失敗: {str(e)}")
        test_results['unified_detailed_webhook'] = False
        all_tests_passed = False
    
    # 測試總結
    end_time = time.time()
    duration = end_time - start_time
    
    print_header("測試總結")
    print(f"測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"總耗時: {duration:.2f} 秒")
    
    print("\n📊 測試結果:")
    for test_name, result in test_results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        test_display_name = {
            'webhook_basic': '基礎 Webhook 功能',
            'quota_reset': '配額重置功能',
            'subscription_scenarios': '訂閱場景測試',
            'webhook_integration': 'Webhook 整合測試',
            'unified_detailed_webhook': '統一詳細 Webhook 事件測試'
        }.get(test_name, test_name)
        print(f"  {test_display_name}: {status}")
    
    if all_tests_passed:
        print("\n🎉 所有測試通過！")
        print("\n✅ 驗證完成的功能:")
        print("  ✅ Stripe Webhook 事件處理")
        print("  ✅ 訂閱生命週期管理")
        print("  ✅ 每日配額重置邏輯")
        print("  ✅ 升級/降級場景處理")
        print("  ✅ 設備限制管理")
        print("  ✅ 支付事件處理")
        print("  ✅ 數據庫狀態一致性")
        print("  ✅ 購買記錄創建")
        print("  ✅ 取消到 FREE 計劃")
        print("  ✅ 時區感知配額重置")
        print("  ✅ 共享測試用戶架構")
        print("  ✅ 統一詳細事件記錄")
        print("  ✅ 逐步操作驗證")
        print("  ✅ 自動 Markdown 報告生成")
        
        print("\n🚀 系統準備就緒！")
        print("\n📋 部署檢查清單:")
        print("  [ ] 更新 Stripe Price ID 映射 (stripe_config.py)")
        print("  [ ] 設置環境變數 (STRIPE_SECRET_KEY, STRIPE_WEBHOOK_SECRET)")
        print("  [ ] 部署 Cloud Functions")
        print("  [ ] 配置 Stripe Webhook 端點")
        print("  [ ] 測試實際支付流程")
        
        print("\n🔗 重要端點:")
        print("  Webhook: /stripe_webhook")
        print("  定時任務: daily_quota_reset_handler (每小時)")
        
        print("\n📝 支援的訂閱計劃:")
        print("  FREE: 10分鐘/天, 1設備")
        print("  STARTER: 1小時/天, 1設備 ($9.99/月, $99/年)")
        print("  PRO: 3小時/天, 2設備 ($19.99/月, $199/年)")
        print("  PREMIUM: 8小時/天, 5設備 ($59.99/月, $559/年)")
        print("  MAX: 無限制, 無限設備 ($129.99/月, $1099/年)")
        
        print("\n⚡ 關鍵特性:")
        print("  - 基於用戶時區的每日配額重置")
        print("  - 升級立即生效，降級保持使用量")
        print("  - 取消時自動降級到 FREE")
        print("  - 完整的設備限制管理")
        print("  - 支付事件自動處理")
        print("  - 購買記錄完整追蹤")
        
    else:
        print("\n❌ 部分測試失敗")
        print("\n🔧 故障排除:")
        
        failed_tests = [name for name, result in test_results.items() if not result]
        
        if 'webhook_basic' in failed_tests or 'quota_reset' in failed_tests:
            print("  - 基礎功能測試失敗可能是由於缺少依賴或配置問題")
            print("  - 檢查 Python 路徑和模組導入")
        
        firebase_tests = ['subscription_scenarios', 'webhook_integration', 'complete_webhook_scenarios']
        if any(test in failed_tests for test in firebase_tests):
            print("  - Firebase 相關測試失敗可能是由於:")
            print("    * Firebase Admin SDK 未安裝: pip install firebase-admin")
            print("    * 服務帳戶金鑰文件不存在或路徑錯誤")
            print("    * Firebase 項目權限問題")
            print("    * 網絡連接問題")

        if 'complete_webhook_scenarios' in failed_tests:
            print("  - 完整 Webhook 場景測試失敗可能是由於:")
            print("    * create_or_update_user 模塊導入問題")
            print("    * 測試日誌記錄器配置問題")
            print("    * 共享用戶創建邏輯錯誤")
        
        print("\n📝 建議:")
        print("  1. 檢查所有依賴是否正確安裝")
        print("  2. 確認 Firebase 服務帳戶金鑰文件存在")
        print("  3. 驗證 Firebase 項目配置")
        print("  4. 檢查網絡連接")
        print("  5. 查看詳細錯誤信息並逐一修復")
    
    print("\n" + "=" * 80)
    return all_tests_passed

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
