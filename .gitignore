# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Firebase
.firebase/
firebase-debug.log
firebase-debug.*.log



# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Node.js (legacy - to be removed)
node_modules/
package-lock.json

# TypeScript (legacy - to be removed)
*.js.map
lib/
tsconfig.json

# Temporary files
*.tmp
*.temp