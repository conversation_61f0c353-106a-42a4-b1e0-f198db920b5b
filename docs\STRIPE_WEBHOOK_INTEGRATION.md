# 🔗 Stripe Webhook 整合指南

## 📋 **概覽**

**版本**: V2.2 - 統一 Default 資料庫架構
**更新日期**: 2025-01-27
**目標受眾**: 網站開發團隊

SpeechPilot V2.2 支援完整的 Stripe Webhook 整合，自動處理訂閱生命週期事件，確保計費資訊與系統狀態同步。

### 🎯 **架構概要**
- **開發環境**: `speakoneai-dev-9f995` + `backend-api` + `(default)` database
- **生產環境**: `speakoneai-prod` + `backend-api` + `(default)` database
- **Webhook 端點**: `/stripe-webhook`
- **數據同步**: 雙表同步（users + subscriptions）

---

## 🎯 **支援的 Webhook 事件**

### 1. ✅ **customer.subscription.created**
**觸發時機**: 用戶首次訂閱付費計劃時
**業務場景**: 用戶從 FREE 升級到 STARTER/PRO/PREMIUM/MAX

**數據庫操作**:
```javascript
// 1. 更新 subscriptions 表
const subscriptionUpdate = {
  plan: 'PRO',  // 從 Stripe metadata 獲取
  status: 'active',
  stripe_subscription_id: subscription.id,
  stripe_customer_id: subscription.customer,
  next_billing_date: new Date(subscription.current_period_end * 1000),
  cancel_at_period_end: subscription.cancel_at_period_end,
  webhook_events: {
    'customer.subscription.created': new Date()
  },
  updated_at: new Date()
};

// 2. 同步更新 users 表
const userUpdate = {
  device_limit: planConfig.max_devices,  // 根據計劃更新
  daily_limit_seconds: planConfig.daily_limit_seconds,
  updated_at: new Date()
};
```

### 2. ✅ **customer.subscription.updated**
**觸發時機**: 計劃變更、計費週期更新、取消設置
**業務場景**: 升級/降級計劃、設置取消

**數據庫操作**:
```javascript
// 檢查變更類型
const isUpgrade = newPlan > currentPlan;
const isCancellation = subscription.cancel_at_period_end;

// 1. 更新 subscriptions 表
const subscriptionUpdate = {
  plan: subscription.metadata.plan,
  cancel_at_period_end: subscription.cancel_at_period_end,
  next_billing_date: new Date(subscription.current_period_end * 1000),
  webhook_events: {
    ...existing_events,
    'customer.subscription.updated': new Date()
  },
  updated_at: new Date()
};

// 2. 如果是計劃變更，同步更新 users 表
if (isUpgrade || isDowngrade) {
  const userUpdate = {
    device_limit: newPlanConfig.max_devices,
    daily_limit_seconds: newPlanConfig.daily_limit_seconds,
    updated_at: new Date()
  };

  // 3. 如果是降級且設備超限，需要處理設備清理
  if (isDowngrade && currentDeviceCount > newDeviceLimit) {
    // 標記超出限制的設備為非活躍
    // 或提示用戶選擇保留的設備
  }
}
```

### 3. ✅ **customer.subscription.deleted**
**觸發時機**: 訂閱被取消或到期
**業務場景**: 用戶取消訂閱，降級到 FREE 計劃

**數據庫操作**:
```javascript
// 1. 更新 subscriptions 表為 FREE 計劃
const subscriptionUpdate = {
  plan: 'FREE',
  status: 'cancelled',
  stripe_subscription_id: null,  // 清除 Stripe 關聯
  cancel_at_period_end: false,
  next_billing_date: null,
  webhook_events: {
    ...existing_events,
    'customer.subscription.deleted': new Date()
  },
  updated_at: new Date()
};

// 2. 降級 users 表到 FREE 限制
const userUpdate = {
  device_limit: 1,  // FREE 計劃限制
  daily_limit_seconds: 600,  // 10 分鐘
  updated_at: new Date()
};

// 3. 處理設備超限問題
const currentDevices = user.device_ids || [];
if (currentDevices.length > 1) {
  // 保留最近活躍的設備，停用其他設備
  const devicesToDeactivate = currentDevices.slice(1);
  // 更新 devices 表，設置 is_active = false
}

// 4. 重置當日使用量（可選）
const usageReset = {
  current_day_used_seconds: 0,
  last_usage_reset_date: new Date().toISOString().split('T')[0]
};
```

### 4. ✅ **invoice.payment_succeeded**
**觸發時機**: 定期付款成功
**業務場景**: 月度/年度續費成功

**數據庫操作**:
```javascript
// 1. 確認訂閱狀態
const subscriptionUpdate = {
  status: 'active',
  next_billing_date: new Date(invoice.period_end * 1000),
  webhook_events: {
    ...existing_events,
    'invoice.payment_succeeded': new Date()
  },
  updated_at: new Date()
};

// 2. 重置每日使用量（如果是新計費週期開始）
const today = new Date().toISOString().split('T')[0];
const usageReset = {
  current_day_used_seconds: 0,
  last_daily_reset_date: today,
  last_daily_reset_at: new Date()
};

// 3. 記錄付款成功事件（可選）
const paymentLog = {
  user_id: userId,
  amount: invoice.amount_paid,
  currency: invoice.currency,
  invoice_id: invoice.id,
  payment_date: new Date(),
  status: 'succeeded'
};
```

### 5. ✅ **invoice.payment_failed**
**觸發時機**: 付款失敗
**業務場景**: 信用卡過期、餘額不足等

**數據庫操作**:
```javascript
// 1. 更新訂閱狀態
const subscriptionUpdate = {
  status: 'past_due',
  webhook_events: {
    ...existing_events,
    'invoice.payment_failed': new Date()
  },
  updated_at: new Date()
};

// 2. 可選：限制服務使用
const serviceRestriction = {
  is_service_restricted: true,
  restriction_reason: 'payment_failed',
  restriction_date: new Date()
};

// 3. 記錄付款失敗事件
const paymentLog = {
  user_id: userId,
  amount: invoice.amount_due,
  currency: invoice.currency,
  invoice_id: invoice.id,
  payment_date: new Date(),
  status: 'failed',
  failure_reason: 'payment_method_declined'
};
```

---

## 💻 **實際代碼實現**

### 🔧 **Webhook 端點設置**

**開發環境**:
```
POST https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/backend-api/stripe-webhook
```

**生產環境**:
```
POST https://asia-east1-speakoneai-prod.cloudfunctions.net/backend-api/stripe-webhook
```

### 📝 **完整實現示例**

```javascript
// Webhook 處理器主函數
async function handleStripeWebhook(req, res) {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  let event;
  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.log(`Webhook signature verification failed.`, err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // 獲取環境感知的數據庫連接
  const db = getFirestoreClient(); // 自動選擇 fs-speakoneai-dev 或 fs-speakoneai-prod

  try {
    switch (event.type) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(db, event.data.object);
        break;
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(db, event.data.object);
        break;
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(db, event.data.object);
        break;
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(db, event.data.object);
        break;
      case 'invoice.payment_failed':
        await handlePaymentFailed(db, event.data.object);
        break;
      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    res.json({received: true});
  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).json({error: 'Webhook processing failed'});
  }
}

// 訂閱創建處理
async function handleSubscriptionCreated(db, subscription) {
  const userId = subscription.metadata.user_id;
  const plan = subscription.metadata.plan;

  if (!userId || !plan) {
    throw new Error('Missing user_id or plan in subscription metadata');
  }

  // 獲取計劃配置
  const planConfig = SUBSCRIPTION_PLANS[plan];
  if (!planConfig) {
    throw new Error(`Invalid plan: ${plan}`);
  }

  // 批量更新操作
  const batch = db.batch();

  // 1. 更新 subscriptions 表
  const subscriptionRef = db.collection('subscriptions').doc(`sub_${userId}`);
  batch.update(subscriptionRef, {
    plan: plan,
    status: 'active',
    stripe_subscription_id: subscription.id,
    stripe_customer_id: subscription.customer,
    next_billing_date: new Date(subscription.current_period_end * 1000),
    cancel_at_period_end: subscription.cancel_at_period_end,
    webhook_events: {
      'customer.subscription.created': new Date()
    },
    updated_at: new Date()
  });

  // 2. 更新 users 表
  const userRef = db.collection('users').doc(userId);
  batch.update(userRef, {
    device_limit: planConfig.max_devices,
    daily_limit_seconds: planConfig.daily_limit_seconds,
    updated_at: new Date()
  });

  await batch.commit();
  console.log(`Subscription created for user ${userId}, plan: ${plan}`);
}

// 訂閱更新處理
async function handleSubscriptionUpdated(db, subscription) {
  const userId = subscription.metadata.user_id;
  const newPlan = subscription.metadata.plan;

  // 獲取當前訂閱資訊
  const subscriptionDoc = await db.collection('subscriptions').doc(`sub_${userId}`).get();
  const currentSubscription = subscriptionDoc.data();
  const currentPlan = currentSubscription.plan;

  const batch = db.batch();

  // 1. 更新 subscriptions 表
  const subscriptionRef = db.collection('subscriptions').doc(`sub_${userId}`);
  batch.update(subscriptionRef, {
    plan: newPlan,
    cancel_at_period_end: subscription.cancel_at_period_end,
    next_billing_date: new Date(subscription.current_period_end * 1000),
    webhook_events: {
      ...currentSubscription.webhook_events,
      'customer.subscription.updated': new Date()
    },
    updated_at: new Date()
  });

  // 2. 如果計劃變更，更新用戶限制
  if (newPlan !== currentPlan) {
    const newPlanConfig = SUBSCRIPTION_PLANS[newPlan];
    const userRef = db.collection('users').doc(userId);

    batch.update(userRef, {
      device_limit: newPlanConfig.max_devices,
      daily_limit_seconds: newPlanConfig.daily_limit_seconds,
      updated_at: new Date()
    });

    // 3. 處理設備超限（如果是降級）
    const userDoc = await db.collection('users').doc(userId).get();
    const userData = userDoc.data();
    const currentDeviceIds = userData.device_ids || [];

    if (currentDeviceIds.length > newPlanConfig.max_devices) {
      // 停用超出限制的設備
      const devicesToDeactivate = currentDeviceIds.slice(newPlanConfig.max_devices);

      for (const deviceId of devicesToDeactivate) {
        const deviceRef = db.collection('devices').doc(deviceId);
        batch.update(deviceRef, {
          is_active: false,
          deactivated_reason: 'plan_downgrade',
          deactivated_at: new Date()
        });
      }

      // 更新用戶的設備列表
      const activeDeviceIds = currentDeviceIds.slice(0, newPlanConfig.max_devices);
      batch.update(userRef, {
        device_ids: activeDeviceIds
      });
    }
  }

  await batch.commit();
  console.log(`Subscription updated for user ${userId}: ${currentPlan} -> ${newPlan}`);
}

// 訂閱刪除處理
async function handleSubscriptionDeleted(db, subscription) {
  const userId = subscription.metadata.user_id;

  const batch = db.batch();

  // 1. 降級到 FREE 計劃
  const subscriptionRef = db.collection('subscriptions').doc(`sub_${userId}`);
  batch.update(subscriptionRef, {
    plan: 'FREE',
    status: 'cancelled',
    stripe_subscription_id: null,
    cancel_at_period_end: false,
    next_billing_date: null,
    webhook_events: {
      'customer.subscription.deleted': new Date()
    },
    updated_at: new Date()
  });

  // 2. 更新用戶限制到 FREE
  const userRef = db.collection('users').doc(userId);
  const userDoc = await userRef.get();
  const userData = userDoc.data();

  batch.update(userRef, {
    device_limit: 1,
    daily_limit_seconds: 600, // 10 分鐘
    updated_at: new Date()
  });

  // 3. 處理設備超限
  const currentDeviceIds = userData.device_ids || [];
  if (currentDeviceIds.length > 1) {
    // 保留最近活躍的設備
    const devicesToKeep = currentDeviceIds.slice(0, 1);
    const devicesToDeactivate = currentDeviceIds.slice(1);

    for (const deviceId of devicesToDeactivate) {
      const deviceRef = db.collection('devices').doc(deviceId);
      batch.update(deviceRef, {
        is_active: false,
        deactivated_reason: 'subscription_cancelled',
        deactivated_at: new Date()
      });
    }

    batch.update(userRef, {
      device_ids: devicesToKeep
    });
  }

  await batch.commit();
  console.log(`Subscription cancelled for user ${userId}, downgraded to FREE`);
}
```

---

## 🏗️ **數據架構更新**

### 🗄️ **V2.2 數據架構**

#### **subscriptions 表關鍵欄位**

```javascript
{
  // 基本訂閱資訊
  "plan": "PRO",
  "status": "active",

  // 每日配額設定
  "daily_limit_seconds": 10800,
  "current_day_used_seconds": 3600,
  "last_daily_reset_date": "2025-01-27",

  // Stripe 整合
  "stripe_customer_id": "cus_xxx",
  "stripe_subscription_id": "sub_xxx",
  "next_billing_date": "2025-02-27T00:00:00Z",
  "cancel_at_period_end": false,

  // Webhook 事件追蹤
  "webhook_events": {
    "customer.subscription.created": "2025-01-01T00:00:00Z",
    "customer.subscription.updated": "2025-01-15T00:00:00Z",
    "invoice.payment_succeeded": "2025-01-27T00:00:00Z"
  }
}
```

#### **users 表關鍵欄位**

```javascript
{
  // 設備管理
  "device_ids": ["device_1", "device_2"],
  "device_limit": 2,

  // 每日配額追蹤
  "daily_limit_seconds": 10800,
  "current_day_used_seconds": 3600,
  "last_usage_reset_date": "2025-01-27",

  // 地區化設定
  "register_region": "Hong Kong",
  "register_region_timezone": "UTC+8",
  "usage_reset_hour": 0
}
```

---

## ⚙️ **環境配置**

### 🔑 **環境變數設置**

**開發環境**:
```bash
STRIPE_SECRET_KEY=sk_test_xxx
STRIPE_WEBHOOK_SECRET=whsec_xxx
FIRESTORE_DATABASE_ID=fs-speakoneai-dev
FUNCTIONS_NAME=func-speakoneai-dev
ENVIRONMENT=dev
```

**生產環境**:
```bash
STRIPE_SECRET_KEY=sk_live_xxx
STRIPE_WEBHOOK_SECRET=whsec_xxx
FIRESTORE_DATABASE_ID=fs-speakoneai-prod
FUNCTIONS_NAME=func-speakoneai-prod
ENVIRONMENT=prod
```

### 📋 **Stripe Dashboard 配置**

#### **1. 創建 Webhook 端點**

**開發環境**:
- URL: `https://asia-east1-speechpilot-f1495.cloudfunctions.net/func-speakoneai-dev/stripe-webhook`
- 事件: `customer.subscription.*`, `invoice.payment_*`

**生產環境**:
- URL: `https://asia-east1-speakoneai-prod.cloudfunctions.net/backend-api/stripe-webhook`
- 事件: `customer.subscription.*`, `invoice.payment_*`

#### **2. 產品和價格設置**

```javascript
// 創建產品腳本示例
const products = [
  {
    id: 'starter',
    name: 'Starter Plan',
    description: '1 hour per day, 1 device',
    metadata: { plan: 'STARTER' }
  },
  {
    id: 'pro',
    name: 'Pro Plan',
    description: '3 hours per day, 2 devices',
    metadata: { plan: 'PRO' }
  }
];

const prices = [
  {
    product: 'starter',
    unit_amount: 999, // $9.99
    currency: 'usd',
    recurring: { interval: 'month' }
  },
  {
    product: 'pro',
    unit_amount: 1999, // $19.99
    currency: 'usd',
    recurring: { interval: 'month' }
  }
];
```

---

## 🔧 **實施指南**

### 1. Webhook 端點設置

**端點 URL**: `https://asia-east1-speechpilot-f1495.cloudfunctions.net/backend-api/stripe-webhook`

**需要監聽的事件**:
```
customer.subscription.created
customer.subscription.updated  
customer.subscription.deleted
invoice.payment_succeeded
invoice.payment_failed
```

### 2. Webhook 簽名驗證

```javascript
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

function verifyWebhookSignature(payload, signature, secret) {
  try {
    return stripe.webhooks.constructEvent(payload, signature, secret);
  } catch (err) {
    throw new Error('Webhook signature verification failed');
  }
}
```

### 3. 事件處理流程

```javascript
async function handleStripeWebhook(event) {
  switch (event.type) {
    case 'customer.subscription.created':
      await handleSubscriptionCreated(event.data.object);
      break;
    case 'customer.subscription.updated':
      await handleSubscriptionUpdated(event.data.object);
      break;
    case 'customer.subscription.deleted':
      await handleSubscriptionDeleted(event.data.object);
      break;
    case 'invoice.payment_succeeded':
      await handlePaymentSucceeded(event.data.object);
      break;
    case 'invoice.payment_failed':
      await handlePaymentFailed(event.data.object);
      break;
  }
}
```

---

## 📊 **業務邏輯處理**

### 訂閱創建處理

```javascript
async function handleSubscriptionCreated(subscription) {
  const userId = subscription.metadata.user_id;
  const plan = subscription.metadata.plan;
  
  // 更新 subscriptions 表
  await updateSubscription(userId, {
    stripe_subscription_id: subscription.id,
    plan: plan,
    status: subscription.status,
    currentPeriodStart: new Date(subscription.current_period_start * 1000),
    currentPeriodEnd: new Date(subscription.current_period_end * 1000),
    webhook_events: {
      'customer.subscription.created': new Date()
    }
  });
  
  // 同步更新 users 表
  await updateUserLimits(userId, plan);
}
```

### 訂閱更新處理

```javascript
async function handleSubscriptionUpdated(subscription) {
  const userId = subscription.metadata.user_id;
  
  // 檢查是否為計劃變更
  if (subscription.metadata.plan_changed) {
    await handlePlanUpgrade(userId, subscription);
  }
  
  // 更新週期資訊
  await updateBillingCycle(userId, subscription);
}
```

### 付款成功處理

```javascript
async function handlePaymentSucceeded(invoice) {
  const subscriptionId = invoice.subscription;
  const subscription = await stripe.subscriptions.retrieve(subscriptionId);
  const userId = subscription.metadata.user_id;
  
  // 重置當前週期使用量
  await resetPeriodUsage(userId);
  
  // 確認訂閱狀態
  await updateSubscriptionStatus(userId, 'active');
}
```

---

## 🔄 **數據同步策略**

### 1. 雙向同步
- **Stripe → SpeechPilot**: Webhook 事件自動更新
- **SpeechPilot → Stripe**: API 調用同步狀態

### 2. 冪等性保證
- 使用 Stripe 事件 ID 防止重複處理
- 實施樂觀鎖定避免併發衝突

### 3. 錯誤處理
- Webhook 失敗重試機制
- 數據不一致檢測和修復
- 詳細的錯誤日誌記錄

---

## 🧪 **測試策略**

### 1. **本地測試（使用 Stripe CLI）**

```bash
# 安裝 Stripe CLI
# 登入 Stripe 帳戶
stripe login

# 轉發 Webhook 到本地開發環境
stripe listen --forward-to https://asia-east1-speechpilot-f1495.cloudfunctions.net/func-speakoneai-dev/stripe-webhook

# 觸發測試事件
stripe trigger customer.subscription.created --add customer:metadata[user_id]=test_user_123 --add subscription:metadata[plan]=PRO

stripe trigger customer.subscription.updated --add customer:metadata[user_id]=test_user_123 --add subscription:metadata[plan]=PREMIUM

stripe trigger customer.subscription.deleted --add customer:metadata[user_id]=test_user_123

stripe trigger invoice.payment_succeeded --add customer:metadata[user_id]=test_user_123

stripe trigger invoice.payment_failed --add customer:metadata[user_id]=test_user_123
```

### 2. **端到端測試流程**

#### **測試案例 1: 新用戶訂閱**
```javascript
// 1. 創建測試用戶
const testUser = {
  uid: 'test_user_stripe_123',
  email: '<EMAIL>',
  plan: 'FREE'
};

// 2. 模擬 Stripe 訂閱創建
const subscriptionEvent = {
  type: 'customer.subscription.created',
  data: {
    object: {
      id: 'sub_test123',
      customer: 'cus_test123',
      metadata: {
        user_id: 'test_user_stripe_123',
        plan: 'PRO'
      },
      current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60
    }
  }
};

// 3. 驗證數據庫更新
// - subscriptions 表 plan 更新為 PRO
// - users 表 device_limit 更新為 2
// - users 表 daily_limit_seconds 更新為 10800
```

#### **測試案例 2: 計劃升級**
```javascript
// 1. 現有 STARTER 用戶升級到 PRO
const upgradeEvent = {
  type: 'customer.subscription.updated',
  data: {
    object: {
      metadata: {
        user_id: 'existing_user_123',
        plan: 'PRO'  // 從 STARTER 升級
      }
    }
  }
};

// 2. 驗證升級處理
// - 設備限制從 1 增加到 2
// - 每日限制從 3600 增加到 10800
// - 現有設備保持活躍
```

#### **測試案例 3: 計劃降級**
```javascript
// 1. PRO 用戶降級到 STARTER
const downgradeEvent = {
  type: 'customer.subscription.updated',
  data: {
    object: {
      metadata: {
        user_id: 'pro_user_123',
        plan: 'STARTER'  // 從 PRO 降級
      }
    }
  }
};

// 2. 驗證降級處理
// - 設備限制從 2 減少到 1
// - 超出限制的設備被停用
// - 用戶收到設備管理通知
```

### 3. **數據驗證檢查**

```javascript
// 驗證函數示例
async function verifyWebhookProcessing(userId, expectedPlan) {
  const db = getFirestoreClient();

  // 1. 檢查 subscriptions 表
  const subscriptionDoc = await db.collection('subscriptions').doc(`sub_${userId}`).get();
  const subscription = subscriptionDoc.data();

  assert(subscription.plan === expectedPlan, 'Plan not updated correctly');
  assert(subscription.webhook_events['customer.subscription.created'], 'Webhook event not recorded');

  // 2. 檢查 users 表
  const userDoc = await db.collection('users').doc(userId).get();
  const user = userDoc.data();

  const planConfig = SUBSCRIPTION_PLANS[expectedPlan];
  assert(user.device_limit === planConfig.max_devices, 'Device limit not synced');
  assert(user.daily_limit_seconds === planConfig.daily_limit_seconds, 'Daily limit not synced');

  // 3. 檢查設備狀態
  const activeDevices = user.device_ids || [];
  assert(activeDevices.length <= user.device_limit, 'Device count exceeds limit');

  console.log(`✅ Webhook processing verified for user ${userId}, plan ${expectedPlan}`);
}
```

---

## 🔍 **故障排除**

### ❌ **常見問題**

#### **1. Webhook 簽名驗證失敗**
```javascript
// 錯誤: Webhook signature verification failed
// 原因: STRIPE_WEBHOOK_SECRET 不正確或請求體被修改

// 解決方案:
// 1. 檢查環境變數
console.log('Webhook Secret:', process.env.STRIPE_WEBHOOK_SECRET?.substring(0, 10) + '...');

// 2. 確保請求體為原始格式
app.use('/stripe-webhook', express.raw({type: 'application/json'}));

// 3. 驗證 Stripe Dashboard 中的端點 URL
```

#### **2. 用戶 ID 缺失**
```javascript
// 錯誤: Missing user_id in subscription metadata
// 原因: 創建訂閱時未設置 metadata

// 解決方案: 在創建 Stripe 訂閱時添加 metadata
const subscription = await stripe.subscriptions.create({
  customer: customerId,
  items: [{price: priceId}],
  metadata: {
    user_id: firebaseUserId,  // 必須設置
    plan: 'PRO'              // 必須設置
  }
});
```

#### **3. 數據庫更新失敗**
```javascript
// 錯誤: Firestore permission denied or document not found
// 原因: 用戶或訂閱文檔不存在

// 解決方案: 添加存在性檢查
async function handleSubscriptionCreated(db, subscription) {
  const userId = subscription.metadata.user_id;

  // 檢查用戶是否存在
  const userDoc = await db.collection('users').doc(userId).get();
  if (!userDoc.exists) {
    throw new Error(`User ${userId} not found`);
  }

  // 檢查訂閱是否存在
  const subscriptionDoc = await db.collection('subscriptions').doc(`sub_${userId}`).get();
  if (!subscriptionDoc.exists) {
    throw new Error(`Subscription for user ${userId} not found`);
  }

  // 繼續處理...
}
```

#### **4. 設備超限處理錯誤**
```javascript
// 錯誤: 降級時設備清理失敗
// 原因: 設備文檔不存在或權限問題

// 解決方案: 安全的設備清理
async function deactivateExcessDevices(db, userId, newDeviceLimit) {
  const userDoc = await db.collection('users').doc(userId).get();
  const deviceIds = userDoc.data().device_ids || [];

  if (deviceIds.length <= newDeviceLimit) {
    return; // 無需清理
  }

  const devicesToDeactivate = deviceIds.slice(newDeviceLimit);
  const batch = db.batch();

  for (const deviceId of devicesToDeactivate) {
    const deviceRef = db.collection('devices').doc(deviceId);
    const deviceDoc = await deviceRef.get();

    if (deviceDoc.exists) {
      batch.update(deviceRef, {
        is_active: false,
        deactivated_reason: 'plan_downgrade',
        deactivated_at: new Date()
      });
    }
  }

  await batch.commit();
}
```

### 🔧 **調試工具**

#### **1. Webhook 事件日誌**
```javascript
// 添加詳細日誌記錄
function logWebhookEvent(event, userId, action) {
  console.log({
    timestamp: new Date().toISOString(),
    event_type: event.type,
    event_id: event.id,
    user_id: userId,
    action: action,
    environment: process.env.ENVIRONMENT
  });
}
```

#### **2. 數據一致性檢查**
```javascript
// 定期檢查數據一致性
async function checkDataConsistency(db, userId) {
  const userDoc = await db.collection('users').doc(userId).get();
  const subscriptionDoc = await db.collection('subscriptions').doc(`sub_${userId}`).get();

  const user = userDoc.data();
  const subscription = subscriptionDoc.data();

  const issues = [];

  // 檢查設備限制一致性
  if (user.device_limit !== subscription.max_devices) {
    issues.push('Device limit mismatch between user and subscription');
  }

  // 檢查每日限制一致性
  if (user.daily_limit_seconds !== subscription.daily_limit_seconds) {
    issues.push('Daily limit mismatch between user and subscription');
  }

  // 檢查設備數量
  const deviceIds = user.device_ids || [];
  if (deviceIds.length > user.device_limit) {
    issues.push('Active device count exceeds limit');
  }

  return issues;
}
```

---

## 📈 **監控和維護**

### 1. 關鍵指標
- Webhook 事件處理成功率
- 數據同步延遲時間
- 付款失敗率
- 訂閱狀態一致性

### 2. 告警設置
- Webhook 處理失敗告警
- 數據不一致告警
- 付款失敗告警
- 系統錯誤告警

### 3. 定期維護
- 清理過期的 Webhook 事件記錄
- 驗證數據一致性
- 更新 Stripe API 版本
- 檢查安全配置

---

## 🎯 **最佳實踐**

### 1. **安全性**
- ✅ 始終驗證 Webhook 簽名
- ✅ 使用 HTTPS 端點
- ✅ 限制 Webhook 端點的訪問
- ✅ 定期輪換 Webhook 密鑰

### 2. **可靠性**
- ✅ 實施冪等性處理（防止重複處理）
- ✅ 使用數據庫事務確保一致性
- ✅ 添加重試機制處理臨時失敗
- ✅ 記錄詳細的錯誤日誌

### 3. **性能**
- ✅ 使用批量操作減少數據庫調用
- ✅ 異步處理非關鍵操作
- ✅ 實施適當的超時設置
- ✅ 監控 Webhook 處理時間

### 4. **維護性**
- ✅ 清晰的錯誤訊息和日誌
- ✅ 完整的測試覆蓋
- ✅ 文檔化的故障排除流程
- ✅ 定期的數據一致性檢查

---

## 📋 **檢查清單**

### 🔧 **實施前檢查**
- [ ] 環境變數正確設置
- [ ] Stripe Dashboard 配置完成
- [ ] Webhook 端點可訪問
- [ ] 數據庫權限正確
- [ ] 測試用戶和訂閱準備就緒

### 🧪 **測試檢查**
- [ ] 訂閱創建測試通過
- [ ] 計劃升級測試通過
- [ ] 計劃降級測試通過
- [ ] 訂閱取消測試通過
- [ ] 付款成功/失敗測試通過
- [ ] 數據一致性驗證通過

### 🚀 **上線檢查**
- [ ] 生產環境 Webhook 配置
- [ ] 監控和告警設置
- [ ] 錯誤處理機制驗證
- [ ] 備份和恢復流程測試
- [ ] 團隊培訓完成

---

## 🎯 **總結**

通過完整的 Stripe Webhook 整合，SpeechPilot V2.2 實現了：

✅ **自動化計費管理** - 無需手動干預的訂閱生命週期管理
✅ **實時狀態同步** - Stripe 事件即時反映到系統中
✅ **準確的配額追蹤** - 基於每日重置的精確計量
✅ **靈活的計劃管理** - 支援升級、降級、取消等操作
✅ **完整的審計追蹤** - 所有 Webhook 事件都有記錄
✅ **多環境支援** - 開發和生產環境完全隔離
✅ **設備管理整合** - 自動處理設備限制和清理

### 🌟 **關鍵優勢**

1. **業務自動化**: 從用戶訂閱到服務限制的全自動管理
2. **數據一致性**: 雙表同步確保用戶和訂閱資訊一致
3. **環境隔離**: 開發和生產環境完全分離，安全可靠
4. **易於維護**: 詳細的文檔和故障排除指南
5. **可擴展性**: 支援未來新計劃和功能的擴展

**這個架構為 SpeechPilot 的全球化商業運營提供了堅實的技術基礎！** 🚀

---

## 📞 **支援**

如有問題，請聯繫開發團隊：
- 📧 技術支援: <EMAIL>
- 📋 問題追蹤: GitHub Issues
- 📚 更多文檔: `/docs` 目錄
