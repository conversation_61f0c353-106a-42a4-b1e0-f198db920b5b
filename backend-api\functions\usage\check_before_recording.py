"""
Usage checking before recording functionality for SpeechPilot Firebase Functions
"""

import logging
import os
from firebase_functions import https_fn
from firebase_admin import firestore
from typing import Dict, Any

from ..utils.validation import validate_auth, validate_device_id, validate_features
from ..utils.database import get_user_data, check_device_exists, check_subscription_usage_limits
from ..utils.constants import ERROR_MESSAGES, DEFAULT_WEB_PORTAL_URL, DEFAULT_UPGRADE_PATH
from ..utils.error_handler import handle_error, handle_validation_error, log_function_start, log_function_success

logger = logging.getLogger(__name__)

def get_db():
    """獲取 Firestore 客戶端（環境感知）"""
    from ..utils.environment import get_db as get_env_db
    return get_env_db()

def check_usage_before_recording_handler(request: https_fn.CallableRequest) -> Dict[str, Any]:
    """
    錄音前檢查可用使用時間
    
    Args:
        request: Firebase Functions 請求對象
        
    Returns:
        Dict[str, Any]: 檢查結果
    """
    try:
        # 驗證用戶認證
        user_id = validate_auth(request)
        
        # 獲取請求數據
        data = request.data or {}
        device_id = data.get('device_id')
        estimated_duration_seconds = data.get('estimated_duration_seconds', 0)
        features = data.get('features', [])
        
        logger.info(f"Checking usage before recording for user {user_id}, device {device_id}")
        
        # 驗證必要參數
        if not device_id:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="device_id is required"
            )
        
        if not validate_device_id(device_id):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="Invalid device_id format"
            )
        
        if not validate_features(features):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message="Invalid features"
            )
        
        # 檢查設備是否存在且屬於用戶
        if not check_device_exists(user_id, device_id):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.NOT_FOUND,
                message=ERROR_MESSAGES["DEVICE_NOT_FOUND"]
            )
        
        # 獲取用戶訂閱資料
        user_data = get_user_data(user_id)
        subscription_id = user_data.get('current_subscription_id')

        if not subscription_id:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.FAILED_PRECONDITION,
                message="用戶沒有有效的訂閱"
            )

        # 檢查使用限制
        usage_check = check_subscription_usage_limits(subscription_id, estimated_duration_seconds)

        # 計算可用時間（只使用每日限制）
        available_seconds = usage_check["daily_remaining"]

        # 檢查是否可以使用
        if not usage_check["can_use"]:
            upgrade_url = f"{os.getenv('WEB_PORTAL_URL', DEFAULT_WEB_PORTAL_URL)}{DEFAULT_UPGRADE_PATH}"

            return {
                "success": True,
                "data": {
                    "can_use": False,
                    "remaining_seconds": max(0, available_seconds) if available_seconds != -1 else 0,
                    "reason": usage_check["reason"],
                    "upgrade_required": True,
                    "upgrade_url": upgrade_url,
                    "daily_limit": usage_check["daily_limit"]
                }
            }

        # 檢查預估時間是否超過可用時間
        if available_seconds != -1 and estimated_duration_seconds > available_seconds:
            # 如果預估時間超過可用時間，但仍有一些可用時間
            if available_seconds > 0:
                return {
                    "success": True,
                    "data": {
                        "can_use": True,
                        "remaining_seconds": available_seconds,
                        "reason": f"可錄音時間有限，最多 {available_seconds} 秒",
                        "upgrade_required": False,
                        "daily_limit": usage_check["daily_limit"]
                    }
                }
            else:
                upgrade_url = f"{os.getenv('WEB_PORTAL_URL', DEFAULT_WEB_PORTAL_URL)}{DEFAULT_UPGRADE_PATH}"
                return {
                    "success": True,
                    "data": {
                        "can_use": False,
                        "remaining_seconds": 0,
                        "reason": "使用時間已用完",
                        "upgrade_required": True,
                        "upgrade_url": upgrade_url,
                        "daily_limit": usage_check["daily_limit"]
                    }
                }

        # 可以正常錄音
        result = {
            "success": True,
            "data": {
                "can_use": True,
                "remaining_seconds": available_seconds,
                "reason": None,
                "upgrade_required": False,
                "daily_limit": usage_check["daily_limit"]
            }
        }
        
        logger.info(f"Usage check completed for user {user_id}: can_record=True, available={available_seconds}s")
        
        return result
        
    except https_fn.HttpsError:
        # 重新拋出 Firebase Functions 錯誤
        raise
    except Exception as e:
        raise handle_error(
            error=e,
            error_code="INTERNAL_ERROR",
            user_message="檢查錄音前使用量時發生錯誤",
            context={"user_id": user_id, "function": "check_usage_before_recording"}
        )
