"""
環境變數載入工具
"""

import os
from typing import Dict, Any

def load_env_file(env_file: str = '.env.dev') -> Dict[str, Any]:
    """
    載入環境變數文件
    
    Args:
        env_file: 環境變數文件路徑
        
    Returns:
        Dict[str, Any]: 環境變數字典
    """
    env_vars = {}
    
    # 設置預設環境變數
    env_vars['FIREBASE_PROJECT_ID'] = 'speakoneai-dev-9f995'
    env_vars['FIREBASE_DATABASE_URL'] = 'https://speakoneai-dev-9f995-default-rtdb.firebaseio.com'
    env_vars['ENVIRONMENT'] = 'development'
    
    # 嘗試從文件載入
    try:
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key.strip()] = value.strip().strip('"\'')
    except Exception as e:
        print(f"警告：無法載入環境變數文件 {env_file}: {str(e)}")
    
    # 設置到環境變數
    for key, value in env_vars.items():
        os.environ[key] = value
    
    return env_vars

def print_env_info():
    """打印環境資訊"""
    print("🌍 環境資訊:")
    print(f"  - Firebase Project: {os.environ.get('FIREBASE_PROJECT_ID', 'N/A')}")
    print(f"  - Environment: {os.environ.get('ENVIRONMENT', 'N/A')}")
    print(f"  - Database URL: {os.environ.get('FIREBASE_DATABASE_URL', 'N/A')}")
