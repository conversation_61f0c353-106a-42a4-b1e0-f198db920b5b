# 🔌 SpeechPilot API 介面文檔 V2.3 最終版

**版本**: V2.3 - API 整合和簡化  
**更新日期**: 2025-01-27  
**環境**: speakoneai-dev-9f995 (開發) / speakoneai-prod (生產)

**開發環境**: `https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/`
**生產環境**: `https://asia-east1-speakoneai-prod.cloudfunctions.net/`

## ⚠️ **重要 URL 修正**

**正確的 API 調用格式**:
```
https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/{function_name}
```

**錯誤格式** (會導致 404):
```
https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/backend-api/{function_name}
```

## 🆕 **V2.3 新增功能**

- ✅ **統一用戶儀表板**: 新增 `get_user_dashboard` 三合一 API
- ✅ **統一設備管理**: 新增 `validate_or_register_device` 統一 API
- ✅ **設備移除功能**: 新增 `remove_device` 設備管理 API
- ✅ **增強訂閱創建**: `create_subscription` 接收完整 Stripe 支付資訊
- ✅ **購買記錄整合**: 完整的 Stripe 支付資訊記錄
- ✅ **API 簡化**: 移除舊的分離 API，統一使用新 API

**總計**: 8 個核心 API (新增 1 個設備管理 API)

---

## 🔐 **認證 API**


### 1. 用戶儀表板 (三合一 API) 🆕
**端點**: `POST https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/get_user_dashboard`

**用途**: 一次獲取用戶完整資訊，包含用戶資料、訂閱狀態、設備列表（使用量統計已移除，只信任 subscription.current_day_used_seconds）

**請求體**: 無（使用 Firebase Auth Token）

**回應**:
```javascript
{
  "success": true,
  "data": {
    "user": {
      "user_id": "xxx",
      "email": "<EMAIL>",
      "display_name": "User Name",
      "created_at": "2025-01-01T00:00:00Z",
      "preferences": {
        "language": "zh-TW",
        "notifications": true,
        "auto_upgrade_prompts": true
      },
      "onboarding_completed": true,
      "first_device_registered": true
    },
    "subscription": {
      "subscription_id": "sub_xxx",
      "plan": "PRO",
      "status": "active",
      "daily_limit_seconds": 10800,
      "current_day_used_seconds": 3600,  // 唯一的使用量來源
      "max_devices": 2,
      "supported_platforms": ["windows", "macos", "ios", "android"],
      "features": ["進階語音轉文字", "每日 3 小時", "所有平台"],
      "register_region_timezone": "UTC+8",
      "usage_reset_hour": 0,
      "last_daily_reset_date": "2025-01-27",
      "last_daily_reset_at": "2025-01-27T16:00:00Z",
      "next_billing_date": "2025-02-01T00:00:00Z",
      "auto_renew": true,
      "stripe_customer_id": "cus_xxx",
      "stripe_subscription_id": "sub_xxx",
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z"
    },
    "devices": {
      "active_devices": [
        {
          "device_id": "device_123",
          "device_name": "My Computer",
          "platform": "windows",
          "status": "active",
          "is_authorized": true,
          "last_active_at": "2025-01-27T10:00:00Z",
          "created_at": "2025-01-27T09:00:00Z",
          "app_version": "1.0.0"
        }
      ],
      "device_count": 1
    }
    // 注意：移除了 "usage" 物件，只信任 subscription.current_day_used_seconds
  },
  "message": "用戶儀表板資訊獲取成功"
}
```

**使用量計算** (客戶端實現):
```javascript
// 從 subscription 物件計算使用量統計
const subscription = response.data.subscription;
const dailyLimit = subscription.daily_limit_seconds;
const dailyUsed = subscription.current_day_used_seconds;

// 計算剩餘時間和使用率
const dailyRemaining = dailyLimit === -1 ? -1 : Math.max(0, dailyLimit - dailyUsed);
const canUse = dailyRemaining > 0 || dailyLimit === -1;
const usagePercentage = dailyLimit > 0 ? (dailyUsed / dailyLimit * 100) : 0;

console.log({
  limit: dailyLimit,
  used: dailyUsed,
  remaining: dailyRemaining,
  percentage: usagePercentage.toFixed(1) + '%',
  canUse: canUse
});
```

**優勢**:
- 🚀 **一次調用**: 替代多個獨立 API
- 📊 **數據一致**: 只信任 subscription.current_day_used_seconds
- ⚡ **性能優化**: 減少網路請求，提升載入速度
- 🎯 **前端友好**: 適合個人中心、Dashboard 頁面
- ✅ **修復錯誤**: 移除有問題的 usage 物件

### 2. 創建或更新用戶
**端點**: `POST https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/create_or_update_user`

**用途**: 創建新用戶或更新現有用戶資訊

**請求體**:
```javascript
{
  "email": "<EMAIL>",
  "display_name": "User Name",
  "platform": "windows",
  "app_version": "1.0.0"
}
```

**回應**:
```javascript
{
  "success": true,
  "data": {
    "user": {
      "user_id": "firebase-auth-uid",
      "email": "<EMAIL>",
      "display_name": "User Name",
      "is_new_user": false
    }
  },
  "message": "用戶資訊更新成功"
}
```

---

## 📋 **訂閱管理 API**

### 3. 創建訂閱 (增強版) 🆕
**端點**: `POST https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/create_subscription`

**用途**: 創建新的訂閱計劃，接收完整的 Stripe 支付資訊和計劃詳情

**請求體**:
```javascript
{
  // === 計劃資訊 (前端提供) ===
  "plan": "PRO",
  "daily_limit_seconds": 10800,  // 每日限制秒數
  "max_devices": 2,  // 最大設備數
  "price_amount": 1999,  // 價格（分）
  "currency": "usd",  // 貨幣
  "billing_cycle": "monthly",  // 計費週期
  
  // === Stripe 支付資訊 (來自 webhook) ===
  "stripe_invoice_id": "in_1234567890",
  "stripe_customer_id": "cus_1234567890", 
  "stripe_subscription_id": "sub_1234567890",
  "stripe_payment_intent_id": "pi_1234567890",
  "amount_total": 1999,  // 總金額（分）
  "amount_paid": 1999,  // 已付金額（分）
  "payment_status": "succeeded",  // 支付狀態
  "invoice_created": "2025-01-01T00:00:00Z",  // 發票創建時間
  "period_start": "2025-01-01T00:00:00Z",  // 計費週期開始
  "period_end": "2025-02-01T00:00:00Z",  // 計費週期結束
  
  // === 可選資訊 ===
  "source_app": "web",
  "register_region_timezone": "UTC+8",
  "usage_reset_hour": 0
}
```

**回應**:
```javascript
{
  "success": true,
  "data": {
    "subscription": {
      "subscription_id": "sub_firebase-auth-uid",
      "user_id": "firebase-auth-uid",
      "plan": "PRO",
      "status": "active",
      "daily_limit_seconds": 10800,
      "max_devices": 2,
      "current_day_used_seconds": 0,
      "stripe_customer_id": "cus_1234567890",
      "stripe_subscription_id": "sub_1234567890",
      "billing_cycle": "monthly",
      "auto_renew": true,
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z"
    }
  },
  "message": "PRO 訂閱創建成功"
}
```

**重要變更**:
- 🔄 **前端提供完整資訊**: 不再依賴後端計算，使用前端提供的準確資料
- 💳 **Stripe 資訊完整**: 包含發票、支付、計費週期等完整資訊
- ❌ **移除 monthly_limit_seconds**: 不再使用月度限制
- ✅ **支援 FREE 計劃**: FREE 計劃不需要 Stripe 資訊

---

## 📱 **設備管理 API**

### 4. 統一設備驗證和註冊 🆕
**端點**: `POST https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/validate_or_register_device`

**用途**: 統一處理設備驗證和註冊，簡化前端邏輯

**邏輯流程**:
1. 檢查用戶訂閱和設備限制
2. 檢查當前設備是否在設備表中
3. 如果設備存在且有效 → 返回 code: 1 (已驗證)
4. 如果設備不存在但在限制內 → 註冊設備，返回 code: 2 (已註冊)
5. 如果超過設備限制 → 返回 code: -1 (需要移除設備)

**請求體**:
```javascript
{
  "device_id": "unique-device-id",
  "device_name": "My Device",
  "platform": "windows",
  "app_version": "1.0.0",
  "device_info": {
    "os_version": "10.0.26100",
    "fingerprint": "device-fingerprint"
  }
}
```

**回應狀態碼**:
- `1`: 設備已存在且有效 - 驗證成功
- `2`: 設備不存在但在限制內 - 註冊成功
- `-1`: 超過設備限制 - 需要移除設備
- `-2`: 用戶無訂閱 - 需要創建訂閱
- `-3`: 平台不支援 - 需要升級計劃
- `-4`: 設備狀態無效 - 需要重新註冊

**成功回應 (Code 1 - 驗證成功)**:
```javascript
{
  "success": true,
  "code": 1,
  "message": "設備驗證成功",
  "data": {
    "device": {
      "device_id": "unique-device-id",
      "device_name": "My Device",
      "platform": "windows",
      "status": "active",
      "is_active": true,
      "is_authorized": true,
      "last_active_at": "2025-01-27T10:00:00Z",
      "created_at": "2025-01-27T09:00:00Z",
      "app_version": "1.0.0"
    },
    "subscription": {
      "plan": "PRO",
      "status": "active",
      "daily_limit_seconds": 10800,
      "current_day_used_seconds": 3600,
      "max_devices": 2
    },
    "devices_info": {
      "current_count": 2,
      "max_allowed": 2,
      "can_add_more": false
    }
  }
}
```

**失敗回應 (Code -1 - 設備限制)**:
```javascript
{
  "success": false,
  "code": -1,
  "message": "已達到設備數量限制 (2/2)",
  "reason": "DEVICE_LIMIT_EXCEEDED",
  "action_required": "REMOVE_DEVICE",
  "data": {
    "current_devices": [
      {
        "device_id": "device_1",
        "device_name": "Device 1",
        "platform": "windows",
        "last_active_at": "2025-01-27T09:00:00Z"
      }
    ],
    "current_count": 2,
    "max_allowed": 2,
    "current_plan": "PRO"
  }
}
```

**使用建議**:
- 前端可以用這個 API 替代舊的 `validate_device` 和 `register_device_v2`
- 根據返回的 `code` 進行不同的處理邏輯
- `code > 0` 表示成功，`code < 0` 表示需要用戶操作

### 5. 移除設備 🆕
**端點**: `POST https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/remove_device`

**用途**: 移除用戶設備，釋放設備配額

**請求體**:
```javascript
{
  "device_id": "unique-device-id",
  "reason": "user_request"  // 可選，移除原因
}
```

**回應**:
```javascript
{
  "success": true,
  "data": {
    "device_id": "unique-device-id",
    "removed_at": "2025-01-27T10:00:00Z",
    "remaining_devices": [
      {
        "device_id": "device_2",
        "device_name": "My Phone",
        "platform": "ios",
        "status": "active",
        "last_active_at": "2025-01-27T09:00:00Z"
      }
    ],
    "devices_info": {
      "current_count": 1,
      "max_allowed": 2,
      "can_add_more": true
    }
  },
  "message": "設備移除成功"
}
```

**錯誤回應**:
```javascript
{
  "success": false,
  "error": {
    "code": "NOT_FOUND",
    "message": "設備不存在"
  }
}
```

**使用場景**:
- 用戶主動移除不再使用的設備
- 設備數量達到上限時，移除舊設備以添加新設備
- 設備遺失或更換時的清理操作

---

## 🎙️ **使用量管理 API**

### 6. 使用前檢查
**端點**: `POST https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/check_usage_before_recording`

**用途**: 在開始錄音前檢查用戶配額

**請求體**:
```javascript
{
  "device_id": "unique-device-id",
  "estimated_duration_seconds": 300  // 預估使用時長（秒）
}
```

**回應（可以使用）**:
```javascript
{
  "success": true,
  "data": {
    "can_use": true,
    "remaining_seconds": 5400,  // 剩餘配額（秒）
    "daily_limit": 10800,  // 每日限制（秒）
    "estimated_sessions_remaining": 18,  // 預估剩餘會話數
    "reset_time": "2025-01-28T00:00:00Z",  // 配額重置時間
    "subscription": {
      "plan": "PRO",
      "status": "active"
    }
  },
  "message": "配額檢查通過，可以使用服務"
}
```

**回應（配額不足）**:
```javascript
{
  "success": false,
  "data": {
    "can_use": false,
    "remaining_seconds": 0,
    "reason": "超過每日限制",
    "upgrade_required": true,
    "upgrade_url": "https://portal.speakonai.com/upgrade?plan=PRO",
    "daily_limit": 600,
    "current_plan": "FREE",
    "suggested_plans": [
      {
        "plan": "STARTER",
        "daily_limit": 3600,
        "price": "$9.99/月"
      },
      {
        "plan": "PRO",
        "daily_limit": 10800,
        "price": "$19.99/月"
      }
    ]
  },
  "message": "配額不足，需要升級計劃"
}
```

### 7. 提交使用記錄
**端點**: `POST https://asia-east1-speakoneai-dev-9f995.cloudfunctions.net/submit_usage`

**用途**: 提交實際使用時長記錄

**請求體**:
```javascript
{
  "duration_seconds": 300,  // 實際使用時長（秒）
  "feature_type": "ai-speech-to-text",  // 功能類型: ai-speech-to-text, direct-speech-to-text
  "device_id": "unique-device-id",
  "platform": "windows",
  "token_usage": {  // OpenAI token 使用量 (可選)
    // Chat Completion API (ai-mode)
    "prompt_tokens": 10,
    "completion_tokens": 20,
    "total_tokens": 30,  // 可選，後端會自動計算

    // Realtime API (direct mode)
    "input_tokens": 15,
    "output_tokens": 25
  },
  "session_metadata": {  // 可選的會話元數據
    "audio_quality": "high",
    "language": "zh-TW",
    "file_size_mb": 15.2
  }
}
```

**Token 使用量說明**:
- `total_tokens` 欄位為可選，後端會自動計算所有 token 類型的總和
- 如果前端提供 `total_tokens`，後端會忽略並重新計算以確保準確性
- 支援 Chat Completion API 和 Realtime API 的不同 token 類型

**回應**:
```javascript
{
  "success": true,
  "data": {
    "usage_log_id": "user123_device456_ai-speech-to-text_2025-01-27", // 複合ID
    "duration_recorded": 300,
    // Token 使用量 (頂層欄位，本次提交的數量)
    "prompt_tokens": 10,      // 本次提交的 prompt tokens
    "completion_tokens": 20,  // 本次提交的 completion tokens
    "input_tokens": 15,       // 本次提交的 input tokens (Realtime API)
    "output_tokens": 25,      // 本次提交的 output tokens (Realtime API)
    "total_tokens": 70,       // 本次提交的所有 token 總和 (後端計算)
    // 只信任 subscription 表的欄位
    "current_day_used_seconds": 3900,  // 來自 subscription 表
    "daily_limit_seconds": 10800       // 來自 subscription 表
  },
  "message": "使用量記錄成功：300 秒"
}
```

**重要說明**:
- `total_tokens` 由後端自動計算：`prompt_tokens + completion_tokens + input_tokens + output_tokens`
- 回應中的 token 數量為本次提交的數量，不是累加總數
- 如需查看累加總數，請使用 `get_user_dashboard` API

**客戶端使用量計算**:
```javascript
// 從回應中獲取可信任的欄位
const currentUsed = response.data.current_day_used_seconds;
const dailyLimit = response.data.daily_limit_seconds;

// 客戶端計算剩餘量和使用率
const remaining = dailyLimit === -1 ? -1 : Math.max(0, dailyLimit - currentUsed);
const canUse = remaining > 0 || dailyLimit === -1;
const usagePercentage = dailyLimit > 0 ? (currentUsed / dailyLimit * 100) : 0;

console.log({
  used: currentUsed,
  limit: dailyLimit,
  remaining: remaining,
  canUse: canUse,
  percentage: usagePercentage.toFixed(1) + '%'
});
```

**錯誤回應（設備未授權）**:
```javascript
{
  "success": false,
  "error": {
    "code": "DEVICE_NOT_AUTHORIZED",
    "message": "設備未授權使用服務",
    "details": {
      "device_id": "unique-device-id",
      "device_status": "inactive",
      "action_required": "REGISTER_DEVICE"
    }
  }
}
```

---

## 🔄 **業務流程 (V2.3 更新)**

### 新用戶 Onboarding
1. **`create_or_update_user`** - 創建用戶和 FREE 訂閱
2. **`validate_or_register_device`** - 統一設備驗證/註冊 (新)
3. **`get_user_dashboard`** - 獲取完整用戶資訊 (新)
4. 開始使用服務

### 現有用戶登入
1. **`create_or_update_user`** - 更新用戶資訊
2. **`validate_or_register_device`** - 統一設備處理 (新)
3. **`get_user_dashboard`** - 獲取最新狀態 (新)


### 設備數量超限處理
當 `validate_or_register_device` 返回 `code: -1` 時：
- **選項 A**: 使用 `remove_device` 移除舊設備，然後重新註冊新設備 🆕
- **選項 B**: 升級訂閱計劃
- **選項 C**: 繼續使用現有設備

### 設備管理流程 🆕
1. **查看設備列表**: `get_user_dashboard` - 獲取所有設備
2. **移除設備**: `remove_device` - 移除不需要的設備
3. **添加新設備**: `validate_or_register_device` - 註冊新設備

### 使用服務流程
1. **`check_usage_before_recording`** - 檢查配額
2. 執行語音轉文字服務
3. **`submit_usage`** - 記錄實際使用量

### 訂閱升級流程 (新)
1. 前端收集 Stripe 支付資訊 (只限WEBSITE TEAM用, DESKTOP CLIENT APP TEAM PLEASE IGNORE)
2. **`create_subscription`** - 創建付費訂閱 (包含完整 Stripe 資料)
3. **`get_user_dashboard`** - 驗證訂閱更新

---

## 📊 **訂閱計劃 (V2.3 更新)**

| 計劃 | 每日限制 | 設備數量 | 價格/月 | 支援平台 |
|------|----------|----------|---------|----------|
| **FREE** | 10 分鐘 | 1 台 | $0 | iOS, Android |
| **STARTER** | 1 小時 | 1 台 | $9.99 | iOS, Android |
| **PRO** | 3 小時 | 2 台 | $19.99 | 全平台 |
| **PREMIUM** | 8 小時 | 5 台 | $59.99 | 全平台 |
| **MAX** | 無限制 | 無限制 | $129.99 | 全平台 |

**重要變更**:
- ❌ **移除月度限制**: 系統只使用每日限制
- ✅ **平台限制**: 不同計劃支援不同平台
- ✅ **時區支援**: 配額重置時間根據用戶時區調整

---

## 🚫 **已移除的 API**

以下 API 已在 V2.3 中移除，請使用新的統一 API：

### 用戶資訊相關
- ❌ `get_user_info` → 使用 `get_user_dashboard`
- ❌ `check_user_subscription_status` → 使用 `get_user_dashboard`

### 設備管理相關
- ❌ `register_device_v2` → 使用 `validate_or_register_device`
- ❌ `validate_device` → 使用 `validate_or_register_device`
- ❌ `get_devices_info` → 使用 `get_user_dashboard`

### 管理員 API
- ❌ 所有管理員 API 已移除（不對外暴露）

---

## 📈 **遷移指南**

### 前端適配步驟

1. **更新用戶儀表板頁面**:
   ```javascript
   // 舊方式 - 多次調用
   const userInfo = await getUserInfo();
   const subscriptionStatus = await checkUserSubscriptionStatus();
   const devicesInfo = await getDevicesInfo();
   
   // 新方式 - 單次調用
   const dashboard = await getUserDashboard();
   ```

2. **更新設備管理邏輯**:
   ```javascript
   // 舊方式 - 複雜流程
   try {
     const validateResult = await validateDevice(deviceData);
   } catch (error) {
     const registerResult = await registerDeviceV2(deviceData);
   }
   
   // 新方式 - 統一處理
   const result = await validateOrRegisterDevice(deviceData);
   switch (result.code) {
     case 1: case 2: // 成功
       proceedWithApp();
       break;
     case -1: // 需要移除設備
       showDeviceManagement();
       break;
   }
   ```

3. **更新訂閱創建**:
   ```javascript
   // 新方式 - 包含完整 Stripe 資訊
   const subscription = await createSubscription({
     plan: 'PRO',
     daily_limit_seconds: 10800,
     max_devices: 2,
     // ... Stripe 支付資訊
   });
   ```

**所有新 API 已部署並測試完成，可以開始前端適配工作！** 🚀
