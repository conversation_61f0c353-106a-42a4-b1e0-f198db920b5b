"""
統一設備驗證和註冊功能
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional
from firebase_functions import https_fn
from firebase_admin import firestore

from ..utils.validation import validate_auth, validate_device_id, validate_platform, normalize_platform
from ..utils.constants import SUBSCRIPTION_PLANS, ERROR_MESSAGES, DeviceStatus
from ..utils.environment import get_db

logger = logging.getLogger(__name__)

def validate_or_register_device_handler(req: https_fn.CallableRequest) -> Dict[str, Any]:
    """
    統一設備驗證和註冊處理器
    
    邏輯流程:
    1. 檢查用戶訂閱和設備限制
    2. 檢查當前設備是否在設備表中
    3. 如果設備存在且有效 -> 返回 code: 1 (已驗證)
    4. 如果設備不存在但在限制內 -> 註冊設備，返回 code: 2 (已註冊)
    5. 如果超過設備限制 -> 返回 code: -1 (需要移除設備)
    
    Args:
        req: Firebase Functions 請求對象
        req.data: {
            "device_id": "unique-device-id",
            "device_name": "My Device",
            "platform": "windows",
            "app_version": "1.0.0",
            "device_info": {
                "os_version": "10.0.26100",
                "fingerprint": "device-fingerprint"
            }
        }
        
    Returns:
        Dict[str, Any]: 驗證/註冊結果
        {
            "success": true,
            "code": 1,  // 1: 已驗證, 2: 已註冊, -1: 需要移除設備
            "message": "設備驗證成功",
            "data": {
                "device": {...},
                "subscription": {...},
                "devices_info": {...}
            }
        }
    """
    try:
        # 驗證用戶認證
        if not req.auth:
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.UNAUTHENTICATED,
                message='用戶未認證'
            )

        user_id = req.auth.uid
        data = req.data or {}
        
        # 驗證必要參數
        device_id = data.get('device_id')
        if not device_id or not validate_device_id(device_id):
            raise https_fn.HttpsError(
                code=https_fn.FunctionsErrorCode.INVALID_ARGUMENT,
                message='無效的設備ID'
            )
        
        platform = normalize_platform(data.get('platform', 'unknown'))
        device_name = data.get('device_name', f'{platform.title()} Device')
        app_version = data.get('app_version', '1.0.0')
        device_info = data.get('device_info', {})
        
        logger.info(f"處理設備驗證/註冊: user={user_id}, device={device_id}")
        
        db = get_db()
        
        # 1. 獲取用戶訂閱資訊
        subscription_info = get_user_subscription_info(db, user_id)
        if not subscription_info:
            return {
                'success': False,
                'code': -2,
                'message': '用戶沒有有效訂閱',
                'reason': 'NO_SUBSCRIPTION',
                'action_required': 'CREATE_SUBSCRIPTION'
            }
        
        plan = subscription_info['plan']
        plan_config = SUBSCRIPTION_PLANS.get(plan, SUBSCRIPTION_PLANS['FREE'])
        max_devices = plan_config['max_devices']
        
        # 2. 檢查平台支援
        supported_platforms = plan_config.get('supported_platforms', [])
        if platform not in supported_platforms:
            return {
                'success': False,
                'code': -3,
                'message': f'計劃 {plan} 不支援 {platform} 平台',
                'reason': 'PLATFORM_NOT_SUPPORTED',
                'action_required': 'UPGRADE_PLAN',
                'data': {
                    'current_plan': plan,
                    'supported_platforms': supported_platforms
                }
            }
        
        # 3. 獲取用戶當前設備列表
        current_devices = get_user_active_devices(db, user_id)
        current_device_count = len(current_devices)
        
        # 4. 檢查當前設備是否已存在
        current_device = None
        for device in current_devices:
            if device['device_id'] == device_id:
                current_device = device
                break
        
        # 5. 設備已存在 - 驗證邏輯
        if current_device:
            # 檢查設備狀態
            if (current_device.get('is_active', False) and 
                current_device.get('is_authorized', False) and
                current_device.get('status') == DeviceStatus.ACTIVE.value):
                
                # 更新設備最後活動時間
                update_device_activity(db, current_device['doc_ref'], app_version)
                
                # 設備操作記錄已移除
                
                return {
                    'success': True,
                    'code': 1,
                    'message': '設備驗證成功',
                    'data': {
                        'device': format_device_info(current_device),
                        'subscription': format_subscription_info(subscription_info),
                        'devices_info': {
                            'current_count': current_device_count,
                            'max_allowed': max_devices,
                            'can_add_more': current_device_count < max_devices if max_devices != -1 else True
                        }
                    }
                }
            else:
                # 設備存在但狀態無效
                return {
                    'success': False,
                    'code': -4,
                    'message': '設備狀態無效',
                    'reason': 'DEVICE_INVALID_STATUS',
                    'action_required': 'REREGISTER_DEVICE',
                    'data': {
                        'device_status': current_device.get('status'),
                        'is_active': current_device.get('is_active'),
                        'is_authorized': current_device.get('is_authorized')
                    }
                }
        
        # 6. 設備不存在 - 註冊邏輯
        else:
            # 檢查設備數量限制
            if max_devices != -1 and current_device_count >= max_devices:
                return {
                    'success': False,
                    'code': -1,
                    'message': f'已達到設備數量限制 ({current_device_count}/{max_devices})',
                    'reason': 'DEVICE_LIMIT_EXCEEDED',
                    'action_required': 'REMOVE_DEVICE',
                    'data': {
                        'current_devices': [format_device_info(d) for d in current_devices],
                        'current_count': current_device_count,
                        'max_allowed': max_devices,
                        'current_plan': plan
                    }
                }
            
            # 註冊新設備
            new_device = register_new_device(db, user_id, subscription_info['subscription_id'], {
                'device_id': device_id,
                'device_name': device_name,
                'platform': platform,
                'app_version': app_version,
                'device_info': device_info
            })
            
            # 同步設備列表到用戶和訂閱表
            sync_device_lists(db, user_id, subscription_info['subscription_id'])
            
            # 設備操作記錄已移除
            
            return {
                'success': True,
                'code': 2,
                'message': '設備註冊成功',
                'data': {
                    'device': format_device_info(new_device),
                    'subscription': format_subscription_info(subscription_info),
                    'devices_info': {
                        'current_count': current_device_count + 1,
                        'max_allowed': max_devices,
                        'can_add_more': (current_device_count + 1) < max_devices if max_devices != -1 else True
                    }
                }
            }
        
    except https_fn.HttpsError:
        raise
    except Exception as e:
        logger.error(f"設備驗證/註冊失敗: {user_id}, 錯誤: {str(e)}")
        raise https_fn.HttpsError(
            code=https_fn.FunctionsErrorCode.INTERNAL,
            message=f'設備驗證/註冊失敗: {str(e)}'
        )

def get_user_subscription_info(db, user_id: str) -> Optional[Dict[str, Any]]:
    """獲取用戶訂閱資訊"""
    try:
        # 從用戶表獲取訂閱ID
        user_doc = db.collection('users').document(user_id).get()
        if not user_doc.exists:
            return None
        
        user_data = user_doc.to_dict()
        subscription_id = user_data.get('current_subscription_id')
        
        if not subscription_id:
            return None
        
        # 從訂閱表獲取詳細資訊
        subscription_doc = db.collection('subscriptions').document(subscription_id).get()
        if not subscription_doc.exists:
            return None
        
        subscription_data = subscription_doc.to_dict()
        subscription_data['subscription_id'] = subscription_id
        
        return subscription_data
        
    except Exception as e:
        logger.error(f"獲取用戶訂閱資訊失敗: {user_id}, 錯誤: {str(e)}")
        return None

def get_user_active_devices(db, user_id: str) -> list:
    """獲取用戶活躍設備列表"""
    try:
        devices = []
        devices_query = db.collection('devices').where('user_id', '==', user_id).where('is_active', '==', True)
        
        for doc in devices_query.stream():
            device_data = doc.to_dict()
            device_data['doc_ref'] = doc.reference
            device_data['doc_id'] = doc.id
            devices.append(device_data)
        
        return devices
        
    except Exception as e:
        logger.error(f"獲取用戶設備失敗: {user_id}, 錯誤: {str(e)}")
        return []

def register_new_device(db, user_id: str, subscription_id: str, device_data: Dict[str, Any]) -> Dict[str, Any]:
    """註冊新設備"""
    now = datetime.utcnow()
    
    device_record = {
        'user_id': user_id,
        'subscription_id': subscription_id,
        'device_id': device_data['device_id'],
        'device_name': device_data['device_name'],
        'platform': device_data['platform'],
        'app_version': device_data['app_version'],
        'os_version': device_data.get('device_info', {}).get('os_version', 'unknown'),
        'device_fingerprint': device_data.get('device_info', {}).get('fingerprint', ''),
        'is_active': True,
        'is_authorized': True,
        'status': DeviceStatus.ACTIVE.value,
        'created_at': now,
        'updated_at': now,
        'last_active_at': now,
        'user_agent': device_data.get('user_agent', 'unknown')
    }
    
    device_ref = db.collection('devices').document()
    device_ref.set(device_record)
    
    device_record['doc_ref'] = device_ref
    device_record['doc_id'] = device_ref.id
    
    return device_record

def update_device_activity(db, device_ref, app_version: str):
    """更新設備活動時間"""
    device_ref.update({
        'last_active_at': datetime.utcnow(),
        'app_version': app_version,
        'updated_at': datetime.utcnow()
    })

def sync_device_lists(db, user_id: str, subscription_id: str):
    """同步設備列表到用戶和訂閱表"""
    try:
        # 獲取活躍設備ID列表
        active_devices = get_user_active_devices(db, user_id)
        device_ids = [device['device_id'] for device in active_devices]
        
        now = datetime.utcnow()
        
        # 更新用戶表
        user_ref = db.collection('users').document(user_id)
        user_ref.update({
            'device_ids': device_ids,
            'last_device_check': now,
            'updated_at': now
        })
        
        # 更新訂閱表
        subscription_ref = db.collection('subscriptions').document(subscription_id)
        subscription_ref.update({
            'active_device_ids': device_ids,
            'last_device_limit_check': now,
            'updated_at': now
        })
        
    except Exception as e:
        logger.error(f"同步設備列表失敗: {user_id}, 錯誤: {str(e)}")

# device_action_logs 功能已移除

def format_device_info(device: Dict[str, Any]) -> Dict[str, Any]:
    """格式化設備資訊"""
    return {
        'device_id': device.get('device_id'),
        'device_name': device.get('device_name'),
        'platform': device.get('platform'),
        'status': device.get('status'),
        'is_active': device.get('is_active'),
        'is_authorized': device.get('is_authorized'),
        'last_active_at': device.get('last_active_at'),
        'created_at': device.get('created_at'),
        'app_version': device.get('app_version')
    }

def format_subscription_info(subscription: Dict[str, Any]) -> Dict[str, Any]:
    """格式化訂閱資訊"""
    return {
        'plan': subscription.get('plan'),
        'status': subscription.get('status'),
        'daily_limit_seconds': subscription.get('daily_limit_seconds'),
        'current_day_used_seconds': subscription.get('current_day_used_seconds'),
        'max_devices': subscription.get('max_devices')
    }
